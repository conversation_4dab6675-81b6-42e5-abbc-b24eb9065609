# Refactoring Plan: Implement Firebase Streams for Real-time Updates

## Overview
This plan implements Firebase streams for real-time updates while preserving static data loading for alumno information. The repository implementations are already properly set up with stream methods, so we'll focus on:
1. Updating the controller to utilize existing streams
2. Refactoring the UI to use StreamBuilders
3. Optimizing data handling

## Implementation Steps

### 1. Update DetalleAlumnoController
```dart
// lib/app/presentation/alumno_detalle/controller/detalle_alumno.controller.dart
class DetalleAlumnoController extends GetxController {
  // Remove Rx variables for stream data
  Kardex? kardex;
  List<KardexSemana> semanas = [];
  List<CostoAdicional> costosAdicionales = [];

  // Add stream subscriptions
  late StreamSubscription<Kardex?> _kardexSub;
  late StreamSubscription<List<KardexSemana>> _semanasSub;
  late StreamSubscription<List<CostoAdicional>> _costosSub;

  Future<void> loadAlumnoData(String alumnoId) async {
    // Load static alumno data
    alumno.value = await _alumnoRepo.obtenerAlumnoPorId(alumnoId);
    
    // Initialize streams using existing repository methods
    _kardexSub = _kardexRepo.watchKardex(alumnoId).listen((k) {
      kardex = k;
      update();
    });
    
    _semanasSub = _kardexRepo.watchSemanas(alumnoId).listen((s) {
      semanas = s;
      update();
    });
    
    _costosSub = _kardexRepo.watchCostosAdicionales(alumnoId).listen((c) {
      costosAdicionales = c;
      update();
    });
  }

  @override
  void onClose() {
    _kardexSub.cancel();
    _semanasSub.cancel();
    _costosSub.cancel();
    super.onClose();
  }
}
```

### 2. Refactor UI with StreamBuilders
```dart
// lib/app/presentation/alumno_detalle/views/info_tab/info_alumno.dart
class StudentInfoCard extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        // Alumno header (static)
        StudentHeader(alumno: controller.alumno.value),
        
        // Kardex stream
        StreamBuilder<Kardex?>(
          stream: controller.kardexStream,
          builder: (context, snapshot) {
            if (snapshot.connectionState == ConnectionState.waiting) {
              return CircularProgressIndicator();
            }
            if (snapshot.hasError) return Text('Error: ${snapshot.error}');
            return KardexSection(kardex: snapshot.data!);
          }
        ),
        
        // Semanas stream
        StreamBuilder<List<KardexSemana>>(
          stream: controller.semanasStream,
          builder: (context, snapshot) {
            if (snapshot.connectionState == ConnectionState.waiting) {
              return CircularProgressIndicator();
            }
            if (snapshot.hasError) return Text('Error: ${snapshot.error}');
            // Build UI with semanas data
          }
        ),
        
        // Costos stream
        StreamBuilder<List<CostoAdicional>>(
          stream: controller.costosStream,
          builder: (context, snapshot) {
            if (snapshot.connectionState == ConnectionState.waiting) {
              return CircularProgressIndicator();
            }
            if (snapshot.hasError) return Text('Error: ${snapshot.error}');
            return Column(
              children: snapshot.data!
                  .map((costo) => CostItem(costo: costo))
                  .toList(),
            );
          }
        )
      ]
    );
  }
}
```

### 3. Simplify DetalleAlumnoUseCase
```dart
// lib/app/domain/casos_de_uso/detalle_alumno_usecase.dart
class DetalleAlumnoUseCase {
  Future<Alumno?> getAlumno(String alumnoId) async {
    return await _alumnoRepo.obtenerAlumnoPorId(alumnoId);
  }

  // Remove cached DTO logic
}
```

### 4. Optimize Performance
- Add `distinct()` to streams to prevent unnecessary rebuilds
- Implement error handling in stream subscriptions
- Use `StreamBuilder`'s `initialData` for smoother transitions

### Implementation Sequence
1. Update controller to use repository streams
2. Refactor UI to replace Obx with StreamBuilders
3. Simplify use case to only handle static data
4. Test real-time updates with Firebase emulator
5. Remove obsolete DTO and caching logic

## Estimated Time: 2-3 hours