# Plan de Refactorización - Módulo de Detalle de Alumno

## 1. Problemas Identificados
### 1.1 Gestión de Datos
- **Waterfall loading**: Llamadas secuenciales a Firestore (alumno → kardex → semanas → pagos → costos)
- **Falta de manejo de errores parciales**: Un error en cualquier llamada falla toda la carga
- **Caché incompleta**: Implementación de session storage comentada

### 1.2 Reactividad
- Estado monolítico (`DetalleAlumnoState`) en lugar de observables granulares
- Solo `detallesPagos` es reactivo, los demás datos no
- Actualizaciones de estado sin debouncing

### 1.3 Arquitectura
- Lógica de agregación en el controlador
- Repositorios devuelven entidades directamente al controlador
- Ausencia de DTOs para transferencia de datos optimizada

## 2. Propuesta de Mejoras

### 2.1 Optimización de Carga de Datos
```mermaid
graph TD
    A[loadAlumnoData] --> B[Future.wait]
    B --> C[AlumnoRepo]
    B --> D[KardexRepo]
    B --> E[ReciboRepo]
    C --> F[Alumno]
    D --> G[Kardex]
    E --> H[Pagos]
    F --> I[Estado]
    G --> I[Estado]
    H --> I[Estado]
```

Implementación:
```dart
Future<void> loadAlumnoData(String alumnoId) async {
  state.value = DetalleAlumnoLoading();
  
  try {
    final results = await Future.wait([
      _alumnoRepo.obtenerAlumnoPorId(alumnoId),
      _kardexRepo.getKardexByAlumnoId(alumnoId),
      _kardexRepo.getSemanas(alumnoId),
      _kardexRepo.getDetallesPago(alumnoId),
      _kardexRepo.getCostosAdicionales(alumnoId),
    ], eagerError: true);

    final loadedState = DetalleAlumnoLoaded(
      alumno: results[0] as Alumno,
      kardex: results[1] as Kardex,
      semanas: results[2] as List<Semana>,
      detallesPago: results[3] as List<DetallePagoUnificado>,
      costosAdicionales: results[4] as List<CostoAdicional>,
    );

    state.value = loadedState;
    _updateCache(alumnoId, loadedState);
  } catch (e) {
    state.value = DetalleAlumnoError('Error: ${e.toString()}');
  }
}
```

### 2.2 Reactividad Mejorada con GetX
```dart
class DetalleAlumnoController extends GetxController {
  // Observables individuales
  final alumno = Rxn<Alumno>();
  final kardex = Rxn<Kardex>();
  final semanas = RxList<Semana>();
  final detallesPagos = RxList<DetallePagoUnificado>();
  final costosAdicionales = RxList<CostoAdicional>();
  final error = RxString('');

  // Worker para reaccionar a cambios
  final debounce = Debounce(duration: 300.ms);

  void _updateState() {
    debounce.call(() {
      if (alumno.value != null && 
          kardex.value != null) {
        state.value = DetalleAlumnoLoaded(...);
      }
    });
  }

  ever(alumno, (_) => _updateState());
  ever(kardex, (_) => _updateState());
  // ... otros listeners
}
```

### 2.3 Refactorización Arquitectónica
```mermaid
graph LR
    A[UI] --> B[Controller]
    B --> C[UseCase]
    C --> D[AlumnoRepo]
    C --> E[KardexRepo]
    C --> F[CacheRepo]
    D --> G[Firestore]
    E --> G
    F --> H[Local Storage]
```

Nuevos componentes:
1. **DetalleAlumnoUseCase**:
```dart
class DetalleAlumnoUseCase {
  Future<DetalleAlumnoData> execute(String alumnoId) async {
    // Lógica de agregación y caché
  }
}
```

2. **DetalleAlumnoDTO**:
```dart
class DetalleAlumnoDTO {
  final Alumno alumno;
  final Kardex kardex;
  final List<Semana> semanas;
  // ...
}
```

### 2.4 Estrategia de Caché
```dart
class AlumnoCache {
  static final _cache = <String, DetalleAlumnoData>{};
  static const _cacheDuration = Duration(minutes: 15);

  static DetalleAlumnoData? get(String alumnoId) {
    final data = _cache[alumnoId];
    if (data != null && 
        DateTime.now().difference(data.timestamp) < _cacheDuration) {
      return data;
    }
    return null;
  }

  static void set(String alumnoId, DetalleAlumnoData data) {
    _cache[alumnoId] = data.copyWith(timestamp: DateTime.now());
  }
}
```

## 3. Plan de Implementación

1. **Fase 1: Refactor de repositorios**
   - Añadir métodos de caché en repositorios
   - Crear DTOs de transferencia

2. **Fase 2: Implementar use case**
   - Mover lógica de agregación desde controller
   - Implementar estrategia de caché unificada

3. **Fase 3: Refactor del controlador**
   - Adoptar observables granulares
   - Implementar debouncing
   - Conectar con use case

4. **Fase 4: Optimizaciones finales**
   - Add lazy loading para pagos históricos
   - Implementar refresh estratégico
   - Añadir analytics para monitoreo

¿Le gustaría que implementemos estos cambios? Puedo cambiar al modo de código para comenzar la refactorización.