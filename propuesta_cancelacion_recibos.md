# Estructura de Archivos Actualizada

## Estructura de Carpetas
```
lib/
├── app/
│   ├── data/
│   │   └── repositories/
│   │       ├── recibo_repository_impl.dart      # Implementación de repositorio de recibos
│   │       └── kardex_repository_impl.dart      # Implementación de repositorio de kardex
│   ├── domain/
│   │   ├── entities/
│   │   │   ├── recibo.dart                     # Entidad Recibo (actualizada)
│   │   │   └── kardex.dart                     # Entidad Kardex (actualizada)
│   │   ├── repositories/
│   │   │   ├── recibo_repository.dart          # Interfaz de repositorio de recibos
│   │   │   └── kardex_repository.dart          # Interfaz de repositorio de kardex
│   │   └── casos_de_uso/
│   │       └── cancelar_recibo_usecase.dart    # Caso de uso de cancelación
│   ├── presentation/
│   │   └── detalle_recibo/
│   │       ├── controller/
│   │       │   └── detalle_recibo.controller.dart # Controlador actualizado
│   │       └── detalle_recibo.page.dart          # Vista con botón de cancelación
│   └── ...
├── injection.dart                              # Configuración de inyección de dependencias
└── main.dart
```

## Correcciones en Repositorios

### 1. Kardex Repository (solo operaciones de kardex)
**`lib/app/domain/repositories/kardex_repository.dart`**:
```dart
abstract class KardexRepository {
  Future<Kardex> getKardexById(String id);
  Future<void> updateKardex(Kardex kardex);
  Future<KardexSemana> getKardexSemanaById(String id);
  Future<void> updateKardexSemana(KardexSemana semana);
}
```

**`lib/app/data/repositories/kardex_repository_impl.dart`**:
```dart
class KardexRepositoryImpl implements KardexRepository {
  final FirebaseFirestore _firestore;

  KardexRepositoryImpl(this._firestore);

  @override
  Future<Kardex> getKardexById(String id) async {
    final doc = await _firestore.collection('kardex').doc(id).get();
    return Kardex.fromMap(doc.data()!);
  }

  @override
  Future<void> updateKardex(Kardex kardex) async {
    await _firestore.collection('kardex').doc(kardex.id).update(kardex.toMap());
  }

  @override
  Future<KardexSemana> getKardexSemanaById(String id) async {
    final doc = await _firestore.collection('kardex_semanas').doc(id).get();
    return KardexSemana.fromMap(doc.data()!);
  }

  @override
  Future<void> updateKardexSemana(KardexSemana semana) async {
    await _firestore.collection('kardex_semanas').doc(semana.id).update(semana.toMap());
  }
}
```

### 2. Recibo Repository (solo operaciones de recibos)
**`lib/app/domain/repositories/recibo_repository.dart`**:
```dart
abstract class ReciboRepository {
  Future<Recibo> getReciboById(String id);
  Future<void> updateRecibo(Recibo recibo);
}
```

**`lib/app/data/repositories/recibo_repository_impl.dart`**:
```dart
class ReciboRepositoryImpl implements ReciboRepository {
  final FirebaseFirestore _firestore;

  ReciboRepositoryImpl(this._firestore);

  @override
  Future<Recibo> getReciboById(String id) async {
    final doc = await _firestore.collection('recibos').doc(id).get();
    return Recibo.fromMap(doc.data()!);
  }

  @override
  Future<void> updateRecibo(Recibo recibo) async {
    await _firestore.collection('recibos').doc(recibo.id).update(recibo.toMap());
  }
}
```

## Mejoras en el Caso de Uso
**`lib/app/domain/casos_de_uso/cancelar_recibo_usecase.dart`**:
```dart
class CancelarReciboUseCase {
  final ReciboRepository _reciboRepo;
  final KardexRepository _kardexRepo;

  CancelarReciboUseCase(this._reciboRepo, this._kardexRepo);

  Future<void> execute(String reciboId) async {
    final recibo = await _reciboRepo.getReciboById(reciboId);
    
    if (recibo.estado == 'cancelado') {
      throw Exception('Recibo ya cancelado');
    }

    for (final detalle in recibo.detalles) {
      if (detalle.tipo == 'semana' && detalle.referenciaId != null) {
        await _revertirSemana(detalle, recibo.id);
      }
    }

    await _reciboRepo.updateRecibo(recibo.copyWith(estado: 'cancelado'));
  }

  Future<void> _revertirSemana(DetallePago detalle, String reciboId) async {
    final semana = await _kardexRepo.getKardexSemanaById(detalle.referenciaId!);
    
    if (semana.reciboId == reciboId) {
      final updatedSemana = semana.copyWith(
        pagada: false,
        montoPagado: semana.montoPagado - detalle.monto,
        reciboId: null,
      );
      
      await _kardexRepo.updateKardexSemana(updatedSemana);
      
      final kardex = await _kardexRepo.getKardexById(semana.kardexId);
      await _kardexRepo.updateKardex(kardex.copyWith(
        totalPagado: kardex.totalPagado - detalle.monto,
        saldoPendiente: kardex.saldoPendiente + detalle.monto,
      ));
    }
  }
}
```

## Inyección de Dependencias Actualizada
**`lib/injection.dart`**:
```dart
void initDependencies() {
  // Firestore instance
  final firestore = FirebaseFirestore.instance;
  
  // Repositorios
  Get.lazyPut<ReciboRepository>(() => ReciboRepositoryImpl(firestore));
  Get.lazyPut<KardexRepository>(() => KardexRepositoryImpl(firestore));
  
  // Casos de Uso
  Get.lazyPut(() => CancelarReciboUseCase(
    Get.find<ReciboRepository>(),
    Get.find<KardexRepository>(),
  ));
}
```

## Beneficios de la Estructura
1. **Separación clara de responsabilidades**:
   - Cada repositorio maneja solo su dominio
   - El caso de uso coordina la lógica entre repositorios
2. **Acoplamiento mínimo**:
   - Las implementaciones dependen de interfaces
   - Fácil de cambiar la fuente de datos
3. **Testeabilidad mejorada**:
   - Se pueden mockear repositorios individualmente
4. **Mantenimiento más sencillo**:
   - Cambios en un dominio no afectan a otros

¿Esta estructura se alinea con lo que necesita? ¿Está listo para implementar la solución?