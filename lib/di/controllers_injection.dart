import 'package:get_it/get_it.dart';
import 'package:gemini_app/app/domain/casos_de_uso/watch_semanas_usecase.dart';
import 'package:gemini_app/app/presentation/alumno_detalle/controller/detalle_alumno.controller.dart';
import 'package:gemini_app/app/domain/casos_de_uso/detalle_alumno_usecase.dart';
import 'package:gemini_app/app/domain/casos_de_uso/watch_alumno_usecase.dart';
import 'package:gemini_app/app/domain/casos_de_uso/watch_kardex_usecase.dart'; // Added new use case
import 'package:gemini_app/app/domain/casos_de_uso/watch_recibos_usecase.dart'; // Import the new use case
import 'package:gemini_app/app/domain/casos_de_uso/watch_costos_adicionales_usecase.dart';
import 'package:gemini_app/app/presentation/alumno_detalle/views/pagos_tab/controllers/payment_controller.dart';
import 'package:gemini_app/app/domain/repositories/kardex_repository.dart';
import 'package:gemini_app/app/domain/repositories/recibo_repository.dart';
import 'package:gemini_app/app/presentation/detalle_recibo/detalle_recibo.controller.dart';

void setupControllers(GetIt getIt) {
  // Controlador de detalle de alumno
  getIt.registerFactory<DetalleAlumnoController>(
    () => DetalleAlumnoController(
      getIt<DetalleAlumnoUseCase>(),
      getIt<WatchSemanasUseCase>(),
      getIt<WatchAlumnoUseCase>(),
      getIt<WatchKardexUseCase>(),
      getIt<WatchRecibosUseCase>(), // Added new dependency
      getIt<WatchCostosAdicionalesUseCase>(),
    ),
  );

  // Controlador de pagos
  getIt.registerFactory<PaymentController>(
    () => PaymentController(
      getIt<KardexRepository>(),
      getIt<ReciboRepository>(),
    ),
  );

  // Controlador de detalle de recibo
  getIt.registerFactory<DetalleReciboController>(
    () => DetalleReciboController(),
  );
}