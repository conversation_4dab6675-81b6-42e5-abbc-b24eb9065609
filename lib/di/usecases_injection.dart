import 'package:gemini_app/app/presentation/lista_alumnos/casos_de_uso/obtener_alumnos_paginados.caso_uso.dart';
import 'package:get_it/get_it.dart';
import 'package:gemini_app/app/presentation/login/casos_de_uso/sign_in_usecase.dart';
import 'package:gemini_app/app/domain/casos_de_uso/detalle_alumno_usecase.dart';
import 'package:gemini_app/app/domain/casos_de_uso/cancelar_recibo_usecase.dart';
import 'package:gemini_app/app/domain/casos_de_uso/watch_semanas_usecase.dart';
import 'package:gemini_app/app/domain/casos_de_uso/watch_alumno_usecase.dart';
import 'package:gemini_app/app/domain/casos_de_uso/watch_kardex_usecase.dart'; // Added import
import 'package:gemini_app/app/domain/casos_de_uso/watch_recibos_usecase.dart';
import 'package:gemini_app/app/domain/casos_de_uso/watch_costos_adicionales_usecase.dart';
import 'package:gemini_app/app/domain/repositories/auth_repository.dart';
import 'package:gemini_app/app/domain/repositories/alumno_repositorio.dart';
import 'package:gemini_app/app/domain/repositories/kardex_repository.dart';
import 'package:gemini_app/app/domain/repositories/recibo_repository.dart';

void setupUseCases(GetIt getIt) {
  getIt.registerLazySingleton(() => SignInUseCase(getIt<AuthRepository>()));
  getIt.registerLazySingleton(
    () => DetalleAlumnoUseCase(
      getIt<AlumnoRepository>(),
      getIt<KardexRepository>(),
      getIt<ReciboRepository>(),
    ),
  );
  getIt.registerLazySingleton(
    () => CancelarReciboUseCase(
      getIt<ReciboRepository>(),
      getIt<KardexRepository>(),
    ),
  );
  getIt.registerLazySingleton(
    () => StreamAlumnosPorBusquedaCasoUso(getIt<AlumnoRepository>()),
  );
  getIt.registerLazySingleton(
    () => WatchSemanasUseCase(getIt<KardexRepository>()),
  );
  getIt.registerLazySingleton(
    () => WatchAlumnoUseCase(getIt<AlumnoRepository>()),
  );
  getIt.registerLazySingleton(
    () => WatchKardexUseCase(getIt<KardexRepository>()), // Added registration
  );
  getIt.registerLazySingleton(
    () => WatchRecibosUseCase(getIt<ReciboRepository>()),
  );
  getIt.registerLazySingleton(
    () => WatchCostosAdicionalesUseCase(getIt<KardexRepository>()),
  );
}