import 'package:get_it/get_it.dart';
import 'package:gemini_app/app/data/repositories/auth_repository_impl.dart';
import 'package:gemini_app/app/data/repositories/alumno_repository_impl.dart';
import 'package:gemini_app/app/data/repositories/kardex_repository_impl.dart';
import 'package:gemini_app/app/data/repositories/recibo_repository_impl.dart';
import 'package:gemini_app/app/domain/repositories/auth_repository.dart';
import 'package:gemini_app/app/domain/repositories/alumno_repositorio.dart';
import 'package:gemini_app/app/domain/repositories/kardex_repository.dart';
import 'package:gemini_app/app/domain/repositories/recibo_repository.dart';
import 'package:gemini_app/app/servicios/database_service.dart';

void setupRepositories(GetIt getIt) {
  getIt.registerLazySingleton<AuthRepository>(() => AuthRepositoryImpl());
  getIt.registerLazySingleton<AlumnoRepository>(() => AlumnosRepositoryImpl(getIt<DatabaseService>()));
  
  getIt.registerLazySingleton<KardexRepository>(
    () => KardexRepositoryImpl(getIt<DatabaseService>()),
  );
  getIt.registerLazySingleton<ReciboRepository>(
    () => ReciboRepositoryImpl(
      getIt<DatabaseService>(),
      getIt<KardexRepository>(),
    ),
  );
}