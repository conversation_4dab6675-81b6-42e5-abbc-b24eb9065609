// File generated by FlutterFire CLI.
// Contains configuration for Firebase.
// Should not be checked into version control.

import 'package:firebase_core/firebase_core.dart' show FirebaseOptions;
import 'package:flutter/foundation.dart'
    show defaultTargetPlatform, kIsWeb, TargetPlatform;

/// Default [FirebaseOptions] for use with your Firebase apps.
class DefaultFirebaseOptions {
  static FirebaseOptions get currentPlatform {
    if (kIsWeb) {
      return web;
    }
    switch (defaultTargetPlatform) {
      case TargetPlatform.android:
        return android;
      case TargetPlatform.iOS:
        return ios;
      case TargetPlatform.macOS:
        return macos;
      case TargetPlatform.windows:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for windows - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      case TargetPlatform.linux:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for linux - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      default:
        throw UnsupportedError(
          'DefaultFirebaseOptions are not supported for this platform.',
        );
    }
  }

  static const FirebaseOptions web = FirebaseOptions(
      apiKey: "AIzaSyCTVvSNXDG9NI0U86eLwYiFdzrnOFnDhXY",
      authDomain: "instituto-mexicano-tepeaca.firebaseapp.com",
      projectId: "instituto-mexicano-tepeaca",
      storageBucket: "instituto-mexicano-tepeaca.firebasestorage.app",
      messagingSenderId: "727170514205",
      appId: "1:727170514205:web:99e80124e6165fc12975f4",
      measurementId: "G-HSBQCWZKMB"
  );

  static const FirebaseOptions android = FirebaseOptions(
      apiKey: "AIzaSyCTVvSNXDG9NI0U86eLwYiFdzrnOFnDhXY",
      authDomain: "instituto-mexicano-tepeaca.firebaseapp.com",
      projectId: "instituto-mexicano-tepeaca",
      storageBucket: "instituto-mexicano-tepeaca.firebasestorage.app",
      messagingSenderId: "727170514205",
      appId: "1:727170514205:web:99e80124e6165fc12975f4",
      measurementId: "G-HSBQCWZKMB"
  );

  static const FirebaseOptions ios = FirebaseOptions(
      apiKey: "AIzaSyCTVvSNXDG9NI0U86eLwYiFdzrnOFnDhXY",
      authDomain: "instituto-mexicano-tepeaca.firebaseapp.com",
      projectId: "instituto-mexicano-tepeaca",
      storageBucket: "instituto-mexicano-tepeaca.firebasestorage.app",
      messagingSenderId: "727170514205",
      appId: "1:727170514205:web:99e80124e6165fc12975f4",
      measurementId: "G-HSBQCWZKMB"
  );

  static const FirebaseOptions macos = FirebaseOptions(
      apiKey: "AIzaSyCTVvSNXDG9NI0U86eLwYiFdzrnOFnDhXY",
      authDomain: "instituto-mexicano-tepeaca.firebaseapp.com",
      projectId: "instituto-mexicano-tepeaca",
      storageBucket: "instituto-mexicano-tepeaca.firebasestorage.app",
      messagingSenderId: "727170514205",
      appId: "1:727170514205:web:99e80124e6165fc12975f4",
      measurementId: "G-HSBQCWZKMB"
  );
}