import 'package:beamer/beamer.dart';
import 'package:firebase_core/firebase_core.dart';
import 'package:flutter/material.dart';
import 'package:gemini_app/app/presentation/panel_admin/panel_administrativo.pagina.dart';
import 'package:gemini_app/app/presentation/login/login.page.dart';
import 'package:gemini_app/app/routes/beamer_locations.dart';
import 'package:provider/provider.dart';
import 'package:responsive_framework/responsive_framework.dart';

import 'app/presentation/instituto/vistas/estilismo_diseno.dart';
import 'app/presentation/instituto/instituto.page.dart';
import 'app/presentation/instituto/vistas/tecnico_computacion.dart';
import 'app/providers/auth_provider.dart';
import 'firebase_options.dart';
import 'di/injection.dart';
import 'package:gemini_app/app/domain/modelos/recibo.dart';
import 'package:gemini_app/app/presentation/detalle_recibo/detalle_recibo.page.dart';


void main() async {
  WidgetsFlutterBinding.ensureInitialized();
  try {
    await Firebase.initializeApp(
      options: DefaultFirebaseOptions.currentPlatform,
    );
  } catch (e) {
    // Manejo de errores durante la inicialización de Firebase
    print('Error al inicializar Firebase: $e');
  }

  // Configuración de la inyección de dependencias
  setupDependencies();

  runApp(
    MultiProvider(
      providers: [
        ChangeNotifierProvider(create: (context) => ProveedorAutenticacion()),
      ],
      child: MiAplicacion(),
    ),
  );
}

class MiAplicacion extends StatelessWidget {
  final routerDelegate = BeamerDelegate(
    locationBuilder: BeamerLocationBuilder(
      beamLocations: [
        HomeLocation(),
        LoginLocation(),
        AdminLocation(),
        TecnicoComputacionLocation(),
        EstilismoLocation(),
        NotFoundLocation(),
      ],
    ),
    notFoundPage: const BeamPage(
      child: Scaffold(body: Center(child: Text('Página no encontrada'))),
    ),
  );

  @override
  Widget build(BuildContext context) {
    return MaterialApp.router(
      title: 'Instituto Educativo',
      theme: ThemeData(
        primarySwatch: Colors.blue,
        visualDensity: VisualDensity.adaptivePlatformDensity,
      ),
      builder:
          (context, child) => ResponsiveBreakpoints.builder(
            child: child!,
            breakpoints: [
              const Breakpoint(start: 0, end: 450, name: MOBILE),
              const Breakpoint(start: 451, end: 800, name: TABLET),
              const Breakpoint(start: 801, end: 1920, name: DESKTOP),
              const Breakpoint(start: 1921, end: double.infinity, name: '4K'),
            ],
          ),
      routeInformationParser: BeamerParser(),
      routerDelegate: routerDelegate,
    );
  }
}
