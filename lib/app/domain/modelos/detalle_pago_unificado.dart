// lib/app/modelos/detalle_pago_unificado.dart
import 'package:cloud_firestore/cloud_firestore.dart';

class DetallePagoUnificado {
  final String id;
  final String concepto;
  final String tipo; // 'semana' o 'costo_adicional'
  final String? referenciaId;
  final int? numeroSemana;
  final double monto;
  final DateTime fecha;
  final String estado; // 'activo' o 'cancelado'

  // Campos opcionales para contexto de Kardex
  final String? alumnoId;
  final String? reciboId;
  final DateTime? createdAt;
  final DateTime? updatedAt;

  DetallePagoUnificado({
    String? id,
    required this.concepto,
    required this.tipo,
    this.referenciaId,
    this.numeroSemana,
    required this.monto,
    required this.fecha,
    this.estado = 'activo', // Valor por defecto
    this.alumnoId,
    this.reciboId,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) : id = id ?? '',
       createdAt = createdAt ?? DateTime.now(),
       updatedAt = updatedAt ?? DateTime.now();

  factory DetallePagoUnificado.fromMap(Map<String, dynamic> map) {
    //imprime todo el map para ver cual viene nulo
    final a = DetallePagoUnificado(
      id: map['id'] as String? ?? '',
      concepto: map['concepto'] as String? ?? '',
      tipo: map['tipo'] as String? ?? 'costo_adicional',
      referenciaId: map['referenciaId'] as String?,
      numeroSemana: map['numeroSemana'] != null
          ? int.tryParse(map['numeroSemana'].toString())
          : null,
      monto: (map['monto'] as num?)?.toDouble() ?? 0.0,
      fecha: map['fecha'] is Timestamp
          ? (map['fecha'] as Timestamp).toDate()
          : DateTime.parse(map['fecha'].toString()),
      estado: map['estado'] as String? ?? 'activo', // Manejar nulos
      alumnoId: map['alumnoId'] as String?,
      reciboId: map['reciboId'] as String?,
      createdAt: map['createdAt'] != null
          ? (map['createdAt'] as Timestamp).toDate()
          : null,
      updatedAt: map['updatedAt'] != null
          ? (map['updatedAt'] as Timestamp).toDate()
          : null,
    );
    return a;
  }

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'concepto': concepto,
      'tipo': tipo,
      if (referenciaId != null) 'referenciaId': referenciaId,
      if (numeroSemana != null) 'numeroSemana': numeroSemana,
      'monto': monto,
      'fecha': fecha is Timestamp ? fecha : Timestamp.fromDate(fecha),
      if (alumnoId != null) 'alumnoId': alumnoId,
      if (reciboId != null) 'reciboId': reciboId,
      'estado': estado,
      if (createdAt != null) 'createdAt': Timestamp.fromDate(createdAt!),
      if (updatedAt != null) 'updatedAt': Timestamp.fromDate(updatedAt!),
    };
  }

  DetallePagoUnificado copyWith({
    String? id,
    String? concepto,
    String? tipo,
    String? referenciaId,
    int? numeroSemana,
    double? monto,
    DateTime? fecha,
    String? estado,
    String? alumnoId,
    String? reciboId,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return DetallePagoUnificado(
      id: id ?? this.id,
      concepto: concepto ?? this.concepto,
      tipo: tipo ?? this.tipo,
      referenciaId: referenciaId ?? this.referenciaId,
      numeroSemana: numeroSemana ?? this.numeroSemana,
      monto: monto ?? this.monto,
      fecha: fecha ?? this.fecha,
      estado: estado ?? this.estado,
      alumnoId: alumnoId ?? this.alumnoId,
      reciboId: reciboId ?? this.reciboId,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }
}
