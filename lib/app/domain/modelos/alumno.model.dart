import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:gemini_app/app/utils/indice_map.dart';
import '../entities/alumno.dart';

class AlumnoModel extends Alumno {
  AlumnoModel({
    required String id,
    required String nombre,
    required String apellidoPaterno,
    String? apellidoMaterno,
    required String carrera,
    required int semestre,
    required String email,
    String? telefono,
    DateTime? fechaRegistro,
    bool activo = true,
    String fotoUrl = '',
  }) : super(
         id: id,
         nombre: nombre,
         apellidoPaterno: apellidoPaterno,
         apellidoMaterno: apellidoMaterno,
         carrera: carrera,
         semestre: semestre,
         email: email,
         telefono: telefono,
         fechaRegistro: fechaRegistro,
         activo: activo,
         fotoUrl: fotoUrl,
       );

  String get indiceBusqueda => obtenerNombreIndex(nombre);

  // Documento de Firestore para la paginación
  DocumentSnapshot? documento;

  // In modelos/alumno.model.dart
  factory AlumnoModel.fromFirestore(DocumentSnapshot doc) {
    final data = doc.data() as Map<String, dynamic>;
    return AlumnoModel(
      id: doc.id,
      nombre: data['nombre'] ?? '',
      // Handle both old and new field names
      apellidoPaterno: data['apellidoPaterno'] ?? data['apellido1'] ?? '',
      apellidoMaterno: data['apellidoMaterno'] ?? data['apellido2'],
      carrera: data['carrera'] ?? '',
      semestre: data['semestre'] ?? 0,
      email: data['email'] ?? '',
      telefono: data['telefono'] ?? '',
      fechaRegistro:
          (data['fechaRegistro'] as Timestamp?)?.toDate() ?? DateTime.now(),
      activo: data['activo'] ?? true,
      fotoUrl: data['fotoUrl'] ?? '',
    )..documento = doc;
  }

  // Convertir un objeto Alumno a un Map para Firestore
  Map<String, dynamic> toFirestore() {
    return {
      'id': id,
      'nombre': nombre,
      'apellidoPaterno': apellidoPaterno,
      'apellidoMaterno': apellidoMaterno,
      'carrera': carrera,
      'semestre': semestre,
      'email': email,
      'telefono': telefono,
      'fechaRegistro': Timestamp.fromDate(fechaRegistro!),
      'activo': activo,
      'fotoUrl': fotoUrl,
    };
  }

  factory AlumnoModel.fromEntity(Alumno alumno) {
    return AlumnoModel(
      id: alumno.id,
      nombre: alumno.nombre,
      apellidoPaterno: alumno.apellidoPaterno,
      apellidoMaterno: alumno.apellidoMaterno,
      carrera: alumno.carrera,
      semestre: alumno.semestre,
      email: alumno.email,
      telefono: alumno.telefono,
      fechaRegistro: alumno.fechaRegistro,
      activo: alumno.activo,
      fotoUrl: alumno.fotoUrl,
    );
  }

  factory AlumnoModel.fromMap(Map<String, dynamic> map) {
    return AlumnoModel(
      id: map['id'] ?? '',
      nombre: map['nombre'] ?? '',
      apellidoPaterno: map['apellidoPaterno'] ?? '',
      apellidoMaterno: map['apellidoMaterno'] ?? '',
      carrera: map['carrera'] ?? '',
      semestre: map['semestre'] ?? 1,
      email: map['email'] ?? '',
      telefono: map['telefono'] ?? '',
      fechaRegistro: map['fechaRegistro']?.toDate(),
      activo: map['activo'] ?? true,
      fotoUrl: map['fotoUrl'] ?? '',
    );
  }

  toEntity() {
    return Alumno(
      id: id,
      nombre: nombre,
      apellidoPaterno: apellidoPaterno,
      apellidoMaterno: apellidoMaterno,
      carrera: carrera,
      semestre: semestre,
      email: email,
      telefono: telefono,
      fechaRegistro: fechaRegistro,
      activo: activo,
      fotoUrl: fotoUrl,
    );
  }

  // Crear una copia del objeto con campos actualizados
  AlumnoModel copyWith({
    String? id,
    String? nombre,
    String? apellidoPaterno,
    String? apellidoMaterno,
    String? carrera,
    int? semestre,
    String? email,
    String? telefono,
    DateTime? fechaRegistro,
    bool? activo,
    String? fotoUrl,
  }) {
    
    return AlumnoModel(
      id: id ?? this.id,
      nombre: nombre ?? this.nombre,
      apellidoPaterno: apellidoPaterno ?? this.apellidoPaterno,
      apellidoMaterno: apellidoMaterno ?? this.apellidoMaterno,
      carrera: carrera ?? this.carrera,
      semestre: semestre ?? this.semestre,
      email: email ?? this.email,
      telefono: telefono ?? this.telefono,
      fechaRegistro: fechaRegistro ?? this.fechaRegistro,
      activo: activo ?? this.activo,
      fotoUrl: fotoUrl ?? this.fotoUrl,
    );
  }
}
