// lib/app/modelos/kardex.dart
import 'package:cloud_firestore/cloud_firestore.dart';

class Kardex {
  final String id;
  final String alumnoId;
  final String alumnoNombre;
  final String cursoId;
  final String cursoNombre;
  final DateTime fechaInscripcion;
  final double montoPorSemana;
  final int totalSemanasCurso;
  final double montoTotalCurso;
  final double totalPagado;
  final double saldoPendiente;
  final bool activo;
  final DateTime createdAt;
  final DateTime updatedAt;

  Kardex({
    required this.id,
    required this.alumnoId,
    required this.alumnoNombre,
    required this.cursoId,
    required this.cursoNombre,
    required this.fechaInscripcion,
    required this.montoPorSemana,
    required this.totalSemanasCurso,
    required this.montoTotalCurso,
    this.totalPagado = 0.0,
    required this.saldoPendiente,
    this.activo = true,
    required this.createdAt,
    required this.updatedAt,
  });

  factory Kardex.fromMap(Map<String, dynamic> map) {
    return Kardex(
      id: map['id'] as String,
      alumnoId: map['alumnoId'] as String,
      alumnoNombre: map['alumnoNombre'] as String,
      cursoId: map['cursoId'] as String,
      cursoNombre: map['cursoNombre'] as String,
      fechaInscripcion: (map['fechaInscripcion'] as Timestamp).toDate(),
      montoPorSemana: (map['montoPorSemana'] as num).toDouble(),
      totalSemanasCurso: map['totalSemanasCurso'] as int,
      montoTotalCurso: (map['montoTotalCurso'] as num).toDouble(),
      totalPagado: (map['totalPagado'] as num?)?.toDouble() ?? 0.0,
      saldoPendiente: (map['saldoPendiente'] as num).toDouble(),
      activo: map['activo'] as bool? ?? true,
      createdAt: (map['createdAt'] as Timestamp).toDate(),
      updatedAt: (map['updatedAt'] as Timestamp).toDate(),
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'alumnoId': alumnoId,
      'alumnoNombre': alumnoNombre,
      'cursoId': cursoId,
      'cursoNombre': cursoNombre,
      'fechaInscripcion': Timestamp.fromDate(fechaInscripcion),
      'montoPorSemana': montoPorSemana,
      'totalSemanasCurso': totalSemanasCurso,
      'montoTotalCurso': montoTotalCurso,
      'totalPagado': totalPagado,
      'saldoPendiente': saldoPendiente,
      'activo': activo,
      'createdAt': Timestamp.fromDate(createdAt),
      'updatedAt': Timestamp.fromDate(updatedAt),
    };
  }

  Kardex copyWith({
    String? id,
    String? alumnoId,
    String? alumnoNombre,
    String? cursoId,
    String? cursoNombre,
    DateTime? fechaInscripcion,
    double? montoPorSemana,
    int? totalSemanasCurso,
    double? montoTotalCurso,
    double? totalPagado,
    double? saldoPendiente,
    bool? activo,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return Kardex(
      id: id ?? this.id,
      alumnoId: alumnoId ?? this.alumnoId,
      alumnoNombre: alumnoNombre ?? this.alumnoNombre,
      cursoId: cursoId ?? this.cursoId,
      cursoNombre: cursoNombre ?? this.cursoNombre,
      fechaInscripcion: fechaInscripcion ?? this.fechaInscripcion,
      montoPorSemana: montoPorSemana ?? this.montoPorSemana,
      totalSemanasCurso: totalSemanasCurso ?? this.totalSemanasCurso,
      montoTotalCurso: montoTotalCurso ?? this.montoTotalCurso,
      totalPagado: totalPagado ?? this.totalPagado,
      saldoPendiente: saldoPendiente ?? this.saldoPendiente,
      activo: activo ?? this.activo,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }
}

class KardexSemana {
  String id;
  String kardexId;
  int numeroSemana;
  double monto;
  double montoPagado;
  String estado;
  final bool pagada;
  bool watchStream; // For week stream watching
  List<String> reciboIds;
  DateTime createdAt;
  DateTime updatedAt;

  KardexSemana({
    required this.id,
    required this.kardexId,
    required this.numeroSemana,
    required this.monto,
    this.montoPagado = 0.0,
    this.estado = 'pendiente',
    this.pagada = false,
    this.watchStream = false, // Default to false
    this.reciboIds = const [],
    required this.createdAt,
    required this.updatedAt,
  });

  factory KardexSemana.fromMap(Map<String, dynamic> map) {
    return KardexSemana(
      id: map['id'] as String? ?? '',
      kardexId: map['kardexId'] as String? ?? '',
      numeroSemana: map['numeroSemana'] as int? ?? 0,
      monto: (map['monto'] as num?)?.toDouble() ?? 0.0,
      montoPagado: (map['montoPagado'] as num?)?.toDouble() ?? 0.0,
      estado: map['estado'] as String? ?? 'pendiente',
      pagada: map['pagada'] as bool? ?? false,
      watchStream: map['watchStream'] as bool? ?? false,
      reciboIds: List<String>.from(map['reciboIds'] ?? []),
      createdAt: map['createdAt'] != null
          ? (map['createdAt'] as Timestamp).toDate()
          : DateTime.now(),
      updatedAt: map['updatedAt'] != null
          ? (map['updatedAt'] as Timestamp).toDate()
          : DateTime.now(),
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'kardexId': kardexId,
      'numeroSemana': numeroSemana,
      'monto': monto,
      'montoPagado': montoPagado,
      'estado': estado,
      'pagada': pagada,
      'watchStream': watchStream,
      'reciboIds': reciboIds,
      'createdAt': Timestamp.fromDate(createdAt),
      'updatedAt': Timestamp.fromDate(updatedAt),
    };
  }

  KardexSemana copyWith({
    String? id,
    String? kardexId,
    int? numeroSemana,
    double? monto,
    double? montoPagado,
    bool? pagada,
    String? estado,
    bool? watchStream,
    List<String>? reciboIds,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return KardexSemana(
      id: id ?? this.id,
      kardexId: kardexId ?? this.kardexId,
      numeroSemana: numeroSemana ?? this.numeroSemana,
      monto: monto ?? this.monto,
      montoPagado: montoPagado ?? this.montoPagado,
      pagada: pagada ?? this.pagada,
      estado: estado ?? this.estado,
      watchStream: watchStream ?? this.watchStream,
      reciboIds: reciboIds ?? this.reciboIds,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }
}

