// lib/app/modelos/costo_adicional.dart
import 'package:cloud_firestore/cloud_firestore.dart';

class CostoAdicional {
  final String id;
  final String kardexId;
  final String nombre;
  final String descripcion;
  final double monto;
  final double montoPagado;
  final bool pagado;
  final String? reciboId;
  final DateTime fechaVencimiento;
  final DateTime createdAt;
  final DateTime updatedAt;

  CostoAdicional({
    required this.id,
    required this.kardexId,
    required this.nombre,
    required this.descripcion,
    required this.monto,
    required this.montoPagado,
    required this.pagado,
    this.reciboId,
    required this.fechaVencimiento,
    required this.createdAt,
    required this.updatedAt,
  });

  factory CostoAdicional.fromMap(Map<String, dynamic> map) {
    return CostoAdicional(
      id: map['id'] as String,
      kardexId: map['kardexId'] as String,
      nombre: map['nombre'] as String,
      descripcion: map['descripcion'] as String,
      monto: (map['monto'] as num).toDouble(),
      montoPagado: (map['montoPagado'] as num).toDouble(),
      pagado: map['pagado'] as bool,
      reciboId: map['reciboId'] as String?,
      fechaVencimiento: (map['fechaVencimiento'] as Timestamp).toDate(),
      createdAt: (map['createdAt'] as Timestamp).toDate(),
      updatedAt: (map['updatedAt'] as Timestamp).toDate(),
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'kardexId': kardexId,
      'nombre': nombre,
      'descripcion': descripcion,
      'monto': monto,
      'montoPagado': montoPagado,
      'pagado': pagado,
      'reciboId': reciboId,
      'fechaVencimiento': Timestamp.fromDate(fechaVencimiento),
      'createdAt': Timestamp.fromDate(createdAt),
      'updatedAt': Timestamp.fromDate(updatedAt),
    };
  }

  copyWith({
    String? id,
    String? kardexId,
    String? nombre,
    String? descripcion,
    double? monto,
    double? montoPagado,
    bool? pagado,
    String? reciboId,
    DateTime? fechaVencimiento,
  }) {
    return CostoAdicional(
      id: id ?? this.id,
      kardexId: kardexId ?? this.kardexId,
      nombre: nombre ?? this.nombre,
      descripcion: descripcion ?? this.descripcion,
      monto: monto ?? this.monto,
      montoPagado: montoPagado ?? this.montoPagado,
      pagado: pagado ?? this.pagado,
      reciboId: reciboId ?? this.reciboId,
      fechaVencimiento: fechaVencimiento ?? this.fechaVencimiento,
      createdAt: createdAt,
      updatedAt: DateTime.now(),
    );
  }
}