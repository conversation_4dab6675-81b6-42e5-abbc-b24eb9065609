// lib/app/modelos/recibo.dart
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:gemini_app/app/domain/modelos/detalle_pago_unificado.dart';

class Recibo {
  final String id;
  final String folio;
  final String alumnoId;
  final String alumnoNombre;
  final DateTime fechaEmision;
  final double montoTotal;
  final String metodoPago;
  final String concepto;
  final List<DetallePagoUnificado> detalles;
  final String? notas;
  final String? creadoPor;
  final String estado; // 'activo' o 'cancelado'
  final DateTime? fechaCancelacion;
  final String? canceladoPor;

  Recibo({
    required this.id,
    required this.folio,
    required this.alumnoId,
    required this.alumnoNombre,
    required this.fechaEmision,
    required this.montoTotal,
    required this.metodoPago,
    required this.concepto,
    required this.detalles,
    String? notas,
    String? creadoPor,
    this.estado = 'activo', // Valor por defecto
    this.fechaCancelacion,
    this.canceladoPor,
  }) : notas = notas ?? '',
       creadoPor = creadoPor ?? '';

  // Convertir de Map a Recibo
  factory Recibo.fromMap(Map<String, dynamic> map) {
    return Recibo(
      id: map['id'] as String? ?? '',
      folio: map['folio'] as String? ?? '',
      alumnoId: map['alumnoId'] as String? ?? '',
      alumnoNombre: map['alumnoNombre'] as String? ?? '',
      fechaEmision: (map['fechaEmision'] as Timestamp).toDate(),
      montoTotal: (map['montoTotal'] as num).toDouble(),
      metodoPago: map['metodoPago'] as String? ?? '',
      concepto: map['concepto'] as String? ?? '',
      detalles:
          (map['detalles'] as List)
              .map((e) => DetallePagoUnificado.fromMap(e as Map<String, dynamic>))
              .toList(),
      notas: map['notas'] as String? ?? '',
      creadoPor: map['creadoPor'] as String? ?? '',
      estado: map['estado'] as String? ?? 'activo',
      fechaCancelacion: map['fechaCancelacion'] != null ? (map['fechaCancelacion'] as Timestamp).toDate() : null,
      canceladoPor: map['canceladoPor'] as String?,
    );
  }

  // Convertir de Recibo a Map
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'folio': folio,
      'alumnoId': alumnoId,
      'alumnoNombre': alumnoNombre,
      'fechaEmision': Timestamp.fromDate(fechaEmision),
      'montoTotal': montoTotal,
      'metodoPago': metodoPago,
      'concepto': concepto,
      'detalles': detalles.map((e) => e.toMap()).toList(),
      'notas': notas ?? '',
      'creadoPor': creadoPor ?? '',
      'estado': estado, // Nuevo campo
      'fechaCancelacion': fechaCancelacion != null ? Timestamp.fromDate(fechaCancelacion!) : null,
      'canceladoPor': canceladoPor,
    };
  }

  // Generar un nuevo folio
  static String generarFolio() {
    final now = DateTime.now();
    return 'R-${now.year}${now.month.toString().padLeft(2, '0')}${now.day.toString().padLeft(2, '0')}-${now.millisecondsSinceEpoch}';
  }

  Recibo copyWith({
    String? id,
    String? folio,
    String? alumnoId,
    String? alumnoNombre,
    DateTime? fechaEmision,
    double? montoTotal,
    String? metodoPago,
    String? concepto,
    List<DetallePagoUnificado>? detalles,
    String? notas,
    String? creadoPor,
    String? estado,
    DateTime? fechaCancelacion,
    String? canceladoPor,
  }) {
    return Recibo(
      id: id ?? this.id,
      folio: folio ?? this.folio,
      alumnoId: alumnoId ?? this.alumnoId,
      alumnoNombre: alumnoNombre ?? this.alumnoNombre,
      fechaEmision: fechaEmision ?? this.fechaEmision,
      montoTotal: montoTotal ?? this.montoTotal,
      metodoPago: metodoPago ?? this.metodoPago,
      concepto: concepto ?? this.concepto,
      detalles: detalles ?? this.detalles,
      notas: notas ?? this.notas,
      creadoPor: creadoPor ?? this.creadoPor,
      estado: estado ?? this.estado,
      fechaCancelacion: fechaCancelacion ?? this.fechaCancelacion,
      canceladoPor: canceladoPor ?? this.canceladoPor,
    );
  }
}

// Modelo para los detalles de pago dentro del recibo
