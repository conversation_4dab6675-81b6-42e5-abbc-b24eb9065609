import 'package:gemini_app/app/domain/modelos/recibo.dart';

abstract class ReciboRepository {
  Future<Recibo> getReciboById(String id);
  Future<void> updateRecibo(Recibo recibo);
  Stream<List<Recibo>> getRecibosStream(String alumnoId);
  Future<void> crearRecibo(Map<String, dynamic> reciboData);
  
  /// Obtiene el último recibo activo para un alumno
  /// Devuelve null si no hay recibos activos
  Future<Recibo?> getUltimoReciboActivo(String alumnoId);
}