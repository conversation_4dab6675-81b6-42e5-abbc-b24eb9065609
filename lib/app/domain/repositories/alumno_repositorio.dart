import '../../../di/injection.dart';
import '../../data/repositories/alumno_repository_impl.dart';
import '../entities/alumno.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import '../dto/detalle_alumno.dto.dart'; // Importar DTO

abstract class AlumnoRepository {
  Future<(List<Alumno>, DocumentSnapshot?)> obtenerAlumnosPaginados({
    int limite = 10,
    DocumentSnapshot? startAfter,
    String searchQuery = '',
  });
  // Nuevo método para el stream
  Stream<(List<Alumno>, DocumentSnapshot?)> streamAlumnosPorBusqueda({
    required String searchQuery,
    int limite = 10,
    DocumentSnapshot? startAfter,
  });
  
  Future<Alumno?> obtenerAlumnoPorId(String id);

  /// Actualiza los datos de un alumno existente
  /// Lanza una excepción si el ID del alumno es nulo
  Future<void> actualizarAlumno(Alumno alumno);

  /// Actualiza el token de búsqueda de un alumno
  /// Esto es útil para mejorar las búsquedas en la aplicación
  Future<void> actualizarIndiceBusqueda(String alumnoId, String indiceBusqueda);

  Future<void> migrarNombresApellidos();

  // In repositorios/alumno_repositorio.dart
  Future<List<Alumno>> buscarAlumnosPorIndice(String busqueda);

  // Nuevo método para stream de alumno
  Stream<Alumno> streamAlumno(String id);
}
