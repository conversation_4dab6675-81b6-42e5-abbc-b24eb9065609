import 'package:gemini_app/app/domain/modelos/kardex.dart';
import 'package:gemini_app/app/domain/modelos/detalle_pago_unificado.dart'; // Updated import
import 'package:gemini_app/app/domain/modelos/costo_adicional.dart';
import 'package:gemini_app/app/domain/modelos/recibo.dart';

abstract class KardexRepository {
  Future<Kardex> getKardexById(String id);
  Future<void> updateKardex(Kardex kardex);
  Future<KardexSemana> getKardexSemanaById(String alumnoId, String semanaId);
  Future<void> updateKardexSemana(KardexSemana semana);
  Future<void> deleteKardexSemana(String alumnoId, String semanaId);
  Future<Kardex> getKardexByAlumnoId(String alumnoId);
  Future<List<KardexSemana>> getSemanas(String alumnoId);
  Stream<List<KardexSemana>> watchSemanas(String alumnoId);
  
  /// Real-time stream of kardex data (nullable)
  Stream<Kardex?> watchKardex(String alumnoId);
  
  /// Real-time stream of additional costs
  Stream<List<CostoAdicional>> watchCostosAdicionales(String alumnoId);
  
  /// Real-time stream of payment details
  Stream<List<DetallePagoUnificado>> watchDetallesPagos(String alumnoId);
  
  Future<List<DetallePagoUnificado>> getDetallesPago(String alumnoId); // Updated type
  Future<List<CostoAdicional>> getCostosAdicionales(String alumnoId);
  Future<void> guardarDetallePago(DetallePagoUnificado detalle); // Updated type
  Future<void> actualizarCostoAdicional(CostoAdicional costo);
  Future<void> guardarReciboEnKardex(String alumnoId, Recibo recibo);
}
