import 'package:gemini_app/app/domain/modelos/detalle_pago_unificado.dart';
import 'package:gemini_app/app/domain/modelos/recibo.dart';
import 'package:gemini_app/app/domain/modelos/kardex.dart';
import 'package:gemini_app/app/domain/modelos/costo_adicional.dart';
import 'package:gemini_app/app/domain/repositories/recibo_repository.dart';
import 'package:gemini_app/app/domain/repositories/kardex_repository.dart';

class CancelarReciboUseCase {
  final ReciboRepository _reciboRepo;
  final KardexRepository _kardexRepo;

  CancelarReciboUseCase(this._reciboRepo, this._kardexRepo);

  Future<void> execute(String reciboId) async {
    final recibo = await _reciboRepo.getReciboById(reciboId);

    if (recibo.estado == 'cancelado') {
      throw Exception('Recibo ya cancelado');
    }

    // Verificar que sea el último recibo activo
    final ultimoReciboActivo = await _reciboRepo.getUltimoReciboActivo(recibo.alumnoId);
    if (ultimoReciboActivo == null || ultimoReciboActivo.id != recibo.id) {
      throw Exception('Solo se puede cancelar el último recibo activo');
    }

    // Revertir semanas y costos adicionales
    for (final detalle in recibo.detalles) {
      if (detalle.tipo == 'semana' && (detalle.referenciaId != null || detalle.numeroSemana != null)) {
        await _revertirSemana(recibo.alumnoId, detalle, recibo.id);
      } else if (detalle.tipo == 'costo_adicional' && detalle.referenciaId != null) {
        await _revertirCostoAdicional(recibo.alumnoId, detalle, recibo.id);
      }
    }

    // Actualizar kardex principal
    await _revertirKardexPrincipal(recibo);


    // Actualizar detalles a cancelados
    final detallesCancelados = recibo.detalles
        .map((detalle) => detalle.copyWith(estado: 'cancelado'))
        .toList();

    // Marcar recibo como cancelado, establecer fecha y usuario de cancelación
    final updatedRecibo = recibo.copyWith(
      estado: 'cancelado',
      detalles: detallesCancelados,
      fechaCancelacion: DateTime.now(),
      canceladoPor: 'UsuarioActual', // TODO: Obtener usuario actual
    );
    await _reciboRepo.updateRecibo(updatedRecibo);

    // Actualizar recibo en el kardex
    await _kardexRepo.guardarReciboEnKardex(updatedRecibo.alumnoId, updatedRecibo);
  }

  Future<void> _revertirSemana(String alumnoId, DetallePagoUnificado detalle, String reciboId) async {
    // Obtener semana por número de semana si no hay referenciaId
    KardexSemana semana;
    if (detalle.referenciaId != null) {
      semana = await _kardexRepo.getKardexSemanaById(alumnoId, detalle.referenciaId!);
    } else if (detalle.numeroSemana != null) {
      final semanas = await _kardexRepo.getSemanas(alumnoId);
      semana = semanas.firstWhere(
        (s) => s.numeroSemana == detalle.numeroSemana,
        orElse: () => throw Exception('Semana no encontrada'),
      );
    } else {
      throw Exception('Detalle de semana sin referencia');
    }

    if (semana.reciboIds.contains(reciboId)) {
      // Remove this receipt from week's receipts
      final newReciboIds = semana.reciboIds.where((id) => id != reciboId).toList();
      final newMontoPagado = semana.montoPagado - detalle.monto;


      // Delete week document if paid amount becomes zero
      if (newMontoPagado <= 0) {
        await _kardexRepo.deleteKardexSemana(alumnoId, semana.id);
      } else {
        final newEstado = newMontoPagado <= 0 ? 'pendiente' :
                          newMontoPagado >= semana.monto ? 'pagado' : 'parcial';

        final updatedSemana = semana.copyWith(
          reciboIds: newReciboIds,
          montoPagado: newMontoPagado,
          estado: newEstado,
        );

        await _kardexRepo.updateKardexSemana(updatedSemana);
      }
    }
  }

  Future<void> _revertirCostoAdicional(String alumnoId, DetallePagoUnificado detalle, String reciboId) async {
    // Obtener todos los costos adicionales del alumno
    final costos = await _kardexRepo.getCostosAdicionales(alumnoId);
    CostoAdicional? costo;
    try {
      costo = costos.firstWhere((c) => c.id == detalle.referenciaId);
    } on StateError {
      costo = null;
    }

    if (costo != null) {
      // Subtract the payment amount from the paid amount
      final nuevoMontoPagado = costo.montoPagado - detalle.monto;
      // If the new paid amount is less than the total cost, mark as not fully paid
      final nuevoPagado = nuevoMontoPagado >= costo.monto;
      // Only clear reciboId if the paid amount becomes zero
      final nuevoReciboId = nuevoMontoPagado <= 0 ? null : costo.reciboId;

      final updatedCosto = costo.copyWith(
        montoPagado: nuevoMontoPagado,
        pagado: nuevoPagado,
        reciboId: nuevoReciboId,
      );

      await _kardexRepo.actualizarCostoAdicional(updatedCosto);
    } else {
      print('Costo adicional no encontrado para referenciaId: ${detalle.referenciaId}');
    }
  }

  Future<void> _revertirKardexPrincipal(Recibo recibo) async {
    final kardex = await _kardexRepo.getKardexByAlumnoId(recibo.alumnoId);

    // Calcular el monto total del recibo (todos los detalles)
    final montoTotalRecibo = recibo.detalles.fold(0.0, (sum, detalle) => sum + detalle.monto);

    final updatedKardex = kardex.copyWith(
      totalPagado: kardex.totalPagado - montoTotalRecibo,
      saldoPendiente: kardex.saldoPendiente + montoTotalRecibo,
    );

    await _kardexRepo.updateKardex(updatedKardex);
  }
}
