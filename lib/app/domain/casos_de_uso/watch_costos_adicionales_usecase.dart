import 'package:gemini_app/app/domain/modelos/costo_adicional.dart';
import 'package:gemini_app/app/domain/repositories/kardex_repository.dart';

class WatchCostosAdicionalesUseCase {
  final KardexRepository _kardexRepository;

  WatchCostosAdicionalesUseCase(this._kardexRepository);

  Stream<List<CostoAdicional>> execute(String alumnoId) {
    return _kardexRepository.watchCostosAdicionales(alumnoId);
  }
}
