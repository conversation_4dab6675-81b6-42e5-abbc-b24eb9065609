import 'package:gemini_app/app/domain/dto/detalle_alumno.dto.dart';
import 'package:gemini_app/app/domain/entities/alumno.dart';
import 'package:gemini_app/app/domain/modelos/kardex.dart';
import 'package:gemini_app/app/domain/modelos/detalle_pago_unificado.dart';
import 'package:gemini_app/app/domain/modelos/costo_adicional.dart';
import 'package:gemini_app/app/domain/repositories/alumno_repositorio.dart';
import 'package:gemini_app/app/domain/repositories/kardex_repository.dart';
import 'package:gemini_app/app/domain/repositories/recibo_repository.dart';

class DetalleAlumnoUseCase {
  final AlumnoRepository _alumnoRepo;
  final KardexRepository _kardexRepo;
  final ReciboRepository _reciboRepo;
  final Map<String, DetalleAlumnoDTO> _cache = {};
  static const _cacheDuration = Duration(minutes: 15);

  DetalleAlumnoUseCase(this._alumnoRepo, this._kardexRepo, this._reciboRepo);

  Future<DetalleAlumnoDTO> getAlumnoDetails(String alumnoId) async {
    // Check cache
    final cached = _cache[alumnoId];
    if (cached != null &&
        DateTime.now().difference(cached.timestamp) < _cacheDuration) {
      return cached;
    }

    try {
      final results = await Future.wait([
        _alumnoRepo.obtenerAlumnoPorId(alumnoId),
        _kardexRepo.getKardexByAlumnoId(alumnoId),
        _kardexRepo.getSemanas(alumnoId),
        _kardexRepo.getDetallesPago(alumnoId),
        _kardexRepo.getCostosAdicionales(alumnoId),
      ]);
      final alumno = results[0] as Alumno?;
      if (alumno == null) {
        throw Exception('Alumno no encontrado');
      }
      final kardex = results[1] as Kardex;
      final semanas = results[2] as List<KardexSemana>;
      final detallesPago = results[3] as List<DetallePagoUnificado>;
      final costosAdicionales = results[4] as List<CostoAdicional>;

      final dto = DetalleAlumnoDTO(
        alumno: alumno,
        kardex: kardex,
        semanas: semanas,
        detallesPago: detallesPago,
        costosAdicionales: costosAdicionales,
      );

      // Update cache
      _cache[alumnoId] = dto;

      return dto;
    } catch (e) {
      throw Exception('Error en DetalleAlumnoUseCase: $e');
    }
  }

  Future<void> generarRecibo(
    String alumnoId,
    Map<String, dynamic> reciboData,
  ) async {
    await _reciboRepo.crearRecibo(reciboData);
    // Invalidate cache after generating receipt
    _cache.remove(alumnoId);
  }
}
