// lib/features/alumnos/domain/entities/alumno.dart
import 'package:equatable/equatable.dart';

class Alumno extends Equatable {
  final String id;
  final String nombre;
  final String apellidoPaterno;
  final String? apellidoMaterno;
  final String carrera;
  final int semestre;
  final String email;
  final String? telefono;
  final DateTime? fechaRegistro;
  final bool activo;
  final String fotoUrl;

  Alumno({
    required this.id,
    required this.nombre,
    required this.apellidoPaterno,
    this.apellidoMaterno,
    required this.carrera,
    required this.semestre,
    required this.email,
    this.telefono,
    this.fechaRegistro,
    this.activo = true,
    this.fotoUrl = '',
  })  : assert(id.isNotEmpty, 'Alumno ID must not be empty'),
        assert(nombre.isNotEmpty, 'Nombre must not be empty'),
        assert(apellidoPaterno.isNotEmpty, 'Apellido paterno must not be empty'),
        assert(carrera.isNotEmpty, 'Carrera must not be empty'),
        assert(email.isNotEmpty, 'Email must not be empty');

  String get nombreCompleto =>
      '$nombre ${apellidoPaterno??''} ${apellidoMaterno??''}';

  @override
  List<Object?> get props => [
    id,
    nombre,
    apellidoPaterno,
    apellidoMaterno,
    carrera,
    semestre,
    email,
    telefono,
    fechaRegistro,
    activo,
    fotoUrl,
  ];
}
