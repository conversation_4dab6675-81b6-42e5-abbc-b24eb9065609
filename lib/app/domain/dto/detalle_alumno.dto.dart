import 'package:gemini_app/app/domain/entities/alumno.dart';
import 'package:gemini_app/app/domain/modelos/kardex.dart';
import 'package:gemini_app/app/domain/modelos/detalle_pago_unificado.dart';
import 'package:gemini_app/app/domain/modelos/costo_adicional.dart';

class DetalleAlumnoDTO {
  final Alumno alumno;
  final Kardex kardex;
  final List<KardexSemana> semanas;
  final List<DetallePagoUnificado> detallesPago;
  final List<CostoAdicional> costosAdicionales;
  final DateTime timestamp;

  DetalleAlumnoDTO({
    required this.alumno,
    required this.kardex,
    required this.semanas,
    required this.detallesPago,
    required this.costosAdicionales,
    DateTime? timestamp,
  }) : timestamp = timestamp ?? DateTime.now();

  DetalleAlumnoDTO copyWith({
    Alumno? alumno,
    Kardex? kardex,
    List<KardexSemana>? semanas,
    List<DetallePagoUnificado>? detallesPago,
    List<CostoAdicional>? costosAdicionales,
    DateTime? timestamp,
  }) {
    return DetalleAlumnoDTO(
      alumno: alumno ?? this.alumno,
      kardex: kardex ?? this.kardex,
      semanas: semanas ?? this.semanas,
      detallesPago: detallesPago ?? this.detallesPago,
      costosAdicionales: costosAdicionales ?? this.costosAdicionales,
      timestamp: timestamp ?? this.timestamp,
    );
  }
}