import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/foundation.dart';

class ProveedorAutenticacion with ChangeNotifier {
  final FirebaseAuth _auth = FirebaseAuth.instance;
  User? _usuario;
  bool _cargando = true; // Inicialmente en true para indicar que está cargando
  bool _inicializado = false;
  String? _error;

  ProveedorAutenticacion() {
    _inicializar();
  }

  Future<void> _inicializar() async {
    try {
      // Configurar listener primero para capturar el estado inicial
      _auth.authStateChanges().listen((User? usuario) {
        _usuario = usuario;
        _cargando = false;
        _inicializado = true;
        notifyListeners();
      });
      
      // Esperar el primer cambio de estado para asegurar inicialización
      await _auth.authStateChanges().first;
    } catch (e) {
      _error = 'Error al inicializar la autenticación';
      _cargando = false;
      _inicializado = true;
      notifyListeners();
    }
    
  }

  // Getters
  User? get usuario => _usuario;
  bool get estaAutenticado => _usuario != null;
  bool get cargando => _cargando || !_inicializado;
  String? get error => _error;
  bool get estaInicializado => _inicializado;

  // Iniciar sesión con correo y contraseña
  Future<void> iniciarSesion(String correo, String contrasena) async {
    _cargando = true;
    _error = null;
    notifyListeners();

    try {
      await _auth.signInWithEmailAndPassword(
        email: correo,
        password: contrasena,
      );
    } on FirebaseAuthException catch (e) {
      _error = _getErrorMessage(e.code);
    } catch (e) {
      _error = 'Ocurrió un error inesperado';
    } finally {
      _cargando = false;
      notifyListeners();
    }
  }

  // Cerrar sesión
  Future<void> signOut() async {
    await _auth.signOut();
  }

  // Traducir códigos de error de Firebase
  String _getErrorMessage(String code) {
    switch (code) {
      case 'user-not-found':
        return 'No existe una cuenta con este correo electrónico';
      case 'wrong-password':
        return 'Contraseña incorrecta';
      case 'invalid-email':
        return 'El formato del correo electrónico no es válido';
      case 'user-disabled':
        return 'Esta cuenta ha sido deshabilitada';
      case 'too-many-requests':
        return 'Demasiados intentos fallidos. Inténtalo más tarde';
      default:
        return 'Error de autenticación: $code';
    }
  }
}