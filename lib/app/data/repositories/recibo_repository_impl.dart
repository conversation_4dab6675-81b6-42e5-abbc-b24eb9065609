import 'package:gemini_app/app/domain/modelos/recibo.dart';
import 'package:gemini_app/app/domain/repositories/recibo_repository.dart';
import 'package:gemini_app/app/domain/repositories/kardex_repository.dart';
import 'package:gemini_app/app/servicios/database_service.dart';

class ReciboRepositoryImpl implements ReciboRepository {
  final DatabaseService _databaseService;
  final KardexRepository _kardexRepo;

  ReciboRepositoryImpl(this._databaseService, this._kardexRepo);

  @override
  Future<Recibo> getReciboById(String reciboId) async {
    try {
      final data = await _databaseService.getDocument('recibos/$reciboId');
      if (data == null) {
        throw Exception('Recibo con ID $reciboId no encontrado');
      }
      return Recibo.fromMap(data);
    } catch (e) {
      print('Error al cargar recibo $reciboId: $e');
      rethrow;
    }
  }

  @override
  Future<void> updateRecibo(Recibo recibo) async {
    await _databaseService.saveDocument('recibos/${recibo.id}', recibo.toMap());
  }

  @override
  Stream<List<Recibo>> getRecibosStream(String alumnoId) {
    try {
      return _databaseService
          .streamCollection(
            'recibos',
            filters: {'alumnoId': alumnoId},
          )
          .map((documents) => documents
              .map((doc) => Recibo.fromMap(doc))
              .toList()
                ..sort((a, b) => b.fechaEmision.compareTo(a.fechaEmision)));
    } catch (e) {
      print('Error al obtener stream de recibos: $e');
      rethrow;
    }
  }

  @override
  Future<void> crearRecibo(Map<String, dynamic> reciboData) async {
    final recibo = Recibo.fromMap(reciboData);

    // Save to main receipts collection
    await _databaseService.saveDocument('recibos/${recibo.id}', recibo.toMap());

    // Also save copy to kardex
    await _kardexRepo.guardarReciboEnKardex(recibo.alumnoId, recibo);
  }

  @override
  Future<Recibo?> getUltimoReciboActivo(String alumnoId) async {
    try {
      // Get all receipts for the student
      final (dataList, _) = await _databaseService.getCollection(
        path: 'recibos',
        filters: {
          'alumnoId': alumnoId,
          'estado': 'activo', // Only non-canceled receipts
        },
        sortBy: 'fechaEmision',
        descending: true, // Get most recent first
        limit: 1, // We only need the most recent one
      );


      if (dataList.isEmpty) {
        return null;
      }

      return Recibo.fromMap(dataList.first);
    } catch (e) {
      print('Error al obtener último recibo activo: $e');
      rethrow;
    }
  }
}
