import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/foundation.dart';
import '../../domain/dto/detalle_alumno.dto.dart';
import '../../domain/modelos/costo_adicional.dart';
import '../../domain/modelos/kardex.dart';

import '../../domain/entities/alumno.dart';
import '../../domain/modelos/alumno.model.dart';
import '../../domain/repositories/alumno_repositorio.dart';
import '../../servicios/database_service.dart';

class AlumnosRepositoryImpl implements AlumnoRepository {
  final DatabaseService _db;

  AlumnosRepositoryImpl(this._db);
  @override
  Stream<(List<Alumno>, DocumentSnapshot?)> streamAlumnosPorBusqueda({
    required String searchQuery,
    int limite = 10,
    DocumentSnapshot? startAfter,
  }) {
    try {
      return _db
          .streamSearchAlumnos(
            searchQuery: searchQuery,
            path: 'alumnos',
            limit: limite,
            startAfter: startAfter,
            sortBy: 'nombre',
            descending: false,
          )
          .map((data) {
        final alumnos = data.$1
            .map((item) {
              try {
                return AlumnoModel.fromMap(item);
              } catch (e) {
                debugPrint('Error mapeando alumno: $e, datos: $item');
                return null;
              }
            })
            .where((alumno) => alumno != null)
            .cast<Alumno>()
            .toList();
        return (alumnos, data.$2);
      });
    } catch (e) {
      debugPrint('Error en streamAlumnosPorBusqueda: $e');
      rethrow;
    }
  }
  @override
  Future<(List<Alumno>, DocumentSnapshot?)> obtenerAlumnosPaginados({
    int limite = 10,
    DocumentSnapshot? startAfter,
    String searchQuery = '',
  }) async {
    try {
      Map<String, dynamic> filters = {};
      if (searchQuery.isNotEmpty) {
        // TODO: Implement proper search once DatabaseService supports advanced queries

        debugPrint(
          'Warning: searchQuery is not supported in obtenerAlumnosPaginados at the moment',
        );
      }

      final (dataList, nextStartAfter) = await _db.searchAlumnos(
        path: 'alumnos',
        limit: limite,
        startAfter: startAfter,
        sortBy: 'nombre',
        searchQuery: searchQuery,
        descending: false,
      );

      final alumnos =
          dataList.map((data) => AlumnoModel.fromMap(data)).toList();

      return (alumnos, nextStartAfter as DocumentSnapshot?);
    } catch (e) {
      debugPrint('Error en obtenerAlumnosPaginados: $e');
      rethrow;
    }
  }

  @override
  Future<Alumno?> obtenerAlumnoPorId(String id) async {
    try {
      final data = await _db.getDocument('alumnos/$id');
      if (data != null) {
        return AlumnoModel.fromMap({'id': id, ...data});
     
      }
      return null;
    } catch (e) {
      print('Error obteniendo alumno: $e');
      rethrow;
    }
  }

  @override
  Future<void> actualizarAlumno(Alumno alumno) async {
    try {
      if (alumno.id == null) {
        throw Exception('El ID del alumno no puede ser nulo');
      }

      final alumnoMap = AlumnoModel.fromEntity(alumno).toFirestore();
      await _db.saveDocument('alumnos/${alumno.id}', alumnoMap, merge: true);
    } catch (e) {
      print('Error actualizando alumno: $e');
      rethrow;
    }
  }

  @override
  Future<void> actualizarIndiceBusqueda(
    String alumnoId,
    String indiceBusqueda,
  ) async {
    try {
      await _db.saveDocument('alumnos/$alumnoId', {
        'indiceBusqueda': indiceBusqueda,
        'ultimaActualizacion': DateTime.now(),
      }, merge: true);
    } catch (e) {
      print('Error actualizando índice de búsqueda: $e');
      rethrow;
    }
  }

  @override
  Future<List<Alumno>> buscarAlumnosPorIndice(String busqueda) async {
    try {
      if (busqueda.isEmpty) {
        return [];
      }

      final (dataList, _) = await _db.getCollection(
        path: 'alumnos',
        filters: {'indiceBusqueda': busqueda.toLowerCase()},
        limit: 50,
      );

      return dataList.map((data) => AlumnoModel.fromMap(data)).toList();
    } catch (e) {
      print('Error buscando alumnos: $e');
      rethrow;
    }
  }

  @override
  Future<void> migrarNombresApellidos() async {
    try {
      final (dataList, _) = await _db.getCollection(path: 'alumnos');
      final updates =
          dataList
              .where(
                (data) =>
                    data.containsKey('apellido1') ||
                    data.containsKey('apellido2'),
              )
              .map(
                (data) => {
                  'path': 'alumnos/${data['id']}',
                  'data': {
                    'apellidoPaterno': data['apellido1'] ?? '',
                    'apellidoMaterno': data['apellido2'] ?? '',
                  },
                },
              )
              .toList();
      
      await _db.batchUpdate(updates);
    } catch (e) {
      print('Error migrando nombres de apellidos: $e');
      rethrow;
    }
  }

  @override
  Stream<Alumno> streamAlumno(String id) {
    return _db.streamDocument('alumnos/$id').map((data) {
      if (data == null) {
        throw Exception('Alumno no encontrado');
      }
      return AlumnoModel.fromMap({'id': id, ...data});
    });
  }
}

