import 'package:gemini_app/app/domain/modelos/kardex.dart';
import 'package:gemini_app/app/domain/modelos/detalle_pago_unificado.dart';
import 'package:gemini_app/app/domain/modelos/costo_adicional.dart';
import 'package:gemini_app/app/domain/modelos/recibo.dart';
import 'package:gemini_app/app/domain/repositories/kardex_repository.dart';
import 'package:gemini_app/app/servicios/database_service.dart';

class KardexRepositoryImpl implements KardexRepository {
  final DatabaseService _databaseService;

  KardexRepositoryImpl(this._databaseService);

  @override
  Future<Kardex> getKardexById(String id) async {
    final data = await _databaseService.getDocument('kardex/$id');
    if (data == null) {
      throw Exception('No se encontró el kardex con el ID: $id');
    }
    return Kardex.fromMap(data);
  }

  @override
  Future<void> updateKardex(Kardex kardex) async {
    print(kardex.toMap());
    await _databaseService.saveDocument(
      'kardex/${kardex.alumnoId}',
      kardex.toMap(),
      merge: true,
    );
  }

  @override
  Future<KardexSemana> getKardexSemanaById(
    String alumnoId,
    String semanaId,
  ) async {
    final data = await _databaseService.getDocument(
      'kardex/$alumnoId/semanas/$semanaId',
    );

    if (data == null) {
      throw Exception('No se encontró la semana con el ID: $semanaId');
    }

    return KardexSemana.fromMap(data);
  }

  @override
  Future<void> updateKardexSemana(KardexSemana semana) async {
    if (semana.kardexId.isEmpty) {
      throw ArgumentError('kardexId no puede estar vacío');
    }
    if (semana.id.isEmpty) {
      throw ArgumentError('El ID de la semana no puede estar vacío');
    }

    final path = 'kardex/${semana.kardexId}/semanas/${semana.id}';

    try {
      await _databaseService.saveDocument(path, semana.toMap(), merge: true);
    } catch (e) {
      print('Error al actualizar semana: $e');
      rethrow;
    }
  }

  @override
  Future<Kardex> getKardexByAlumnoId(String alumnoId) async {
    final data = await _databaseService.getDocument('kardex/$alumnoId');
    if (data == null) {
      throw Exception('No se encontró el kardex con el ID: $alumnoId');
    }
    return Kardex.fromMap(data);
  }

  @override
  @override
  Future<List<KardexSemana>> getSemanas(String alumnoId) async {
    final (dataList, _) = await _databaseService.getCollection(
      path: 'kardex/$alumnoId/semanas',
    );
    return dataList.map((data) => KardexSemana.fromMap(data)).toList();
  }

  @override
  Stream<List<KardexSemana>> watchSemanas(String alumnoId) {
    return _databaseService.streamCollection(
      'kardex/$alumnoId/semanas',
    ).map((snapshots) => snapshots
        .map(KardexSemana.fromMap)
        .toList());
  }

  @override
  Stream<Kardex?> watchKardex(String alumnoId) {
    return _databaseService.streamDocument('kardex/$alumnoId')
        .map((snapshot) => snapshot != null ? Kardex.fromMap(snapshot) : null);
  }

  @override
  Stream<List<CostoAdicional>> watchCostosAdicionales(String alumnoId) {
    return _databaseService.streamCollection(
      'kardex/$alumnoId/costos_adicionales',
    ).map((snapshots) => snapshots
        .map(CostoAdicional.fromMap)
        .toList());
  }

  @override
  Stream<List<DetallePagoUnificado>> watchDetallesPagos(String alumnoId) {
    return _databaseService.streamCollection('kardex/$alumnoId/detalles_pago')
        .map((snapshots) => snapshots.map(DetallePagoUnificado.fromMap).toList());
  }

  @override
  Future<List<DetallePagoUnificado>> getDetallesPago(String alumnoId) async {
    final (dataList, _) = await _databaseService.getCollection(
      path: 'kardex/$alumnoId/detalles_pago',
    );
    if (dataList.isEmpty) {
      return [];
    }
    return dataList.map((data) => DetallePagoUnificado.fromMap(data)).toList();
  }

  @override
  Future<List<CostoAdicional>> getCostosAdicionales(String alumnoId) async {
    final (dataList, _) = await _databaseService.getCollection(
      path: 'kardex/$alumnoId/costos_adicionales',
    );
    return dataList.map((data) => CostoAdicional.fromMap(data)).toList();
  }

  @override
  Future<void> guardarDetallePago(DetallePagoUnificado detalle) async {
    await _databaseService.saveDocument(
      'kardex/${detalle.alumnoId}/detalles_pago/${detalle.id}',
      detalle.toMap(),
    );
  }

  @override
  Future<void> actualizarDetallePago(DetallePagoUnificado detalle) async {
    await _databaseService.saveDocument(
      'kardex/${detalle.alumnoId}/detalles_pago/${detalle.id}',
      detalle.toMap(),
      merge: true,
    );
  }

  @override
  Future<void> actualizarCostoAdicional(CostoAdicional costo) async {
    await _databaseService.saveDocument(
      'kardex/${costo.kardexId}/costos_adicionales/${costo.id}',
      costo.toMap(),
      merge: true,
    );
  }

  @override
  Future<void> deleteKardexSemana(String alumnoId, String semanaId) async {
    await _databaseService.deleteDocument('kardex/$alumnoId/semanas/$semanaId');
  }

  @override
  Future<void> guardarReciboEnKardex(String alumnoId, Recibo recibo) async {
    await _databaseService.saveDocument(
      'kardex/$alumnoId/recibos/${recibo.id}',
      recibo.toMap(),
    );
  }
}
