import 'package:beamer/beamer.dart';
import 'package:flutter/material.dart';
import 'package:gemini_app/app/presentation/alumno_detalle/alumno_detalle.page.dart';
import 'package:provider/provider.dart';
import 'package:get/get.dart';
import 'package:gemini_app/app/presentation/instituto/instituto.page.dart';
import 'package:gemini_app/app/presentation/instituto/vistas/estilismo_diseno.dart';
import 'package:gemini_app/app/presentation/detalle_recibo/detalle_recibo.page.dart';
import 'package:gemini_app/app/presentation/panel_admin/panel_administrativo.pagina.dart';
import 'package:gemini_app/app/providers/auth_provider.dart';
import 'package:get_it/get_it.dart';
import 'package:gemini_app/app/presentation/login/login.page.dart';
import 'package:gemini_app/app/presentation/instituto/vistas/tecnico_computacion.dart';
import 'package:gemini_app/app/presentation/lista_alumnos/lista_alumnos.binding.dart';
import 'package:gemini_app/app/presentation/alumno_detalle/alumno_detalle.binding.dart';
import 'package:gemini_app/di/injection.dart';
import '../domain/repositories/recibo_repository.dart';
import '../domain/modelos/recibo.dart';

// Ubicación principal (Home)
class HomeLocation extends BeamLocation<BeamState> {
  @override
  List<String> get pathPatterns => ['/'];

  @override
  List<BeamPage> buildPages(BuildContext context, BeamState state) {
    return [
      const BeamPage(
        key: ValueKey('home'),
        title: 'Instituto - Página Principal',
        child: InstitutoPage(),
      ),
    ];
  }
}

// Ubicación de Login
class LoginLocation extends BeamLocation<BeamState> {
  @override
  List<String> get pathPatterns => ['/login'];

  @override
  List<BeamPage> buildPages(BuildContext context, BeamState state) {
    final redirectTo = state.queryParameters['redirectTo'] ?? '/admin';

    return [
      BeamPage(
        key: const ValueKey('login'),
        title: 'Iniciar Sesión',
        child: LoginPage(),
      ),
    ];
  }
}

class AdminLocation extends BeamLocation<BeamState> {
  @override
  List<String> get pathPatterns => [
    '/admin',
    '/admin/alumnos/:id',
    '/admin/alumnos/:alumnoId/recibos/:reciboId',
  ];

  @override
  List<BeamPage> buildPages(BuildContext context, BeamState state) {
    // Use context.watch to rebuild when auth changes
    final auth = context.watch<ProveedorAutenticacion>();

    // Handle authentication states: loading, authenticated, unauthenticated
    if (auth.cargando) {
      // Show loading indicator while auth state is being determined
      return [
        const BeamPage(
          key: ValueKey('admin-loading'),
          child: Scaffold(body: Center(child: CircularProgressIndicator())),
        ),
      ];
    }

    if (!auth.estaAutenticado) {
      // Only redirect when authentication is confirmed to be false
      WidgetsBinding.instance.addPostFrameCallback((_) {
        Beamer.of(context).beamToNamed('/login?redirectTo=/admin');
      });
      return [
        const BeamPage(
          key: ValueKey('admin-redirect'),
          child: Scaffold(body: Center(child: CircularProgressIndicator())),
        ),
      ];
    }

    final pages = [
      BeamPage(
        key: const ValueKey('admin'),
        title: 'Panel de Administración',
        child: const PanelAdministrativoPage(),
      ),
    ];

    if (state.uri.pathSegments.length >= 3) {
      if (state.uri.pathSegments[1] == 'alumnos') {
        final studentId = state.uri.pathSegments[2];
        if (studentId.isEmpty) {
          // Redirigir a la página de administración si el ID está vacío
          return [
            const BeamPage(
              key: ValueKey('admin-home'),
              child: PanelAdministrativoPage(),
            ),
          ];
        }

        // Check if this is a receipt details route
        if (state.uri.pathSegments.length >= 5 &&
            state.uri.pathSegments[3] == 'recibos') {
          final reciboId = state.uri.pathSegments[4];
          final reciboRepo = GetIt.instance<ReciboRepository>();
          pages.addAll([
            // Student details page
            BeamPage(
              key: ValueKey('student-details-$studentId'),
              title: 'Detalles del Alumno',
              child: Builder(
                builder: (context) {
                  DetalleAlumnoBinding().dependencies();
                  return DetalleAlumnoPage(studentId: studentId);
                },
              ),
            ),
            // Receipt details page
            BeamPage(
              key: ValueKey('receipt-details-$reciboId'),
              title: 'Detalles del Recibo',
              child: FutureBuilder<Recibo>(
                future: getIt<ReciboRepository>().getReciboById(reciboId),
                builder: (context, snapshot) {
                  if (snapshot.connectionState == ConnectionState.waiting) {
                    return const Scaffold(
                      body: Center(child: CircularProgressIndicator()),
                    );
                  }

                  if (snapshot.hasError) {
                    return const Scaffold(
                      body: Center(child: Text('Error al cargar el recibo')),
                    );
                  }

                  if (!snapshot.hasData) {
                    return const Scaffold(
                      body: Center(child: Text('No se encontró el recibo')),
                    );
                  }

                  return DetalleReciboPage(recibo: snapshot.data!);
                },
              ),
            ),
          ]);
        } else {
          // Regular student details route
          pages.add(
            BeamPage(
              key: ValueKey('student-details-$studentId'),
              title: 'Detalles del Alumno',
              child: Builder(
                builder: (context) {
                  DetalleAlumnoBinding().dependencies();
                  return DetalleAlumnoPage(studentId: studentId);
                },
              ),
            ),
          );
        }
      }
    }

    return pages;
  }
}


// Ubicación para Técnico en Computación
class TecnicoComputacionLocation extends BeamLocation<BeamState> {
  @override
  List<String> get pathPatterns => ['/tecnico-computacion'];

  @override
  List<BeamPage> buildPages(BuildContext context, BeamState state) {
    return [
      const BeamPage(
        key: ValueKey('home'),
        title: 'Instituto - Página Principal',
        child: InstitutoPage(),
      ),
      const BeamPage(
        key: ValueKey('tecnico-computacion'),
        title: 'Técnico en Computación',
        child: TecnicoComputacionPage(),
      ),
    ];
  }
}


// Ubicación para Estilismo y Diseño de Imagen
class EstilismoLocation extends BeamLocation<BeamState> {
  @override
  List<String> get pathPatterns => ['/estilismo-diseno'];

  @override
  List<BeamPage> buildPages(BuildContext context, BeamState state) {
    return [
      const BeamPage(
        key: ValueKey('home'),
        title: 'Instituto - Página Principal',
        child: InstitutoPage(),
      ),
      const BeamPage(
        key: ValueKey('estilismo-diseno'),
        title: 'Estilismo y Diseño de Imagen',
        child: EstilismoDisenoDePage(),
      ),
    ];
  }
}

// Ubicación para rutas no encontradas
class NotFoundLocation extends BeamLocation<BeamState> {
  @override
  List<String> get pathPatterns => ['/404'];

  @override
  List<BeamPage> buildPages(BuildContext context, BeamState state) {
    return [
      BeamPage(
        key: const ValueKey('not-found'),
        title: 'Página no encontrada',
        child: Scaffold(
          appBar: AppBar(title: const Text('Página no encontrada')),
          body: Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Text(
                  'La página que buscas no existe',
                  style: TextStyle(fontSize: 20),
                ),
                SizedBox(height: 20),
                ElevatedButton(
                  onPressed: () => Beamer.of(context).beamToNamed('/'),
                  child: Text('Volver al inicio'),
                ),
              ],
            ),
          ),
        ),
      ),
    ];
  }
}

