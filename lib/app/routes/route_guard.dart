import 'package:flutter/material.dart';
import 'package:gemini_app/app/providers/auth_provider.dart';
import 'package:get/get.dart';
import 'package:provider/provider.dart';

class GuardiaRuta extends GetMiddleware {
  @override
  RouteSettings? redirect(String? ruta) {
    final proveedorAuth = Get.context!.read<ProveedorAutenticacion>();

    // Si aún no se ha inicializado el estado de autenticación, no hacer nada por ahora
    if (!proveedorAuth.estaInicializado) {
      return null;
    }

    // Si no está autenticado, redirigir al login con la ruta de retorno
    if (!proveedorAuth.estaAutenticado) {
      return RouteSettings(
        name: '/login',
        arguments: {'redirectTo': ruta},
      );
    }

    // Si está autenticado, permitir el acceso
    return null;
  }
}
