import 'package:beamer/beamer.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:provider/provider.dart';
import '../../providers/auth_provider.dart';
import 'widgets/contacto_section.dart';
import 'widgets/footer.dart';

class InstitutoPage extends StatefulWidget {
  const InstitutoPage({super.key});

  @override
  State<InstitutoPage> createState() => _InstitutoPageState();
}

class _InstitutoPageState extends State<InstitutoPage>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _fadeAnimation;
  late List<Animation<Offset>> _slideAnimations;
  late Animation<double> _contactFadeAnimation;

  @override
  void initState() {
    super.initState();

    _controller = AnimationController(
      vsync: this,
      duration: const Duration(seconds: 2),
    );

    // Animación para el título
    _fadeAnimation = Tween<double>(begin: 0, end: 1).animate(
      CurvedAnimation(
        parent: _controller,
        curve: const Interval(0.0, 0.5, curve: Curves.easeInOut),
      ),
    );

    // Animaciones para las imágenes (invertidas)
    _slideAnimations = List.generate(5, (index) {
      final bool isLeft = index % 2 == 0;
      return Tween<Offset>(
        begin: Offset(isLeft ? 1.5 : -1.5, 0), // Invertimos las direcciones
        end: Offset.zero,
      ).animate(
        CurvedAnimation(
          parent: _controller,
          curve: Interval(0.3 + (0.15 * index), 1, curve: Curves.easeOut),
        ),
      );
    });

    // Animación para la sección de contacto
    _contactFadeAnimation = Tween<double>(begin: 0, end: 1).animate(
      CurvedAnimation(
        parent: _controller,
        curve: const Interval(0.7, 1.0, curve: Curves.easeIn),
      ),
    );

    _controller.forward();
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  // Función para mostrar el modal
  void _showModal(BuildContext context, String title, String content) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: Text(
            title,
            style: const TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
          ),
          content: SingleChildScrollView(
            child: Text(content, style: const TextStyle(fontSize: 16)),
          ),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
              },
              child: const Text("Cerrar"),
            ),
          ],
        );
      },
    );
  }

  // Función para manejar clic en el título
  void _handleTitleClick(BuildContext context) {
    final authProvider = Provider.of<ProveedorAutenticacion>(
      context,
      listen: false,
    );

    // Si aún está cargando, no hacer nada
    if (authProvider.cargando) {
      return;
    }

    // Navegar al admin - el RouteGuard manejará la autenticación
    context.beamToNamed('/admin');
  }

  // Dentro de la clase _HomePageImiState
  @override
  Widget build(BuildContext context) {
    List<Map<String, dynamic>> images = [
      {
        'asset': 'assets/tecnico.png',
        'title': 'TÉCNICO EN COMPUTACIÓN',
        'content':
            '**Perfil de Ingreso:** Si te apasiona la tecnología, eres curioso, te gusta resolver problemas y buscas una carrera corta con amplio campo laboral, ¡la carrera técnica en computación es para ti!💻\n\n'
            '**Perfil de Egreso:** Serás un profesional capaz de instalar, configurar, mantener y reparar equipos de cómputo. Desarrollarás habilidades en programación, redes y soporte técnico, listo para enfrentar los retos del mundo digital.🧑‍💻\n\n'
            '**Futuro Laboral:** Podrás trabajar en empresas de cualquier sector como técnico de soporte, administrador de redes, programador web, o emprender tu propio negocio. ¡El límite lo pones tú!💼',
        'route': () => context.beamToNamed('/tecnico-computacion'),
      },
      {
        'asset': 'assets/estilismo.png',
        'title': 'ESTILISMO Y DISEÑO DE IMAGEN',
        'content':
            '**Perfil de Ingreso:** ¿Te gusta la moda, el maquillaje y potenciar la belleza? Si eres creativo, te apasiona el cuidado personal y buscas expresarte a través del estilismo, ¡esta carrera es para ti! 💇‍♀️\n\n'
            '**Perfil de Egreso:** Serás un profesional capaz de realizar cortes, peinados, maquillaje, manicura, pedicura y diseño de imagen. Dominarás las últimas tendencias y técnicas para realzar la belleza de tus clientes. 💅\n\n'
            '**Futuro Laboral:** Podrás trabajar en salones de belleza, spas, centros de estética o emprender tu propio negocio. ¡Destaca en la industria de la belleza y la moda! ✨',
        'route': () => context.beamToNamed('/estilismo-diseno'),
      },
      {
        'asset': 'assets/contador.png',
        'title': 'CONTADOR TÉCNICO CON COMPUTACIÓN',
        'content':
            '**Perfil de Ingreso:** ¿Te gustan los números, el orden y la precisión? Si eres analítico, responsable y buscas una carrera con gran demanda laboral, ¡la carrera de Contador Técnico con Computación es ideal para ti! 🧮\n\n'
            '**Perfil de Egreso:** Serás un profesional capaz de llevar la contabilidad de empresas, realizar declaraciones fiscales y manejar software contable. Dominarás los aspectos financieros y fiscales que toda organización necesita. 📊\n\n'
            '**Futuro Laboral:** Podrás trabajar en despachos contables, departamentos financieros de empresas o como consultor independiente. ¡Una profesión con excelentes oportunidades laborales! 💰',
        'route': () => Get.toNamed('/contador-tecnico'),
      },
      {
        'asset': 'assets/bachillerato1.png',
        'title': 'BACHILLERATO NO ESCOLARIZADO',
        'content':
            '**Perfil de Ingreso:** ¿Necesitas terminar tu bachillerato y buscas flexibilidad para terminarlo en máximo 18 meses o menos? Si tienes responsabilidades laborales o personales que te impiden asistir a clases entre semana, ¡el Bachillerato No Escolarizado es tu solución! 📚\n\n'
            '**Perfil de Egreso:** Obtendrás tu certificado de bachillerato con validez oficial, abriendo las puertas a la universidad, a mejores oportunidades laborales y a un futuro más prometedor. 🎓\n\n'
            '**Futuro Laboral:** Ampliarás tus horizontes profesionales. Podrás acceder a estudios universitarios, a puestos de trabajo que requieren bachillerato e incrementar tus posibilidades de crecimiento personal y profesional. 🚀',
      },
      {
        'asset': 'assets/bachillerato2.png',
        'title': 'BACHILLERATO GENERAL ESCOLARIZADO',
        'content':
            '**Perfil de Ingreso:** Si buscas una formación integral y flexible que te permita continuar con tus estudios universitarios, el Bachillerato General Escolarizado es para ti. 📖\n\n'
            '**Perfil de Egreso:** Al terminar, habrás desarrollado habilidades académicas y sociales que te prepararán para la educación superior o el mundo laboral. 🎒\n\n'
            '**Futuro Laboral:** Podrás acceder a estudios universitarios o incorporarte al mercado laboral con una base sólida de conocimientos generales. 🌟',
      },
    ];

    return Scaffold(
      backgroundColor: const Color(0xFFB62F30),
      body: Center(
        child: ConstrainedBox(
          constraints: const BoxConstraints(maxWidth: 900),
          child: SingleChildScrollView(
            child: Column(
              children: [
                // Título con fade animation y detector de clics
                FadeTransition(
                  opacity: _fadeAnimation,
                  child: Padding(
                    padding: const EdgeInsets.symmetric(vertical: 10),
                    child: Builder(
                      builder: (innerContext) {
                        return GestureDetector(
                          onTap: () => _handleTitleClick(innerContext),
                          child: Image.asset(
                            'assets/title.png',
                            fit: BoxFit.cover,
                          ),
                        );
                      },
                    ),
                  ),
                ),
                const SizedBox(height: 20),

                // Imágenes con animación alternada e invertida
                for (int i = 0; i < images.length; i++)
                  SlideTransition(
                    position: _slideAnimations[i],
                    child: Padding(
                      padding: EdgeInsets.only(
                        left: i % 2 == 0 ? 100 : 0,
                        right: i % 2 != 0 ? 100 : 0,
                        top: 10,
                        bottom: 10,
                      ),
                      child: MouseRegion(
                        cursor: SystemMouseCursors.click,
                        child: GestureDetector(
                          onTap:
                              i == 0 || i == 1
                                  ? images[i]['route']
                                  : () => _showModal(
                                    context,
                                    images[i]['title']!,
                                    images[i]['content']!,
                                  ),
                          child: Hero(
                            tag:
                                i == 0
                                    ? 'tecnico-computacion-image'
                                    : i == 1
                                    ? 'estilismo-diseno-image'
                                    : 'image-$i',
                            child: Image.asset(
                              images[i]['asset']!,
                              fit: BoxFit.cover,
                            ),
                          ),
                        ),
                      ),
                    ),
                  ),

                const SizedBox(height: 40),

                // Sección de contacto con animación
                FadeTransition(
                  opacity: _contactFadeAnimation,
                  child: const ContactoSection(),
                ),

                const SizedBox(height: 20),

                // Footer
                const Footer(),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
