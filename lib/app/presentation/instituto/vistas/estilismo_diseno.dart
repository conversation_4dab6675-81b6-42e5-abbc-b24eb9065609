import 'package:beamer/beamer.dart';
import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';

class EstilismoDisenoDePage extends StatelessWidget {
  const EstilismoDisenoDePage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      appBar: AppBar(
        title: const Text('Estilismo y Diseño de Imagen'),
        backgroundColor: const Color(0xFFB62F30),
        foregroundColor: Colors.white,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back),
          onPressed: () => context.beamToNamed('/'),
        ),
      ),
      body: SingleChildScrollView(
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Encabezado
              Center(
                child: Column(
                  children: [
                    Hero(
                      tag: 'estilismo-diseno-image',
                      child: Image.asset('assets/estilismo.png', height: 200),
                    ),
                    const SizedBox(height: 16),
                    Text(
                      'ESTILISMO Y DISEÑO DE IMAGEN',
                      style: GoogleFonts.alfaSlabOne(
                        fontSize: 28,
                        color: const Color(0xFFB62F30),
                      ),
                      textAlign: TextAlign.center,
                    ),
                    const SizedBox(height: 8),
                    Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 16,
                        vertical: 8,
                      ),
                      decoration: BoxDecoration(
                        color: Colors.pink[100],
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: Text(
                        '¡DESATA TU CREATIVIDAD Y CONVIÉRTETE EN UN EXPERTO DEL DISEÑO DE IMAGEN! 💡💫',
                        style: const TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                        ),
                        textAlign: TextAlign.center,
                      ),
                    ),
                  ],
                ),
              ),

              const SizedBox(height: 32),

              // Descripción principal
              Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: Colors.grey[100],
                  borderRadius: BorderRadius.circular(8),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.grey.withOpacity(0.3),
                      spreadRadius: 1,
                      blurRadius: 3,
                      offset: const Offset(0, 2),
                    ),
                  ],
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      '¿Buscas una carrera emocionante y con rápida inserción laboral? Nuestra Carrera Técnica en Estilismo y Diseño de Imagen te ofrece una formación práctica y completa en solo 2 años. Adquiere las habilidades demandadas en salones de belleza, estudios de maquillaje, producciones audiovisuales y más. ¡Invierte en tu pasión y comienza a construir tu futuro profesional ahora!',
                      style: const TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 16),
                    Text(
                      '¿List@ para incursionar en una industria con infinitas posibilidades creativas y laborales? Nuestra Carrera Técnica en Estilismo y Diseño de Imagen te ofrece una formación integral y actualizada en solo 2 años. Desde técnicas clásicas hasta las últimas tendencias en coloración, barbería, maquillaje y diseño de uñas, ¡adquiere las herramientas para destacar y construir tu propio camino profesional!',
                      style: const TextStyle(fontSize: 16),
                    ),
                  ],
                ),
              ),

              const SizedBox(height: 32),

              // Programa de Estudio
              _buildSectionTitle('PROGRAMA DE ESTUDIO'),

              _buildSemesterCard('Primer Semestre:', [
                'Peinado Básico y Técnicas de Corte Fundamentales ✂️',
                'Principios de Maquillaje y Coloración del Cabello 💄',
                'Tratamiento Facial y Manicure/Pedicure 💅',
              ], Colors.pink[50]!),

              _buildSemesterCard('Segundo Semestre:', [
                'Corte Vanguardista y Barbería I 💈',
                'Maquillaje Social y Visagismo 👤',
                'Teñido y Decoloración, Ondulado Permanente',
              ], Colors.purple[50]!),

              _buildSemesterCard('Tercer Semestre:', [
                'Peinado Gala Profesional y Corte Vanguardista II 🌟',
                'Tratamiento Capilar y Diseño Capilar',
                'Maquillaje Profesional I y Color/Efectos Especiales ✨',
              ], Colors.blue[50]!),

              _buildSemesterCard('Cuarto Semestre:', [
                'Maquillaje Profesional II - Body Paint 🎭',
                'Técnicas de Extensiones y Uñas Acrílicas 💅',
                'Prácticas de Negocio y Desarrollo Humano 💼',
              ], Colors.teal[50]!),

              const SizedBox(height: 32),

              // Por qué estudiar
              _buildSectionTitle(
                '¿POR QUÉ ESTUDIAR ESTILISMO Y DISEÑO DE IMAGEN?',
              ),

              Wrap(
                spacing: 16,
                runSpacing: 16,
                children: [
                  _buildReasonChip('Desarrolla tu Talento 🎨'),
                  _buildReasonChip('Rápida Inserción Laboral 💼'),
                  _buildReasonChip('Independencia Profesional 🚀'),
                  _buildReasonChip('Conviértete en un Asesor de Imagen 👤'),
                  _buildReasonChip('Explora tu Creatividad ✨'),
                  _buildReasonChip('Actualización Constante 📚'),
                  _buildReasonChip('Formación Integral 🎓'),
                ],
              ),

              const SizedBox(height: 32),

              // Oportunidades
              _buildSectionTitle('OPORTUNIDADES PROFESIONALES'),

              _buildOpportunityCard(
                'Salones de Belleza',
                'Trabaja en prestigiosos salones o establece tu propio negocio, ofreciendo servicios de corte, peinado y coloración.',
                Icons.content_cut,
              ),
              _buildOpportunityCard(
                'Maquillaje Profesional',
                'Colabora en producciones fotográficas, desfiles de moda, eventos sociales o como maquillista personal.',
                Icons.face,
              ),
              _buildOpportunityCard(
                'Asesoría de Imagen',
                'Ayuda a personas y empresas a proyectar la mejor imagen posible, creando estilos personalizados.',
                Icons.person_outline,
              ),
              _buildOpportunityCard(
                'Industria del Entretenimiento',
                'Participa en producciones de cine, televisión, teatro y publicidad como estilista o maquillista.',
                Icons.movie_creation_outlined,
              ),

              const SizedBox(height: 32),

              // Banner final
              Container(
                width: double.infinity,
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    colors: [Colors.pink[300]!, Colors.purple[300]!],
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                  ),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Text(
                  '¡UN MUNDO DE OPORTUNIDADES CREATIVAS Y PROFESIONALES TE ESPERA!',
                  style: GoogleFonts.alfaSlabOne(
                    fontSize: 20,
                    color: Colors.white,
                  ),
                  textAlign: TextAlign.center,
                ),
              ),

              const SizedBox(height: 32),

              // Botón de inscripción
              Center(
                child: ElevatedButton(
                  onPressed: () {
                    // Acción para inscripción
                  },
                  style: ElevatedButton.styleFrom(
                    backgroundColor: const Color(0xFFB62F30),
                    foregroundColor: Colors.white,
                    padding: const EdgeInsets.symmetric(
                      horizontal: 32,
                      vertical: 16,
                    ),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(30),
                    ),
                  ),
                  child: Text(
                    'INSCRÍBETE AHORA',
                    style: GoogleFonts.alfaSlabOne(fontSize: 18),
                  ),
                ),
              ),

              const SizedBox(height: 32),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildSectionTitle(String title) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          title,
          style: GoogleFonts.alfaSlabOne(
            fontSize: 22,
            color: const Color(0xFFB62F30),
          ),
        ),
        const Divider(thickness: 2, color: Color(0xFFB62F30)),
        const SizedBox(height: 16),
      ],
    );
  }

  Widget _buildSemesterCard(String title, List<String> subjects, Color color) {
    return Card(
      margin: const EdgeInsets.only(bottom: 16),
      elevation: 4,
      color: color,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              title,
              style: const TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 8),
            ...subjects
                .map(
                  (subject) => Padding(
                    padding: const EdgeInsets.only(bottom: 8),
                    child: Row(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        const Icon(
                          Icons.check_circle,
                          size: 20,
                          color: Color(0xFFB62F30),
                        ),
                        const SizedBox(width: 8),
                        Expanded(
                          child: Text(
                            subject,
                            style: const TextStyle(fontSize: 16),
                          ),
                        ),
                      ],
                    ),
                  ),
                )
                .toList(),
          ],
        ),
      ),
    );
  }

  Widget _buildOpportunityCard(
    String title,
    String description,
    IconData icon,
  ) {
    return Card(
      margin: const EdgeInsets.only(bottom: 16),
      elevation: 3,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Icon(icon, size: 32, color: Colors.pink[400]),
            const SizedBox(width: 16),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    title,
                    style: const TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(description, style: const TextStyle(fontSize: 16)),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildReasonChip(String label) {
    return Chip(
      label: Text(
        label,
        style: const TextStyle(
          color: Colors.white,
          fontWeight: FontWeight.bold,
        ),
      ),
      backgroundColor: Colors.pink[400],
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
    );
  }
}
