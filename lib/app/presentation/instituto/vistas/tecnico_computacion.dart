import 'package:beamer/beamer.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:google_fonts/google_fonts.dart';

class TecnicoComputacionPage extends StatelessWidget {
  const TecnicoComputacionPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      appBar: AppBar(
        title: const Text('Técnico en Computación'),
        backgroundColor: const Color(0xFFB62F30),
        foregroundColor: Colors.white,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back),
            onPressed: () => context.beamToNamed('/')
        ),
      ),
      body: SingleChildScrollView(
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Encabezado
              Center(
                child: Column(
                  children: [
                    Hero(
                      tag: 'tecnico-computacion-image',
                      child: Image.asset(
                        'assets/tecnico.png',
                        height: 200,
                      ),
                    ),
                    const SizedBox(height: 16),
                    Text(
                      'TÉCNICO EN COMPUTACIÓN',
                      style: GoogleFonts.alfaSlabOne(
                        fontSize: 28,
                        color: const Color(0xFFB62F30),
                      ),
                      textAlign: TextAlign.center,
                    ),
                    const SizedBox(height: 8),
                    Container(
                      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                      decoration: BoxDecoration(
                        color: Colors.blue[100],
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: Text(
                        '¡Un Mundo de Oportunidades Creativas y Profesionales te Espera!',
                        style: const TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                        ),
                        textAlign: TextAlign.center,
                      ),
                    ),
                  ],
                ),
              ),

              const SizedBox(height: 32),

              // Programa de Estudio
              _buildSectionTitle('PROGRAMA DE ESTUDIO'),

              _buildSemesterCard(
                'Primer Semestre:',
                [
                  'Introducción a la Informática y Software de Oficina 🖱️',
                  'Introducción al Diseño Gráfico y Destrezas Mentales 🎨🧠',
                  'Inglés I e Introducción a la Calidad 🇬🇧✅',
                ],
                Colors.blue[50]!,
              ),

              _buildSemesterCard(
                'Segundo Semestre:',
                [
                  'Int. a las Redes y Conectividad y Software de Oficina Avanzado 🌐',
                  'Diseño de Sitios Web y Mantenimiento Básico 💻🛠️',
                  'Inglés II y Gestión de Calidad 🇬🇧✅',
                ],
                Colors.green[50]!,
              ),

              _buildSemesterCard(
                'Tercer Semestre:',
                [
                  'Instalación de Redes y Comunicación Organizacional 📶🗣️',
                  'Animación por Computadora y Algoritmos 🎬⚙️',
                  'Inglés III y Razonamiento Lógico 🇬🇧🧠',
                ],
                Colors.orange[50]!,
              ),

              _buildSemesterCard(
                'Cuarto Semestre:',
                [
                  'Administración de Redes y Arquitectura y Ensamble 🛡️⚙️',
                  'Fundamentos de Base de Datos y Lenguajes Estructurales y Visuales 💾👁️',
                  'Inglés IV y Matemáticas Aplicadas 🇬🇧',
                ],
                Colors.purple[50]!,
              ),

              const SizedBox(height: 32),

              // Ventajas
              _buildSectionTitle('VENTAJAS ADICIONALES'),

              Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: Colors.grey[100],
                  borderRadius: BorderRadius.circular(8),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.grey.withOpacity(0.3),
                      spreadRadius: 1,
                      blurRadius: 3,
                      offset: const Offset(0, 2),
                    ),
                  ],
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Puebla cuenta con un sector industrial en crecimiento, así como un número importante de pequeñas y medianas empresas que requieren soporte técnico y presencia en línea. Además, la presencia de instituciones educativas genera demanda de técnicos en informática para su infraestructura.',
                      style: const TextStyle(fontSize: 16),
                    ),
                    const SizedBox(height: 16),
                    Text(
                      'En resumen, estudiar como Técnico en Computación te proporcionará habilidades prácticas y conocimientos fundamentales para ingresar a un mercado laboral dinámico y con amplias oportunidades de crecimiento. ¡Es una excelente opción si te apasiona la tecnología y buscas una carrera con futuro!',
                      style: const TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
                    ),
                  ],
                ),
              ),

              const SizedBox(height: 32),

              // Oportunidades
              _buildSectionTitle('OPORTUNIDADES'),

              _buildOpportunityCard('Rápida Inserción Laboral', 'Los programas técnicos suelen ser más cortos y enfocados en habilidades prácticas, lo que facilita la entrada al mercado laboral en menos tiempo.', Icons.rocket_launch),
              _buildOpportunityCard('Salarios Competitivos', 'Los profesionales de TI son bien remunerados debido a la alta demanda.', Icons.attach_money),
              _buildOpportunityCard('Desarrollo Profesional Continuo', 'El campo de la tecnología está en constante evolución, lo que te brinda oportunidades para seguir aprendiendo y especializándote.', Icons.trending_up),
              _buildOpportunityCard('Trabajo Remoto', 'Muchas posiciones en el área de TI permiten el trabajo a distancia, ofreciendo flexibilidad.', Icons.home_work),

              const SizedBox(height: 32),

              // Por qué estudiar
              _buildSectionTitle('¿POR QUÉ ESTUDIAR TÉCNICO EN COMPUTACIÓN?'),

              Wrap(
                spacing: 16,
                runSpacing: 16,
                children: [
                  _buildReasonChip('Desarrolla Habilidades Clave 💻'),
                  _buildReasonChip('Alta Demanda Laboral 📈'),
                  _buildReasonChip('Sé un Innovador 💡'),
                  _buildReasonChip('Amplio Campo de Acción 🌐'),
                  _buildReasonChip('Desarrollo Profesional Continuo 🚀'),
                  _buildReasonChip('Inglés como Herramienta Fundamental 🇬🇧'),
                  _buildReasonChip('Formación Integral ⚙️'),
                ],
              ),

              const SizedBox(height: 32),

              // Botón de inscripción
              Center(
                child: ElevatedButton(
                  onPressed: () {
                    // Acción para inscripción
                  },
                  style: ElevatedButton.styleFrom(
                    backgroundColor: const Color(0xFFB62F30),
                    foregroundColor: Colors.white,
                    padding: const EdgeInsets.symmetric(horizontal: 32, vertical: 16),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(30),
                    ),
                  ),
                  child: Text(
                    'INSCRÍBETE AHORA',
                    style: GoogleFonts.alfaSlabOne(fontSize: 18),
                  ),
                ),
              ),

              const SizedBox(height: 32),

              // Botón para regresar
              Center(
                child: OutlinedButton.icon(
                  onPressed: () => Get.offNamed('/'),
                  icon: const Icon(Icons.arrow_back),
                  label: const Text('REGRESAR'),
                  style: OutlinedButton.styleFrom(
                    foregroundColor: const Color(0xFFB62F30),
                    side: const BorderSide(color: Color(0xFFB62F30)),
                    padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(30),
                    ),
                  ),
                ),
              ),

              const SizedBox(height: 16),
            ],
          ),
        ),
      ),
    );
  }
  
  Widget _buildSectionTitle(String title) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          title,
          style: GoogleFonts.alfaSlabOne(
            fontSize: 22,
            color: const Color(0xFFB62F30),
          ),
        ),
        const Divider(thickness: 2, color: Color(0xFFB62F30)),
        const SizedBox(height: 16),
      ],
    );
  }
  
  Widget _buildSemesterCard(String title, List<String> subjects, Color color) {
    return Card(
      margin: const EdgeInsets.only(bottom: 16),
      elevation: 4,
      color: color,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              title,
              style: const TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 8),
            ...subjects.map((subject) => Padding(
              padding: const EdgeInsets.only(bottom: 8),
              child: Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Icon(Icons.check_circle, size: 20, color: Color(0xFFB62F30)),
                  const SizedBox(width: 8),
                  Expanded(child: Text(subject, style: const TextStyle(fontSize: 16))),
                ],
              ),
            )).toList(),
          ],
        ),
      ),
    );
  }
  
  Widget _buildOpportunityCard(String title, String description, IconData icon) {
    return Card(
      margin: const EdgeInsets.only(bottom: 16),
      elevation: 3,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Icon(icon, size: 32, color: const Color(0xFFB62F30)),
            const SizedBox(width: 16),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    title,
                    style: const TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    description,
                    style: const TextStyle(fontSize: 16),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
  
  Widget _buildReasonChip(String label) {
    return Chip(
      label: Text(
        label,
        style: const TextStyle(
          color: Colors.white,
          fontWeight: FontWeight.bold,
        ),
      ),
      backgroundColor: const Color(0xFFB62F30),
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
    );
  }
}