import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:url_launcher/url_launcher.dart';

class ContactoSection extends StatelessWidget {
  const ContactoSection({super.key});

  final LatLng _tepetlacaCoordinates = const LatLng(18.9653306, -97.8990621);

  // Función para abrir Google Maps con la ubicación
  Future<void> _launchMapsUrl() async {
    final url = Uri.parse(
      'https://www.google.com/maps/dir/?api=1&destination=18.9653306,-97.8990621&travelmode=driving',
    );
    if (!await launchUrl(url, mode: LaunchMode.externalApplication)) {
      throw Exception('No se pudo abrir el mapa: $url');
    }
  }

  // Función para iniciar una llamada telefónica
  Future<void> _launchPhoneCall() async {
    final url = Uri.parse('tel:2231110213');
    if (!await launchUrl(url)) {
      throw Exception('No se pudo iniciar la llamada: $url');
    }
  }

  // Función para abrir WhatsApp
  Future<void> _launchWhatsApp() async {
    final url = Uri.parse('https://wa.me/522221921216');
    if (!await launchUrl(url, mode: LaunchMode.externalApplication)) {
      throw Exception('No se pudo abrir WhatsApp: $url');
    }
  }

  // Función para enviar correo electrónico
  Future<void> _launchEmail() async {
    final url = Uri.parse('mailto:<EMAIL>');
    if (!await launchUrl(url)) {
      throw Exception('No se pudo abrir el cliente de correo: $url');
    }
  }

  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: 4,
      color: Colors.white,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
      child: Padding(
        padding: const EdgeInsets.all(24),
        child: Column(
          children: <Widget>[
            Text(
              'Contacto',
              style: GoogleFonts.alfaSlabOne(
                fontSize: 36,
                fontWeight: FontWeight.bold,
                color: Colors.red[700],
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 16),
            const Text(
              'Visítanos en nuestras instalaciones o contáctanos por teléfono o correo electrónico.',
              style: TextStyle(fontSize: 18, color: Colors.black87),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 24),
            Row(
              children: <Widget>[
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: <Widget>[
                      const Text(
                        'Dirección:',
                        style: TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                          color: Colors.black87,
                        ),
                      ),
                      InkWell(
                        onTap: _launchMapsUrl,
                        child: const Text(
                          '4 SUR 108 TEPEACA, PUE.',
                          style: TextStyle(
                            fontSize: 16,
                            color: Colors.blue,
                            decoration: TextDecoration.underline,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
                const SizedBox(width: 20),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: <Widget>[
                      const Text(
                        'Teléfono:',
                        style: TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                          color: Colors.black87,
                        ),
                      ),
                      InkWell(
                        onTap: _launchPhoneCall,
                        child: const Text(
                          '************',
                          style: TextStyle(
                            fontSize: 16,
                            color: Colors.blue,
                            decoration: TextDecoration.underline,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
            const SizedBox(height: 24),
            Row(
              children: [
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Text(
                        'WhatsApp:',
                        style: TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                          color: Colors.black87,
                        ),
                      ),
                      InkWell(
                        onTap: _launchWhatsApp,
                        child: const Text(
                          '222 192 1216',
                          style: TextStyle(
                            fontSize: 16,
                            color: Colors.green,
                            decoration: TextDecoration.underline,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Text(
                        'Correo Electrónico:',
                        style: TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                          color: Colors.black87,
                        ),
                      ),
                      InkWell(
                        onTap: _launchEmail,
                        child: const Text(
                          '<EMAIL>',
                          style: TextStyle(
                            fontSize: 16,
                            color: Colors.blue,
                            decoration: TextDecoration.underline,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
            const SizedBox(height: 24),
            const Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Horario de atención:',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: Colors.black87,
                  ),
                ),
                SizedBox(height: 8),
                Text(
                  'Lunes a Viernes: 8:00 AM - 5:00 PM',
                  style: TextStyle(
                    fontSize: 16,
                    color: Colors.black54,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                SizedBox(height: 4),
                Text(
                  'Sábado y Domingo: 9:00 AM - 1:00 PM',
                  style: TextStyle(
                    fontSize: 16,
                    color: Colors.black54,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 24),
            Stack(
              children: [
                SizedBox(
                  width: double.infinity,
                  height: 400,
                  child: ClipRRect(
                    borderRadius: BorderRadius.circular(8),
                    child: GoogleMap(
                      initialCameraPosition: CameraPosition(
                        target: _tepetlacaCoordinates,
                        zoom: 15,
                      ),
                      markers: {
                        Marker(
                          markerId: const MarkerId('tepetlaca'),
                          position: _tepetlacaCoordinates,
                          infoWindow: const InfoWindow(
                            title: 'Instituto Mexicano Tepeaca',
                            snippet: '4 SUR 108 TEPEACA, PUE.',
                          ),
                        ),
                      },
                      onTap: (_) => _launchMapsUrl(),
                    ),
                  ),
                ),
                Positioned(
                  right: 16,
                  bottom: 16,
                  child: ElevatedButton.icon(
                    onPressed: _launchMapsUrl,
                    icon: const Icon(Icons.directions),
                    label: const Text("Cómo llegar"),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.red[600],
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(
                        horizontal: 16,
                        vertical: 12,
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}