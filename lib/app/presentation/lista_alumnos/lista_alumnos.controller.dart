import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:gemini_app/app/presentation/lista_alumnos/casos_de_uso/obtener_alumnos_paginados.caso_uso.dart';
import 'package:get/get.dart';
import 'package:gemini_app/app/domain/entities/alumno.dart';
import 'dart:async';

class AlumnosController extends GetxController {
  final StreamAlumnosPorBusquedaCasoUso _casoUso;

  // Estado reactivo
  final alumnos = <Alumno>[].obs;
  final cargando = false.obs;
  final error = Rx<String?>(null);
  final searchQuery = ''.obs;
  final hayMas = true.obs;
  final ultimoDoc = Rx<DocumentSnapshot?>(null);
  final _subscriptions = <StreamSubscription>[]; // Lista de suscripciones
  Timer? _debounceTimer;
  int _currentPage = 0; // Contador de páginas cargadas
  final int _limite = 10; // Límite por página

  AlumnosController(this._casoUso);

  @override
  void onInit() {
    super.onInit();
    _subscribeToStream(page: 0);
  }

  @override
  void onClose() {
    _subscriptions.forEach((sub) => sub.cancel());
    _subscriptions.clear();
    _debounceTimer?.cancel();
    super.onClose();
  }

  void actualizarBusqueda(String query) {
    final trimmedQuery = query.toLowerCase().trim();
    if (trimmedQuery == searchQuery.value) return;

    _debounceTimer?.cancel();
    _debounceTimer = Timer(const Duration(milliseconds: 500), () {
      if (!Get.isRegistered<AlumnosController>()) return;

      searchQuery.value = trimmedQuery;
      reiniciar();
    });
  }

  void _subscribeToStream({required int page}) {
    cargando.value = true;
    error.value = null;

    final subscription = _casoUso
        .ejecutar(
          searchQuery: searchQuery.value,
          limite: _limite,
          startAfter: page > 0 ? ultimoDoc.value : null,
        )
        .listen(
          (data) {
            final nuevosAlumnos = data.$1;
            ultimoDoc.value = data.$2;

            if (page == 0) {
              alumnos.value = nuevosAlumnos;
            } else {
              alumnos.addAll(nuevosAlumnos);
            }

            cargando.value = false;
            hayMas.value = nuevosAlumnos.length >= _limite;
          },
          onError: (e) {
            error.value = 'Error al cargar alumnos: $e';
            cargando.value = false;
          },
        );

    _subscriptions.add(subscription);
  }

  void cargarMasAlumnos() {
    if (cargando.value || !hayMas.value) return;

    _currentPage++;
    _subscribeToStream(page: _currentPage);
  }

  void reiniciar() {
    _subscriptions.forEach((sub) => sub.cancel());
    _subscriptions.clear();
    alumnos.clear();
    ultimoDoc.value = null;
    hayMas.value = true;
    _currentPage = 0;
    _subscribeToStream(page: 0);
  }

  void reintentar() {
    reiniciar();
  }
}