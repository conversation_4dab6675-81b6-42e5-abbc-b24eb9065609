import 'package:gemini_app/app/presentation/lista_alumnos/casos_de_uso/obtener_alumnos_paginados.caso_uso.dart';
import 'package:get/get.dart';
import 'package:gemini_app/di/injection.dart';
import 'lista_alumnos.controller.dart';

class ListaAlumnosBinding implements Bindings {
  @override
  void dependencies() {
    Get.lazyPut<AlumnosController>(
      () => AlumnosController(getIt<StreamAlumnosPorBusquedaCasoUso>()),
      fenix: true,
    );
  }

  @override
  void close() {
    Get.delete<AlumnosController>();
  }
}