import 'package:flutter/material.dart';
import 'package:gemini_app/app/presentation/lista_alumnos/casos_de_uso/obtener_alumnos_paginados.caso_uso.dart';
import 'package:get/get.dart';
import 'package:gemini_app/di/injection.dart';
import 'lista_alumnos.controller.dart';
import 'widgets/busqueda_alumnos.section.dart';
import 'widgets/lista_alumnos.item.dart';
import 'widgets/lista_alumnos.header.dart';
import 'widgets/lista_alumnos_footer.dart';
import '../../styles/app_styles.dart';

class ListaAlumnosPagina extends GetView<AlumnosController> {
  ListaAlumnosPagina({super.key}) {
    Get.put(AlumnosController(getIt<StreamAlumnosPorBusquedaCasoUso>()));
  }

  @override
  Widget build(BuildContext context) {
    return Obx(() => _buildBody(context));
  }

  Widget _buildBody(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16.0),
      color: AppStyles.backgroundColor,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              const Icon(
                Icons.school_outlined,
                size: 28,
                color: AppStyles.primaryColor,
              ),
              const SizedBox(width: 12),
              Text('Lista de Alumnos', style: AppStyles.titleStyle),
            ],
          ),
          const SizedBox(height: 24),
          BusquedaAlumnos(controller: controller),
          const SizedBox(height: 24),
          
          if (controller.error.value != null)
            Padding(
              padding: const EdgeInsets.only(bottom: 16),
              child: Column(
                children: [
                  Text(
                    controller.error.value!,
                    style: AppStyles.errorText,
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(height: 8),
                  ElevatedButton(
                    onPressed: controller.reintentar,
                    child: const Text('Reintentar'),
                  ),
                ],
              ),
            ),
          
          Expanded(
            child: controller.cargando.value && controller.alumnos.isEmpty
                ? const Center(child: CircularProgressIndicator())
                : Card(
                    elevation: 0,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(16),
                      side: BorderSide(
                        color: AppStyles.borderColor,
                        width: 1,
                      ),
                    ),
                    child: Column(
                      children: [
                        const ListaAlumnosHeader(),
                        if (controller.alumnos.isNotEmpty)
                          Expanded(
                            child: NotificationListener<ScrollNotification>(
                              onNotification: (scrollInfo) {
                                if (scrollInfo.metrics.pixels >=
                                    scrollInfo.metrics.maxScrollExtent * 0.8) {
                                  controller.cargarMasAlumnos();
                                }
                                return false;
                              },
                              child: ListView.separated(
                                itemCount: controller.alumnos.length,
                                separatorBuilder: (context, index) => Divider(
                                  height: 1,
                                  color: AppStyles.borderColor,
                                  thickness: 1,
                                ),
                                itemBuilder: (context, index) {
                                  final alumno = controller.alumnos[index];
                                  return ListaAlumnosItem(
                                    alumno: alumno,
                                    index: index,
                                    context: context,
                                  );
                                },
                              ),
                            ),
                          )
                        else
                          const Center(child: Text('No hay alumnos disponibles')),
                      ],
                    ),
                  ),
          ),
          const SizedBox(height: 16),
          ListaAlumnosFooter(controller: controller),
        ],
      ),
    );
  }
}