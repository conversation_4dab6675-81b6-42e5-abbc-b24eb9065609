import 'package:flutter/material.dart';
import 'package:beamer/beamer.dart';
import 'package:google_fonts/google_fonts.dart';
import '../../../styles/app_styles.dart';
import '../lista_alumnos.styles.dart';
import '../../../domain/entities/alumno.dart';

class ListaAlumnosItem extends StatelessWidget {
  final Alumno alumno;
  final int index;
  final BuildContext context;

  const ListaAlumnosItem({
    super.key,
    required this.alumno,
    required this.index,
    required this.context,
  });

  @override
  Widget build(BuildContext context) {
    final initial =
        alumno.nombre.isNotEmpty ? alumno.nombre[0].toUpperCase() : 'A';
    final colorIndex = index % ListaAlumnosStyles.avatarColors.length;

    return Container(
      padding: const EdgeInsets.all(16),
      child: Row(
        children: [
          Container(
            width: 40,
            height: 40,
            decoration: BoxDecoration(
              color: ListaAlumnosStyles.avatarColors[colorIndex].withOpacity(
                0.1,
              ),
              borderRadius: BorderRadius.circular(20),
            ),
            child: Center(
              child: Text(
                initial,
                style: GoogleFonts.inter(
                  color: ListaAlumnosStyles.avatarColors[colorIndex],
                  fontWeight: FontWeight.w500,
                  fontSize: 14,
                ),
              ),
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  alumno.nombreCompleto.toString(),
                  style: AppStyles.bodyStyle.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
                ),
                const SizedBox(height: 2),
                Text(
                  alumno.email ?? '<EMAIL>',
                  style: AppStyles.captionStyle,
                ),
              ],
            ),
          ),
          OutlinedButton(
            onPressed: () {
              if (alumno.id != null) {
                context.beamToNamed('/admin/alumnos/${alumno.id!}');
              }
            },
            style: AppStyles.outlineButtonStyle,
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                const Icon(
                  Icons.search,
                  size: 16,
                  color: AppStyles.primaryColor,
                ),
                const SizedBox(width: 4),
                Text(
                  'Ver',
                  style: GoogleFonts.inter(
                    fontSize: 12,
                    fontWeight: FontWeight.w500,
                    color: AppStyles.primaryColor,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
