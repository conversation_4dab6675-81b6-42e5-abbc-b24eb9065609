import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:google_fonts/google_fonts.dart';
import '../lista_alumnos.controller.dart';
import '../lista_alumnos.styles.dart';

class BusquedaAlumnos extends StatelessWidget {
  final AlumnosController controller;

  const BusquedaAlumnos({super.key, required this.controller});

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: ListaAlumnosStyles.searchSectionPadding,
      decoration: ListaAlumnosStyles.searchSectionDecoration,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Buscar alumnos',
            style: ListaAlumnosStyles.searchTitleStyle,
          ),
          const SizedBox(height: 16),
          TextField(
            onChanged: controller.actualizarBusqueda,
            decoration: InputDecoration(
              filled: true,
              fillColor: const Color(0xFFF9FAFB),
              hintText: 'Nombre, apellido paterno o materno...',
              hintStyle: GoogleFonts.inter(
                color: const Color(0xFF6B7280),
                fontSize: 14,
              ),
              prefixIcon: controller.cargando.value
                  ? const Padding(
                      padding: EdgeInsets.all(10),
                      child: SizedBox(
                        width: 16,
                        height: 16,
                        child: CircularProgressIndicator(strokeWidth: 2),
                      ),
                    )
                  : const Icon(
                      Icons.search,
                      size: 20,
                      color: Color(0xFF6B7280),
                    ),
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
                borderSide: BorderSide.none,
              ),
              contentPadding: const EdgeInsets.symmetric(
                vertical: 14,
                horizontal: 16,
              ),
            ),
          ),
          const SizedBox(height: 16),
          /*
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'Filtrar por carrera',
                style: ListaAlumnosStyles.filterLabelStyle,
              ),
              const SizedBox(height: 8),
              Container(
                decoration: ListaAlumnosStyles.dropdownDecoration,
                child: DropdownButtonFormField<String>(
                  isExpanded: true,
                  dropdownColor: Colors.white,
                  icon: const Icon(
                    Icons.arrow_drop_down,
                    color: Color(0xFF6B7280),
                  ),
                  decoration: InputDecoration(
                    contentPadding: const EdgeInsets.symmetric(
                      horizontal: 12,
                      vertical: 8,
                    ),
                    border: InputBorder.none,
                    enabledBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(8),
                      borderSide: const BorderSide(color: Colors.transparent),
                    ),
                    focusedBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(8),
                      borderSide: const BorderSide(
                        color: Color(0xFF6366F1),
                        width: 1,
                      ),
                    ),
                  ),
                  style: GoogleFonts.inter(
                    fontSize: 14,
                    color: const Color(0xFF111827),
                  ),
                  items: [
                    DropdownMenuItem<String>(
                      value: null,
                      child: Text(
                        'Todas las carreras',
                        style: GoogleFonts.inter(
                          color: const Color(0xFF6B7280),
                        ),
                      ),
                    ),
                    DropdownMenuItem<String>(
                      value: 'diseno-grafico',
                      child: Container(
                        padding: ListaAlumnosStyles.dropdownItemPadding,
                        margin: const EdgeInsets.only(bottom: 4),
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(6),
                          color: controller.selectedCarrera.value == 'Computacion'
                              ? const Color(0xFFEEF2FF)
                              : Colors.transparent,
                        ),
                        child: Row(
                          children: [
                            Icon(
                              Icons.school_outlined,
                              size: 18,
                              color: controller.selectedCarrera.value == 'Computacion'
                                  ? const Color(0xFF4F46E5)
                                  : const Color(0xFF6B7280),
                            ),
                            const SizedBox(width: 12),
                            Text(
                              'Diseno Grafico',
                              style: GoogleFonts.inter(
                                color: controller.selectedCarrera.value == 'Computacion'
                                    ? const Color(0xFF4F46E5)
                                    : const Color(0xFF111827),
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                    DropdownMenuItem<String>(
                      value: 'Estilismo',
                      child: Container(
                        padding: ListaAlumnosStyles.dropdownItemPadding,
                        margin: const EdgeInsets.only(bottom: 4),
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(6),
                          color: controller.selectedCarrera.value == 'Estilismo'
                              ? const Color(0xFFEEF2FF)
                              : Colors.transparent,
                        ),
                        child: Row(
                          children: [
                            Icon(
                              Icons.school_outlined,
                              size: 18,
                              color: controller.selectedCarrera.value == 'Estilismo'
                                  ? const Color(0xFF4F46E5)
                                  : const Color(0xFF6B7280),
                            ),
                            const SizedBox(width: 12),
                            Text(
                              'Estilismo',
                              style: GoogleFonts.inter(
                                color: controller.selectedCarrera.value == 'Estilismo'
                                    ? const Color(0xFF4F46E5)
                                    : const Color(0xFF111827),
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                  ],
                  onChanged: (value) {
                    controller.selectedCarrera.value = value.toString();
                  },
                ),
              ),
            ],
          ),
          */
        ],
      ),
    );
  }
}
