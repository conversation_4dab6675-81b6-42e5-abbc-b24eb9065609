import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:google_fonts/google_fonts.dart';
import '../../../styles/app_styles.dart';
import '../lista_alumnos.controller.dart';

class ListaAlumnosFooter extends StatelessWidget {
  final AlumnosController controller;

  const ListaAlumnosFooter({super.key, required this.controller});

  @override
  Widget build(BuildContext context) {
    return Obx(() => Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              'Mostrando ${controller.alumnos.length} alumnos${controller.hayMas.value ? '+' : ''}',
              style: AppStyles.captionStyle,
            ),
            Row(
              children: [
                if (controller.cargando.value)
                  const Padding(
                    padding: EdgeInsets.only(right: 8.0),
                    child: SizedBox(
                      width: 16,
                      height: 16,
                      child: CircularProgressIndicator(strokeWidth: 2),
                    ),
                  )
                else if (controller.hayMas.value)
                  IconButton(
                    icon: const Icon(
                      Icons.chevron_right,
                      color: AppStyles.primaryColor,
                    ),
                    onPressed: () {
                      controller.cargarMasAlumnos();
                    },
                  ),
              ],
            ),
          ],
        ));
  }
}