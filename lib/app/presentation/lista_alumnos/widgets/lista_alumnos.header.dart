import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import '../../../styles/app_styles.dart';

class ListaAlumnosHeader extends StatelessWidget {
  const ListaAlumnosHeader({super.key});

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: AppStyles.cardBackground,
        border: Border(
          bottom: BorderSide(color: AppStyles.borderColor),
        ),
        borderRadius: const BorderRadius.only(
          topLeft: Radius.circular(16),
          topRight: Radius.circular(16),
        ),
      ),
      child: Row(
        children: [
          Text(
            'Nombre',
            style: GoogleFonts.inter(
              fontSize: 12,
              fontWeight: FontWeight.w500,
              color: AppStyles.secondaryTextColor,
              letterSpacing: 0.5,
            ),
          ),
          const Spacer(),
          Text(
            'Accion',
            style: GoogleFonts.inter(
              fontSize: 12,
              fontWeight: FontWeight.w500,
              color: AppStyles.secondaryTextColor,
              letterSpacing: 0.5,
            ),
          ),
        ],
      ),
    );
  }
}