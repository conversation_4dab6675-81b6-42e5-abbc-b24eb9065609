import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:gemini_app/app/domain/entities/alumno.dart';
import 'package:gemini_app/app/domain/repositories/alumno_repositorio.dart';

class StreamAlumnosPorBusquedaCasoUso {
  final AlumnoRepository repositorio;

  StreamAlumnosPorBusquedaCasoUso(this.repositorio);

  Stream<(List<Alumno>, DocumentSnapshot?)> ejecutar({
    required String searchQuery,
    int limite = 10,
    DocumentSnapshot? startAfter,
  }) {
    return repositorio.streamAlumnosPorBusqueda(
      searchQuery: searchQuery,
      limite: limite,
      startAfter: startAfter,
    );
  }
}