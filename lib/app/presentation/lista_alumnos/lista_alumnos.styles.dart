import 'package:flutter/material.dart';

class ListaAlumnosStyles {
  // Colors
  static const List<Color> avatarColors = [
    Color(0xFF6366F1),
    Color(0xFFEC4899),
    Color(0xFF8B5CF6),
    Color(0xFF10B981),
    Color(0xFFF59E0B),
    Color(0xFF84CC16),
    Color(0xFFFF5722),
    Color(0xFF3B82F6),
    Color(0xFF8B5CF6),
    Color(0xFFEC4899),
  ];

  // Search Section
  static const searchSectionPadding = EdgeInsets.all(16);
  static const searchSectionDecoration = BoxDecoration(
    color: Colors.white,
    borderRadius: BorderRadius.all(Radius.circular(12)),
    boxShadow: [
      BoxShadow(
        color: Color(0x05000000),
        blurRadius: 6,
        offset: Offset(0, 2),
      ),
    ],
  );

  // Text Styles
  static TextStyle searchTitleStyle = TextStyle(
    fontSize: 16,
    fontWeight: FontWeight.w600,
    color: Color(0xFF111827),
    letterSpacing: -0.2,
  );

  static TextStyle filterLabelStyle = TextStyle(
    fontSize: 13,
    color: Color(0xFF6B7280),
    fontWeight: FontWeight.w500,
  );

  // Dropdown Styles
  static const dropdownDecoration = BoxDecoration(
    borderRadius: BorderRadius.all(Radius.circular(8)),
    color: Color(0xFFF9FAFB),
  );

  static const dropdownItemPadding = EdgeInsets.symmetric(
    horizontal: 12,
    vertical: 10,
  );
}