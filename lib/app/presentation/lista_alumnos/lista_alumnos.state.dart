import 'package:cloud_firestore/cloud_firestore.dart';

import '../../domain/entities/alumno.dart';
import '../../domain/modelos/alumno.model.dart';

class AlumnosState {
  final List<dynamic> alumnos; // Puede ser List<Alumno> o List<AlumnoModel>
  final DocumentSnapshot? ultimoDoc;
  final bool hayMas;
  final bool cargando;
  final String searchQuery; // Nueva propiedad para la búsqueda

  AlumnosState({
    required this.alumnos,
    required this.ultimoDoc,
    required this.hayMas,
    required this.cargando,
    required this.searchQuery, // Añadido
  });

  factory AlumnosState.inicial() => AlumnosState(
    alumnos: [],
    ultimoDoc: null,
    hayMas: true,
    cargando: false,
    searchQuery: '', // Inicializado como vacío
  );

  AlumnosState copyWith({
    List<dynamic>? alumnos,
    DocumentSnapshot? ultimoDoc,
    bool? hayMas,
    bool? cargando,
    String? searchQuery, // Añadido
  }) {
    return AlumnosState(
      alumnos: alumnos ?? this.alumnos,
      ultimoDoc: ultimoDoc ?? this.ultimoDoc,
      hayMas: hayMas ?? this.hayMas,
      cargando: cargando ?? this.cargando,
      searchQuery: searchQuery ?? this.searchQuery, // Añadido
    );
  }
}
