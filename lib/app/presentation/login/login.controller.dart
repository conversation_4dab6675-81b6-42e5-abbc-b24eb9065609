import 'package:get/get.dart';

import '../../domain/entities/user.dart';
import 'casos_de_uso/sign_in_usecase.dart';

class LoginController extends GetxController {
  final SignInUseCase casoUsoIniciarSesion;
  final Rxn<AppUser> usuario = Rxn<AppUser>();
  final RxBool cargando = false.obs;
  final RxnString error = RxnString();

  LoginController({required this.casoUsoIniciarSesion});

  Future<void> iniciarSesion(String correo, String contrasena) async {
    cargando(true);
    error(null);
    try {
      usuario.value = await casoUsoIniciarSesion(correo, contrasena);
    } catch (e) {
      error(e.toString());
    } finally {
      cargando(false);
    }
  }
}