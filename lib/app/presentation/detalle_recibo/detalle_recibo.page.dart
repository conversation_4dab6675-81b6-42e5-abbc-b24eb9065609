import 'dart:html' as html;
import 'dart:ui' as ui;
import 'package:flutter/material.dart';
import 'package:flutter/rendering.dart';
import 'package:gemini_app/app/domain/modelos/recibo.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:intl/intl.dart';
import 'package:get/get.dart';
import 'package:gemini_app/app/presentation/detalle_recibo/detalle_recibo.controller.dart';

class DetalleReciboPage extends StatefulWidget {
  final Recibo recibo;

  const DetalleReciboPage({super.key, required this.recibo});

  @override
  State<DetalleReciboPage> createState() => _DetalleReciboPageState();
}

class _DetalleReciboPageState extends State<DetalleReciboPage> {
  final GlobalKey _receiptKey = GlobalKey();
  late final DetalleReciboController _controller;

  // === COLORES PERSONALIZADOS ===
  static const Color _colorPrimario = Color(0xFF0D47A1);
  static const Color _colorCancelado = Color(0xFFD32F2F);
  static const Color _colorFondo = Color(0xFFF8FAFC);
  static const Color _colorCanceladoClaro = Color(0xFFFFEBEE);
  static const Color _colorBorde = Color(0xFFE2E8F0);
  static const Color _textoPrincipal = Color(0xFF1E293B);
  static const Color _textoSecundario = Color(0xFF64748B);


  @override
  void initState() {
    super.initState();
    _controller = DetalleReciboController();
    _controller.recibo.value = widget.recibo;
  }

  @override
  void dispose() {
    // No necesitamos eliminar manualmente el controlador ya que GetIt maneja la instancia
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final recibo = widget.recibo;
    final isCancelado = recibo.estado.toLowerCase() == 'cancelado';

    return Scaffold(
      backgroundColor: isCancelado ? _colorCanceladoClaro : _colorFondo,
      appBar: AppBar(
        title: Text(
          'DETALLE DE RECIBO',
          style: GoogleFonts.poppins(
            fontSize: 18,
            fontWeight: FontWeight.w800,
            color: isCancelado ? Colors.white : _colorPrimario,
          ),
        ),
        centerTitle: true,
        backgroundColor: isCancelado ? _colorCancelado : Colors.transparent,
        leading: IconButton(
          icon: Icon(
            Icons.arrow_back_ios_new,
            color: isCancelado ? Colors.white : _colorPrimario,
          ),
          onPressed: () => Navigator.pop(context),
        ),
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: RepaintBoundary(
          key: _receiptKey,
          child: _buildReceiptContent(recibo),
        ),
      ),
      bottomNavigationBar: Padding(
        padding: const EdgeInsets.all(16),
        child: SizedBox(
          width: double.infinity,
          child: ElevatedButton(
            onPressed: () => _downloadReceipt(),
            style: ElevatedButton.styleFrom(
              backgroundColor: isCancelado ? _colorCancelado : _colorPrimario,
              padding: const EdgeInsets.symmetric(vertical: 14),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
            ),
            child: Text(
              'DESCARGAR RECIBO',
              style: GoogleFonts.inter(
                fontSize: 16,
                fontWeight: FontWeight.w700,
                letterSpacing: 0.5,
                color: Colors.white,
              ),
            ),
          ),
        ),
      ),
      floatingActionButton: recibo.estado.toLowerCase() == 'activo'
          ? FloatingActionButton.extended(
              onPressed: () => _confirmarCancelacion(context),
              icon: const Icon(Icons.cancel),
              label: const Text(
                'Cancelar Recibo',
                style: TextStyle(fontWeight: FontWeight.w600),
              ),
              backgroundColor: Colors.red,
            )
          : null,
    );
  }

  Widget _buildReceiptContent(Recibo recibo) {
    final semanasCubiertas = recibo.detalles
        .where((d) => d.tipo == 'semana' && d.referenciaId != null && d.monto > 0)
        .map((d) => d.referenciaId!)
        .toList()
      ..sort();

    final total = recibo.detalles.fold(0.0, (sum, item) => sum + item.monto);

    return ConstrainedBox(
      constraints: const BoxConstraints(maxWidth: 400),
      child: Container(
        padding: const EdgeInsets.all(20),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(10),
          border: Border.all(color: _colorBorde),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Encabezado compacto
            Center(
              child: Column(
                children: [
                  Text(
                    'INSTITUTO MEXICANO DE INFORMÁTICA A.C.',
                    style: GoogleFonts.poppins(
                      fontSize: 16,
                      fontWeight: FontWeight.w900,
                      color: _colorPrimario,
                    ),
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(height: 4),
                  Text(
                    '4 Sur No. 108 Col. Centro Tepeaca, Puebla',
                    style: GoogleFonts.poppins(
                      fontSize: 12,
                      fontWeight: FontWeight.w600,
                      color: _textoSecundario,
                    ),
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(height: 2),
                  Text(
                    'Tel. 2751381 y 1110213',
                    style: GoogleFonts.poppins(
                      fontSize: 11,
                      fontWeight: FontWeight.w600,
                      color: _textoSecundario,
                    ),
                  ),
                ],
              ),
            ),
            const SizedBox(height: 16),
            const Divider(height: 1, thickness: 1, color: _colorBorde),
            const SizedBox(height: 16),

            // Información principal compacta
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'RECIBO DE PAGO',
                      style: GoogleFonts.poppins(
                        fontSize: 18,
                        fontWeight: FontWeight.w900,
                        color: _colorPrimario,
                      ),
                    ),
                    Text(
                      recibo.folio,
                      style: GoogleFonts.poppins(
                        fontSize: 14,
                        fontWeight: FontWeight.w700,
                        color: _textoPrincipal,
                      ),
                    ),
                  ],
                ),
                Text(
                  DateFormat('dd/MM/yy HH:mm').format(recibo.fechaEmision),
                  style: GoogleFonts.poppins(
                    fontSize: 13,
                    fontWeight: FontWeight.w600,
                    color: _textoSecundario,
                  ),
                ),
              ],
            ),

            const SizedBox(height: 16),
            const Divider(height: 1, thickness: 1, color: _colorBorde),
            const SizedBox(height: 12),

            // Información del alumno compacta
            Text(
              'ALUMNO:',
              style: GoogleFonts.poppins(
                fontSize: 14,
                fontWeight: FontWeight.w800,
                color: _textoPrincipal,
              ),
            ),
            const SizedBox(height: 4),
            Text(
              recibo.alumnoNombre,
              style: GoogleFonts.poppins(
                fontSize: 15,
                fontWeight: FontWeight.w700,
                color: _textoPrincipal,
              ),
            ),

            if (semanasCubiertas.isNotEmpty) ...[
              const SizedBox(height: 8),
              Text(
                'Semanas: ${semanasCubiertas.join(', ')}',
                style: GoogleFonts.poppins(
                  fontSize: 13,
                  fontWeight: FontWeight.w600,
                  color: _textoSecundario,
                ),
              ),
            ],

            const SizedBox(height: 16),
            const Divider(height: 1, thickness: 1, color: _colorBorde),
            const SizedBox(height: 12),

            // Detalles de pago compactos
            Text(
              'DETALLE DE PAGO',
              style: GoogleFonts.poppins(
                fontSize: 14,
                fontWeight: FontWeight.w800,
                color: _textoPrincipal,
              ),
            ),
            const SizedBox(height: 8),
            Table(
              columnWidths: const {
                0: FlexColumnWidth(3),
                1: FlexColumnWidth(1),
              },
              defaultVerticalAlignment: TableCellVerticalAlignment.middle,
              children: [
                TableRow(
                  decoration: BoxDecoration(
                    border: Border(bottom: BorderSide(color: _colorBorde)),
                  ),
                  children: [
                    Padding(
                      padding: const EdgeInsets.symmetric(vertical: 8),
                      child: Text(
                        'Concepto',
                        style: GoogleFonts.poppins(
                          fontSize: 13,
                          fontWeight: FontWeight.w800,
                        ),
                      ),
                    ),
                    Padding(
                      padding: const EdgeInsets.symmetric(vertical: 8),
                      child: Text(
                        'Monto',
                        style: GoogleFonts.poppins(
                          fontSize: 13,
                          fontWeight: FontWeight.w800,
                        ),
                          textAlign: TextAlign.right,
                      ),
                    ),
                  ],
                ),
                ...recibo.detalles.map((detalle) => TableRow(
                  decoration: BoxDecoration(
                    border: Border(bottom: BorderSide(color: _colorBorde.withOpacity(0.5))),
                  ),
                  children: [
                    Padding(
                      padding: const EdgeInsets.symmetric(vertical: 6),
                      child: Text(
                        detalle.concepto,
                        style: GoogleFonts.poppins(
                          fontSize: 13,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ),
                    Padding(
                      padding: const EdgeInsets.symmetric(vertical: 6),
                      child: Text(
                        '\$${detalle.monto.toStringAsFixed(2)}',
                        style: GoogleFonts.poppins(
                          fontSize: 13,
                          fontWeight: FontWeight.w600,
                        ),
                          textAlign: TextAlign.right,
                      ),
                    ),
                  ],
                )),
                TableRow(
                  children: [
                    Padding(
                      padding: const EdgeInsets.only(top: 8),
                      child: Text(
                        'TOTAL',
                        style: GoogleFonts.poppins(
                          fontSize: 14,
                          fontWeight: FontWeight.w900,
                        ),
                      ),
                    ),
                    Padding(
                      padding: const EdgeInsets.only(top: 8),
                      child: Text(
                        '\$${total.toStringAsFixed(2)}',
                        style: GoogleFonts.poppins(
                          fontSize: 14,
                          fontWeight: FontWeight.w900,
                          color: _colorPrimario,
                        ),
                          textAlign: TextAlign.right,
                      ),
                    ),
                  ],
                ),
              ],
            ),

            // Estado compacto
            Padding(
              padding: const EdgeInsets.only(top: 16, bottom: 8),
              child: Center(
                child: Container(
                  padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 6),
                  decoration: BoxDecoration(
                    color: recibo.estado.toLowerCase() == 'cancelado'
                        ? _colorCancelado.withOpacity(0.1)
                        : Colors.green.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(12),
                    border: Border.all(
                      color: recibo.estado.toLowerCase() == 'cancelado'
                          ? _colorCancelado
                          : Colors.green,
                    ),
                  ),
                  child: Text(
                    'ESTADO: ${recibo.estado.toUpperCase()}',
                    style: GoogleFonts.poppins(
                      fontSize: 13,
                      fontWeight: FontWeight.w800,
                      color: recibo.estado.toLowerCase() == 'cancelado'
                          ? _colorCancelado
                          : Colors.green,
                    ),
                  ),
                ),
              ),
            ),

            if (recibo.notas != null && recibo.notas!.isNotEmpty) ...[
              const SizedBox(height: 8),
              Text(
                'Nota: ${recibo.notas!}',
                style: GoogleFonts.poppins(
                  fontSize: 12,
                  fontWeight: FontWeight.w500,
                  color: _textoSecundario,
                  fontStyle: FontStyle.italic,
                ),
              ),
            ],

            const SizedBox(height: 12),
            Center(
              child: Text(
                '"Educación de calidad para el futuro"',
                style: GoogleFonts.poppins(
                  fontSize: 12,
                  fontWeight: FontWeight.w600,
                  color: _textoSecundario,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Future<void> _downloadReceipt() async {
    try {
      final boundary = _receiptKey.currentContext?.findRenderObject()
          as RenderRepaintBoundary?;

      if (boundary == null || boundary.size.isEmpty) {
        throw Exception('No se pudo capturar el recibo');
      }

      final image = await boundary.toImage();
      final byteData = await image.toByteData(format: ui.ImageByteFormat.png);
      final pngBytes = byteData!.buffer.asUint8List();

      final blob = html.Blob([pngBytes], 'image/png');
      final url = html.Url.createObjectUrlFromBlob(blob);
      final fileName =
          'recibo_${widget.recibo.folio.replaceAll('/', '_')}_${DateFormat('yyyyMMdd_HHmmss').format(widget.recibo.fechaEmision)}.png';

      final anchor = html.AnchorElement(href: url)
        ..download = fileName
        ..click();

      await Future.delayed(const Duration(milliseconds: 100), () {
        html.Url.revokeObjectUrl(url);
      });
    } catch (e) {
      ScaffoldMessenger.of(Get.context!).showSnackBar(
        SnackBar(content: Text('Error al descargar: $e')),
      );
    }
  }

  void _confirmarCancelacion(BuildContext context) {
    showDialog(
      context: context,
      builder: (ctx) => AlertDialog(
        title: const Text('Confirmar Cancelación'),
        content: const Text('¿Cancelar este recibo y revertir todos los pagos?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(ctx),
            child: const Text('Cancelar'),
          ),
          TextButton(
            onPressed: () {
              _controller.cancelarRecibo();
              Navigator.pop(ctx);
            },
            child: const Text('Confirmar', style: TextStyle(color: Colors.red)),
          ),
        ],
      ),
    );
  }
}
