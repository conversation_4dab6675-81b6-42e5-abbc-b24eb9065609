import 'package:flutter/material.dart';
import 'package:gemini_app/app/domain/modelos/recibo.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:intl/intl.dart';
import '../widgets/line_through_painter.dart';

class ReciboImpresionTemplate extends StatelessWidget {
  final Recibo recibo;
  final List<String> semanasCubiertas;
  final TextStyle estiloSubtitulo;
  final TextStyle estiloCeldaTabla;
  final TextStyle estiloMarcaAgua;
  final Color colorPrimario;
  final Color colorFondo;
  final Color colorBorde;
  final Color textoPrincipal;
  final Color textoSecundario;
  final Color colorCancelado;

  const ReciboImpresionTemplate({
    super.key,
    required this.recibo,
    required this.semanasCubiertas,
    required this.estiloSubtitulo,
    required this.estiloCeldaTabla,
    required this.estiloMarcaAgua,
    required this.colorPrimario,
    required this.colorFondo,
    required this.colorBorde,
    required this.textoPrincipal,
    required this.textoSecundario,
    required this.colorCancelado,
  });

  @override
  Widget build(BuildContext context) {
    final isCancelado = recibo.estado.toLowerCase() == 'cancelado';

    return Container(
      width: 456, // 58mm at 203 DPI
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 20,
            spreadRadius: 2,
            offset: const Offset(0, 6),
          ),
        ],
      ),
      child: Stack(
        children: [
          // Contenido principal del recibo
          Column(
            children: [
              _buildHeaderSection(isCancelado),
              Padding(
                padding: const EdgeInsets.symmetric(
                  vertical: 20,
                  horizontal: 16,
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    _buildReceiptInfoSection(isCancelado),
                    const SizedBox(height: 24),
                    _buildStudentInfoSection(isCancelado),
                    const SizedBox(height: 28),
                    _buildTotalAmountSection(isCancelado),
                    const SizedBox(height: 32),
                    _buildPaymentDetailsSection(isCancelado),
                    if (semanasCubiertas.isNotEmpty) ...[
                      const SizedBox(height: 24),
                      _buildCoveredWeeksSection(isCancelado),
                    ],
                    if (recibo.notas != null &&
                        recibo.notas!.trim().isNotEmpty) ...[
                      const SizedBox(height: 24),
                      _buildNotesSection(isCancelado),
                    ],
                    const SizedBox(height: 28),
                    _buildFooterSection(isCancelado),
                  ],
                ),
              ),
            ],
          ),

          // Marca de agua para recibos cancelados
          if (isCancelado)
            Positioned.fill(
              child: IgnorePointer(
                child: Container(
                  color: Colors.white.withOpacity(0.1),
                  child: Center(
                    child: Transform.rotate(
                      angle: -0.2,
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Text(
                            'CANCELADO',
                            style: GoogleFonts.oswald(
                              fontSize: 80,
                              fontWeight: FontWeight.w900,
                              color: Colors.black.withOpacity(0.15),
                              letterSpacing: 5,
                            ),
                          ),

                        ],
                      ),
                    ),
                  ),
                ),
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildHeaderSection(bool isCancelado) {
    return Container(
      padding: const EdgeInsets.symmetric(vertical: 24, horizontal: 16),
      decoration: BoxDecoration(
        color: Colors.black,
        borderRadius: const BorderRadius.vertical(top: Radius.circular(12)),
      ),
      child: Column(
        children: [
          if (isCancelado)
            Align(
              alignment: Alignment.topRight,
              child: Container(
                padding: const EdgeInsets.symmetric(
                  horizontal: 16,
                  vertical: 8,
                ),
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(4),
                  border: Border.all(color: Colors.black, width: 2),
                ),
                child: Text(
                  'CANCELADO',
                  style: GoogleFonts.roboto(
                    fontSize: 24,
                    fontWeight: FontWeight.w900,
                    color: Colors.black,
                    letterSpacing: 2,
                  ),
                ),
              ),
            ),
          if (isCancelado) const SizedBox(height: 12),
          Text(
            'INSTITUTO MEXICANO DE INFORMÁTICA A.C.',
            style: GoogleFonts.poppins(
              fontSize: 32,
              fontWeight: FontWeight.w900,
              color: Colors.white,
              letterSpacing: 0.7,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 12),
          Text(
            '4 Sur No. 108 Col. Centro Tepeaca, Puebla',
            style: GoogleFonts.inter(
              fontSize: 24,
              fontWeight: FontWeight.w800,
              color: Colors.white.withOpacity(0.9),
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 8),
          Text(
            'Tel. 2751381 y 1110213 | <EMAIL>',
            style: GoogleFonts.inter(
              fontSize: 20,
              fontWeight: FontWeight.w800,
              color: Colors.white.withOpacity(0.9),
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildReceiptInfoSection(bool isCancelado) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.black.withOpacity(0.3), width: 1.5),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(Icons.receipt_long_outlined, size: 28, color: Colors.black),
              const SizedBox(width: 8),
              Text(
                'INFORMACIÓN DEL RECIBO',
                style: GoogleFonts.poppins(
                  fontSize: 28,
                  fontWeight: FontWeight.w900,
                  color: Colors.black,
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          _buildReceiptItem('Folio:', recibo.id, isCancelado, isBold: true),
          _buildReceiptItem(
            'Fecha:',
            DateFormat('dd/MM/yyyy HH:mm').format(recibo.fechaEmision),
            isCancelado,
          ),
          _buildReceiptItem('Concepto:', recibo.concepto, isCancelado),
          _buildReceiptItem('Método de Pago:', recibo.metodoPago, isCancelado),
          if (isCancelado) ...[
            _buildReceiptItem(
              'Fecha de cancelación:',
              DateFormat('dd/MM/yyyy HH:mm').format(recibo.fechaCancelacion!),
              isCancelado,
            ),
            _buildReceiptItem(
              'Cancelado por:',
              recibo.canceladoPor ?? '',
              isCancelado,
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildReceiptItem(
    String label,
    String value,
    bool isCancelado, {
    bool isBold = false,
  }) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 14),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 140,
            child: Text(
              label,
              style: GoogleFonts.inter(
                fontSize: 24,
                color: Colors.black.withOpacity(0.8),
                fontWeight: FontWeight.w800,
              ),
            ),
          ),
          const SizedBox(width: 8),
          Expanded(
            child: Stack(
              children: [
                if (isCancelado)
                  Positioned.fill(
                    child: CustomPaint(
                      painter: LineThroughPainter(
                        color: Colors.black,
                        thickness: 3,
                      ),
                    ),
                  ),
                Text(
                  value,
                  style:
                      isBold
                          ? GoogleFonts.inter(
                            fontSize: 24,
                            fontWeight: FontWeight.w900,
                            color: Colors.black,
                          )
                          : GoogleFonts.inter(
                            fontSize: 24,
                            fontWeight: FontWeight.w800,
                            color: Colors.black,
                          ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildStudentInfoSection(bool isCancelado) {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.black.withOpacity(0.05),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.black.withOpacity(0.3)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(Icons.person_outline_rounded, size: 28, color: Colors.black),
              const SizedBox(width: 8),
              Text(
                'INFORMACIÓN DEL ALUMNO',
                style: GoogleFonts.poppins(
                  fontSize: 28,
                  fontWeight: FontWeight.w900,
                  color: Colors.black,
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          Text(
            'Recibimos de:',
            style: GoogleFonts.inter(
              fontSize: 24,
              color: Colors.black.withOpacity(0.8),
              fontWeight: FontWeight.w800,
            ),
          ),
          const SizedBox(height: 6),
          Stack(
            children: [
              if (isCancelado)
                Positioned.fill(
                  child: CustomPaint(
                    painter: LineThroughPainter(
                      color: Colors.black,
                      thickness: 3,
                    ),
                  ),
                ),
              Text(
                recibo.alumnoNombre,
                style: GoogleFonts.inter(
                  fontSize: 32,
                  fontWeight: FontWeight.w900,
                  color: Colors.black,
                ),
              ),
            ],
          ),
          const SizedBox(height: 10),
          Text(
            'Pagado por: ${recibo.creadoPor}',
            style: GoogleFonts.inter(
              fontSize: 24,
              color: Colors.black.withOpacity(0.8),
              fontWeight: FontWeight.w800,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTotalAmountSection(bool isCancelado) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'MONTO TOTAL',
          style: GoogleFonts.inter(
            fontSize: 28,
            fontWeight: FontWeight.w900,
            color: Colors.black,
            letterSpacing: 0.5,
          ),
        ),
        const SizedBox(height: 12),
        Stack(
          alignment: Alignment.center,
          children: [
            Container(
              padding: const EdgeInsets.symmetric(vertical: 16, horizontal: 20),
              decoration: BoxDecoration(
                color: Colors.black.withOpacity(0.05),
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.black.withOpacity(0.3)),
              ),
              child: Row(
                crossAxisAlignment: CrossAxisAlignment.baseline,
                textBaseline: TextBaseline.alphabetic,
                children: [
                  Text(
                    '\$',
                    style: GoogleFonts.spaceMono(
                      fontSize: 48,
                      fontWeight: FontWeight.w900,
                      color: Colors.black,
                    ),
                  ),
                  const SizedBox(width: 8),
                  Stack(
                    children: [
                      if (isCancelado)
                        Positioned.fill(
                          child: CustomPaint(
                            painter: LineThroughPainter(
                              color: Colors.black,
                              thickness: 4,
                            ),
                          ),
                        ),
                      Text(
                        recibo.montoTotal.toStringAsFixed(2),
                        style: GoogleFonts.spaceMono(
                          fontSize: 56,
                          fontWeight: FontWeight.w900,
                          color: Colors.black,
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
            Positioned(
              right: -10,
              top: -10,
              child: Icon(
                Icons.attach_money_rounded,
                color: Colors.black.withOpacity(0.1),
                size: 80,
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildPaymentDetailsSection(bool isCancelado) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Icon(Icons.list_alt_outlined, size: 28, color: Colors.black),
            const SizedBox(width: 8),
            Text(
              'DESGLOSE DE PAGO',
              style: GoogleFonts.poppins(
                fontSize: 28,
                fontWeight: FontWeight.w900,
                color: Colors.black,
              ),
            ),
          ],
        ),
        const SizedBox(height: 16),
        Container(
          decoration: BoxDecoration(
            color: Colors.black.withOpacity(0.05),
            borderRadius: BorderRadius.circular(8),
            border: Border.all(color: Colors.black.withOpacity(0.3)),
          ),
          child: ListView.separated(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            itemCount: recibo.detalles.length,
            separatorBuilder:
                (_, __) =>
                    Divider(height: 2, color: Colors.black.withOpacity(0.2)),
            itemBuilder: (context, index) {
              final d = recibo.detalles[index];
              return Padding(
                padding: const EdgeInsets.symmetric(
                  vertical: 12,
                  horizontal: 16,
                ),
                child: Row(
                  children: [
                    Expanded(
                      flex: 3,
                      child: Stack(
                        children: [
                          if (isCancelado)
                            Positioned.fill(
                              child: CustomPaint(
                                painter: LineThroughPainter(
                                  color: Colors.black,
                                  thickness: 2,
                                ),
                              ),
                            ),
                          Text(
                            d.tipo == 'semana' && d.referenciaId != null
                                ? 'Semana ${d.referenciaId}'
                                : d.concepto,
                            style: GoogleFonts.inter(
                              fontSize: 24,
                              fontWeight: FontWeight.w800,
                              color: Colors.black,
                            ),
                          ),
                        ],
                      ),
                    ),
                    Expanded(
                      flex: 1,
                      child: Stack(
                        children: [
                          if (isCancelado)
                            Positioned.fill(
                              child: CustomPaint(
                                painter: LineThroughPainter(
                                  color: Colors.black,
                                  thickness: 2,
                                ),
                              ),
                            ),
                          Text(
                            '\$${d.monto.toStringAsFixed(2)}',
                            style: GoogleFonts.inter(
                              fontSize: 24,
                              fontWeight: FontWeight.w900,
                              color: Colors.black,
                            ),
                            textAlign: TextAlign.right,
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              );
            },
          ),
        ),
      ],
    );
  }

  Widget _buildCoveredWeeksSection(bool isCancelado) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Icon(Icons.calendar_today_outlined, size: 28, color: Colors.black),
            const SizedBox(width: 8),
            Text(
              'SEMANAS CUBIERTAS',
              style: GoogleFonts.poppins(
                fontSize: 28,
                fontWeight: FontWeight.w900,
                color: Colors.black,
              ),
            ),
          ],
        ),
        const SizedBox(height: 12),
        Wrap(
          spacing: 12,
          runSpacing: 12,
          children:
              semanasCubiertas
                  .map(
                    (semana) => Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 16,
                        vertical: 8,
                      ),
                      decoration: BoxDecoration(
                        color: Colors.black.withOpacity(0.05),
                        borderRadius: BorderRadius.circular(20),
                        border: Border.all(
                          color: Colors.black.withOpacity(0.3),
                        ),
                      ),
                      child: Stack(
                        children: [
                          if (isCancelado)
                            Positioned.fill(
                              child: CustomPaint(
                                painter: LineThroughPainter(
                                  color: Colors.black,
                                  thickness: 2,
                                ),
                              ),
                            ),
                          Text(
                            'Semana $semana',
                            style: GoogleFonts.inter(
                              fontSize: 24,
                              fontWeight: FontWeight.w800,
                              color: Colors.black,
                            ),
                          ),
                        ],
                      ),
                    ),
                  )
                  .toList(),
        ),
      ],
    );
  }

  Widget _buildNotesSection(bool isCancelado) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Icon(Icons.note_outlined, size: 28, color: Colors.black),
            const SizedBox(width: 8),
            Text(
              'NOTAS ADICIONALES',
              style: GoogleFonts.poppins(
                fontSize: 28,
                fontWeight: FontWeight.w900,
                color: Colors.black,
              ),
            ),
          ],
        ),
        const SizedBox(height: 12),
        Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.black.withOpacity(0.05),
            borderRadius: BorderRadius.circular(8),
            border: Border.all(color: Colors.black.withOpacity(0.3)),
          ),
          child: Text(
            recibo.notas!,
            style: GoogleFonts.inter(
              fontSize: 24,
              fontWeight: FontWeight.w800,
              color: Colors.black,
              height: 1.5,
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildFooterSection(bool isCancelado) {
    return Column(
      children: [
        Divider(color: Colors.black.withOpacity(0.3), height: 2),
        const SizedBox(height: 16),
        Text(
          'NOTA: TODO PAGO REALIZADO DEBERÁ EXIGIR SU RECIBO COMO COMPROBANTE DEL MISMO.',
          style: GoogleFonts.inter(
            fontSize: 20,
            fontWeight: FontWeight.w900,
            color: Colors.black,
            height: 1.4,
          ),
          textAlign: TextAlign.center,
        ),
        const SizedBox(height: 12),
        Text(
          '"Educación de calidad para el futuro"',
          style: GoogleFonts.poppins(
            fontSize: 22,
            fontStyle: FontStyle.italic,
            fontWeight: FontWeight.w800,
            color: Colors.black,
          ),
        ),
      ],
    );
  }
}
