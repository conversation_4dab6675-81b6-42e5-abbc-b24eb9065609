import 'package:flutter/material.dart';
import 'package:gemini_app/app/domain/modelos/recibo.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:intl/intl.dart';
import '../widgets/line_through_painter.dart';

class ReciboCanceladoTemplate extends StatelessWidget {
  final Recibo recibo;
  final List<String> semanasCubiertas;
  final TextStyle estiloSubtitulo;
  final TextStyle estiloCeldaTabla;
  final TextStyle estiloMarcaAgua;
  final Color colorPrimario;
  final Color colorIndigo;
  final Color colorVioleta;
  final Color colorFondo;
  final Color colorBorde;
  final Color textoPrincipal;
  final Color textoSecundario;
  final Color colorDestacado;
  final Color colorCancelado;
  final Color colorCanceladoClaro;

  const ReciboCanceladoTemplate({
    super.key,
    required this.recibo,
    required this.semanasCubiertas,
    required this.estiloSubtitulo,
    required this.estiloCeldaTabla,
    required this.estiloMarcaAgua,
    required this.colorPrimario,
    required this.colorIndigo,
    required this.colorVioleta,
    required this.colorFondo,
    required this.colorBorde,
    required this.textoPrincipal,
    required this.textoSecundario,
    required this.colorDestacado,
    required this.colorCancelado,
    required this.colorCanceladoClaro,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white.withOpacity(0.9),
        borderRadius: BorderRadius.circular(20),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 20,
            spreadRadius: 2,
            offset: const Offset(0, 6),
          ),
        ],
      ),
      child: Stack(
        children: [

          Column(
            children: [
              _buildHeaderSection(),
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 20),
                child: Column(
                  children: [
                    _buildReceiptInfoSection(),
                    const SizedBox(height: 28),
                    _buildStudentInfoSection(),
                    const SizedBox(height: 32),
                    _buildTotalAmountSection(),
                    const SizedBox(height: 40),
                    _buildPaymentDetailsSection(),
                    if (semanasCubiertas.isNotEmpty) ...[
                      const SizedBox(height: 28),
                      _buildCoveredWeeksSection(),
                    ],
                    if (recibo.notas != null && recibo.notas!.trim().isNotEmpty) ...[
                      const SizedBox(height: 28),
                      _buildNotesSection(),
                    ],
                    const SizedBox(height: 32),
                    _buildFooterSection(),
                  ],
                ),
              ),
            ],
          ),
          Positioned.fill(
            child: IgnorePointer(
              child: Center(
                child: Transform.rotate(
                  angle: -0.2,
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Text('CANCELADO', style: estiloMarcaAgua),
                      const SizedBox(height: 20),
                      Text(
                        'DOCUMENTO NO VÁLIDO',
                        style: estiloMarcaAgua.copyWith(fontSize: 80),
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildHeaderSection() {
    return Container(
      padding: const EdgeInsets.symmetric(vertical: 24, horizontal: 16),
      decoration: BoxDecoration(
        borderRadius: const BorderRadius.vertical(top: Radius.circular(16)),
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [colorCancelado, colorCancelado.withOpacity(0.7)],
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Stack(
        children: [
          Positioned(
            right: 20,
            top: 20,
            child: Transform.rotate(
              angle: 0.3,
              child: Container(
                padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 6),
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(4),
                  border: Border.all(color: Colors.black, width: 2),
                ),
                child: Text(
                  'CANCELADO',
                  style: GoogleFonts.roboto(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: Colors.black,
                    letterSpacing: 2,
                  ),
                ),
              ),
            ),
          ),
          Column(
            children: [
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.white.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(12),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.white.withOpacity(0.2),
                      blurRadius: 6,
                      spreadRadius: 1,
                    ),
                  ],
                ),
                child: Image.asset(
                  'assets/logo_corto.png',
                  height: 50,
                  fit: BoxFit.contain,
                ),
              ),
              const SizedBox(height: 20),
              Text(
                'INSTITUTO MEXICANO DE INFORMÁTICA A.C.',
                style: GoogleFonts.poppins(
                  fontSize: 16,
                  fontWeight: FontWeight.w700,
                  color: Colors.white,
                  letterSpacing: 0.5,
                  shadows: [
                    Shadow(
                      color: Colors.black.withOpacity(0.3),
                      offset: const Offset(1, 1),
                      blurRadius: 2,
                    ),
                  ],
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 12),
              Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(Icons.location_on_outlined, size: 14, color: Colors.white70),
                  const SizedBox(width: 4),
                  Expanded(
                    child: Text(
                      '4 Sur No. 108 Col. Centro Tepeaca, Puebla',
                      style: GoogleFonts.inter(
                        fontSize: 11,
                        color: Colors.white.withOpacity(0.9),
                        height: 1.4,
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 4),
              Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(Icons.phone_outlined, size: 14, color: Colors.white70),
                  const SizedBox(width: 4),
                  Text(
                    'Tel. 2751381 y 1110213 | <EMAIL>',
                    style: GoogleFonts.inter(
                      fontSize: 10,
                      color: Colors.white.withOpacity(0.9),
                      height: 1.4,
                    ),
                    textAlign: TextAlign.center,
                  ),
                ],
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildReceiptInfoSection() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: colorCancelado.withOpacity(0.3), width: 1.5),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(Icons.receipt_long_outlined, size: 20, color: colorCancelado),
              const SizedBox(width: 8),
              Text(
                'INFORMACIÓN DEL RECIBO',
                style: estiloSubtitulo.copyWith(color: colorCancelado),
              ),
            ],
          ),
          const SizedBox(height: 16),
          _buildReceiptItem('Folio:', recibo.id, isBold: true),
          _buildReceiptItem('Fecha:', DateFormat('dd/MM/yyyy HH:mm').format(recibo.fechaEmision)),
          _buildReceiptItem('Concepto:', recibo.concepto),
          _buildReceiptItem('Método de Pago:', recibo.metodoPago),
          _buildReceiptItem('Fecha de cancelación:', DateFormat('dd/MM/yyyy HH:mm').format(recibo.fechaCancelacion!)),
          _buildReceiptItem('Cancelado por:', recibo.canceladoPor ?? ''),
        ],
      ),
    );
  }

  Widget _buildReceiptItem(String label, String value, {bool isBold = false}) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 14),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 80,
            child: RichText(
              text: TextSpan(
                style: GoogleFonts.inter(
                  fontSize: 12,
                  color: colorCancelado.withOpacity(0.8),
                  fontWeight: FontWeight.w500,
                ),
                children: [TextSpan(text: label)],
              ),
            ),
          ),
          const SizedBox(width: 8),
          Expanded(
            child: Stack(
              children: [
                Positioned.fill(
                  child: CustomPaint(
                    painter: LineThroughPainter(
                      color: colorCancelado,
                      thickness: 2,
                    ),
                  ),
                ),
                Text(
                  value,
                  style: isBold
                      ? GoogleFonts.inter(
                    fontSize: 12,
                    fontWeight: FontWeight.bold,
                    color: colorCancelado,
                  )
                      : GoogleFonts.inter(
                    fontSize: 12,
                    color: colorCancelado,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildStudentInfoSection() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: colorCancelado.withOpacity(0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: colorCancelado.withOpacity(0.3)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(Icons.person_outline_rounded, size: 20, color: colorCancelado),
              const SizedBox(width: 8),
              Text(
                'INFORMACIÓN DEL ALUMNO',
                style: estiloSubtitulo.copyWith(color: colorCancelado),
              ),
            ],
          ),
          const SizedBox(height: 12),
          Text(
            'Recibimos de:',
            style: estiloCeldaTabla.copyWith(
              fontSize: 12,
              color: colorCancelado.withOpacity(0.8),
            ),
          ),
          const SizedBox(height: 4),
          Stack(
            children: [
              Positioned.fill(
                child: CustomPaint(
                  painter: LineThroughPainter(
                    color: colorCancelado,
                    thickness: 2,
                  ),
                ),
              ),
              Text(
                recibo.alumnoNombre,
                style: GoogleFonts.inter(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color: colorCancelado,
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Text(
            'Pagado por: ${recibo.creadoPor}',
            style: estiloCeldaTabla.copyWith(
              fontSize: 12,
              color: colorCancelado.withOpacity(0.8),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTotalAmountSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'MONTO TOTAL',
          style: GoogleFonts.inter(
            fontSize: 13,
            fontWeight: FontWeight.w600,
            color: colorCancelado.withOpacity(0.8),
            letterSpacing: 0.5,
          ),
        ),
        const SizedBox(height: 10),
        Stack(
          alignment: Alignment.center,
          children: [
            Container(
              padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 16),
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  colors: [
                    colorCancelado.withOpacity(0.1),
                    colorCancelado.withOpacity(0.05),
                  ],
                ),
                borderRadius: BorderRadius.circular(12),
                border: Border.all(color: colorCancelado.withOpacity(0.3)),
                boxShadow: [
                  BoxShadow(
                    color: colorCancelado.withOpacity(0.1),
                    blurRadius: 8,
                    spreadRadius: 1,
                    offset: const Offset(0, 2),
                  ),
                ],
              ),
              child: Row(
                crossAxisAlignment: CrossAxisAlignment.baseline,
                textBaseline: TextBaseline.alphabetic,
                children: [
                  Text(
                    '\$',
                    style: GoogleFonts.spaceMono(
                      fontSize: 28,
                      fontWeight: FontWeight.w700,
                      color: colorCancelado,
                    ),
                  ),
                  const SizedBox(width: 6),
                  Stack(
                    children: [
                      Positioned.fill(
                        child: CustomPaint(
                          painter: LineThroughPainter(
                            color: colorCancelado,
                            thickness: 3,
                          ),
                        ),
                      ),
                      Text(
                        recibo.montoTotal.toStringAsFixed(2),
                        style: GoogleFonts.spaceMono(
                          fontSize: 32,
                          fontWeight: FontWeight.w700,
                          color: colorCancelado,
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
            Positioned(
              right: -10,
              top: -10,
              child: Icon(
                Icons.attach_money_rounded,
                color: colorCancelado.withOpacity(0.2),
                size: 50,
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildPaymentDetailsSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Icon(Icons.list_alt_outlined, size: 20, color: colorCancelado),
            const SizedBox(width: 10),
            Text(
              'DESGLOSE DE PAGO',
              style: estiloSubtitulo.copyWith(color: colorCancelado),
            ),
          ],
        ),
        const SizedBox(height: 16),
        Container(
          decoration: BoxDecoration(
            color: colorCancelado.withOpacity(0.05),
            borderRadius: BorderRadius.circular(10),
            border: Border.all(color: colorCancelado.withOpacity(0.3)),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withOpacity(0.05),
                blurRadius: 6,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: ClipRRect(
            borderRadius: BorderRadius.circular(10),
            child: ListView.separated(
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              itemCount: recibo.detalles.length,
              separatorBuilder: (_, __) => Divider(
                height: 1,
                color: colorCancelado.withOpacity(0.2),
              ),
              itemBuilder: (context, index) {
                final d = recibo.detalles[index];
                return Padding(
                  padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 12),
                  child: Row(
                    children: [
                      Expanded(
                        flex: 3,
                        child: Stack(
                          children: [
                            Positioned.fill(
                              child: CustomPaint(
                                painter: LineThroughPainter(
                                  color: colorCancelado,
                                  thickness: 1.5,
                                ),
                              ),
                            ),
                            Text(
                              d.tipo == 'semana' && d.referenciaId != null
                                  ? 'Semana ${d.referenciaId}'
                                  : d.concepto,
                              style: estiloCeldaTabla.copyWith(color: colorCancelado),
                            ),
                          ],
                        ),
                      ),
                      Expanded(
                        flex: 1,
                        child: Stack(
                          children: [
                            Positioned.fill(
                              child: CustomPaint(
                                painter: LineThroughPainter(
                                  color: colorCancelado,
                                  thickness: 1.5,
                                ),
                              ),
                            ),
                            Text(
                              '\$${d.monto.toStringAsFixed(2)}',
                              style: estiloCeldaTabla.copyWith(
                                fontWeight: FontWeight.bold,
                                color: colorCancelado,
                              ),
                              textAlign: TextAlign.right,
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                );
              },
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildCoveredWeeksSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Icon(Icons.calendar_today_outlined, size: 18, color: colorCancelado),
            const SizedBox(width: 8),
            Text(
              'SEMANAS CUBIERTAS',
              style: estiloSubtitulo.copyWith(color: colorCancelado),
            ),
          ],
        ),
        const SizedBox(height: 8),
        Wrap(
          spacing: 8,
          runSpacing: 8,
          children: semanasCubiertas.map((semana) => Container(
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
            decoration: BoxDecoration(
              color: colorCancelado.withOpacity(0.1),
              borderRadius: BorderRadius.circular(20),
              border: Border.all(color: colorCancelado.withOpacity(0.3)),
            ),
            child: Stack(
              children: [
                Positioned.fill(
                  child: CustomPaint(
                    painter: LineThroughPainter(
                      color: colorCancelado,
                      thickness: 1.5,
                    ),
                  ),
                ),
                Text(
                  'Semana $semana',
                  style: GoogleFonts.inter(
                    fontSize: 12,
                    fontWeight: FontWeight.w500,
                    color: colorCancelado,
                  ),
                ),
              ],
            ),
          )).toList(),
        ),
      ],
    );
  }

  Widget _buildNotesSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Icon(Icons.note_outlined, size: 18, color: colorCancelado),
            const SizedBox(width: 8),
            Text(
              'NOTAS ADICIONALES',
              style: estiloSubtitulo.copyWith(color: colorCancelado),
            ),
          ],
        ),
        const SizedBox(height: 8),
        Container(
          padding: const EdgeInsets.all(12),
          decoration: BoxDecoration(
            color: colorCancelado.withOpacity(0.05),
            borderRadius: BorderRadius.circular(8),
            border: Border.all(color: colorCancelado.withOpacity(0.3)),
          ),
          child: Text(
            recibo.notas!,
            style: GoogleFonts.inter(
              fontSize: 13,
              color: colorCancelado,
              height: 1.5,
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildFooterSection() {
    return Column(
      children: [
        Divider(color: colorCancelado.withOpacity(0.3), height: 1),
        const SizedBox(height: 16),
        Text(
          'NOTA: TODO PAGO REALIZADO DEBERÁ EXIGIR SU RECIBO COMO COMPROBANTE DEL MISMO.',
          style: GoogleFonts.inter(
            fontSize: 10,
            fontWeight: FontWeight.w500,
            color: colorCancelado.withOpacity(0.8),
            height: 1.4,
          ),
          textAlign: TextAlign.center,
        ),
        const SizedBox(height: 12),
        Text(
          '"Educación de calidad para el futuro"',
          style: GoogleFonts.poppins(
            fontSize: 11,
            fontStyle: FontStyle.italic,
            color: colorCancelado,
          ),
        ),
      ],
    );
  }
}
