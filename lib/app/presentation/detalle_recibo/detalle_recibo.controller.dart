import 'package:get/get.dart';
import 'package:gemini_app/app/domain/casos_de_uso/cancelar_recibo_usecase.dart';
import 'package:gemini_app/app/domain/modelos/recibo.dart';
import 'package:gemini_app/app/domain/repositories/recibo_repository.dart';
import 'package:gemini_app/app/domain/repositories/kardex_repository.dart';
import 'package:gemini_app/di/injection.dart';

class DetalleReciboController extends GetxController {
  final Rx<Recibo?> recibo = Rx<Recibo?>(null);
  final RxString error = ''.obs;
  final RxBool isLoading = true.obs;
  final ReciboRepository _reciboRepo;
  final CancelarReciboUseCase _cancelarReciboUseCase;

  DetalleReciboController()
      : _reciboRepo = getIt<ReciboRepository>(),
        _cancelarReciboUseCase = CancelarReciboUseCase(
          getIt<ReciboRepository>(),
          getIt<KardexRepository>(),
        );

  Future<void> fetchRecibo(String reciboId) async {
    try {
      isLoading.value = true;
      final result = await _reciboRepo.getReciboById(reciboId);
      recibo.value = result;
      error.value = '';
    } catch (e) {
      error.value = 'Error al cargar recibo: ${e.toString()}';
      recibo.value = null;
    } finally {
      isLoading.value = false;
    }
  }


  Future<void> cancelarRecibo() async {
    if (recibo.value == null) return;

    try {
      isLoading.value = true;
      await _cancelarReciboUseCase.execute(recibo.value!.id);
      recibo.value = recibo.value!.copyWith(estado: 'cancelado');
      Get.snackbar('Éxito', 'Recibo cancelado correctamente');
    } catch (e) {
      Get.snackbar('Error', 'No se pudo cancelar el recibo: ${e.toString()}');
    } finally {
      isLoading.value = false;
    }
  }
}
