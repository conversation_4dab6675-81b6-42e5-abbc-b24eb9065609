import 'package:beamer/beamer.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:gemini_app/app/domain/modelos/kardex.dart';
import 'package:gemini_app/app/presentation/alumno_detalle/controller/detalle_alumno.controller.dart';
import 'package:gemini_app/app/presentation/alumno_detalle/controller/detalle_alumno.state.dart';

class SemanasTab extends StatelessWidget {
  final DetalleAlumnoController controller;
  final int totalSemanas;

  const SemanasTab({
    super.key,
    required this.controller,
    required this.totalSemanas,
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                const Text(
                  'Control de Semanas',
                  style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                ),
                Text(
                  '$totalSemanas semanas totales',
                  style: const TextStyle(color: Colors.grey),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Expanded(
              child: LayoutBuilder(
                builder: (context, constraints) {
                  return StreamBuilder<List<KardexSemana>>(
                    stream: controller.semanasStream, // Usar el stream del controlador
                    builder: (context, snapshot) {
                      if (snapshot.hasError) {
                        return Center(child: Text('Error: ${snapshot.error}'));
                      }

                      if (!snapshot.hasData) {
                        return const Center(child: CircularProgressIndicator());
                      }

                      final semanas = snapshot.data!;
                      final weekMap = {for (var s in semanas) s.numeroSemana: s};
                      final orderedWeeks = List.generate(totalSemanas, (i) => i + 1);

                      return GridView.builder(
                        gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                          crossAxisCount: 7,
                          childAspectRatio: 1,
                          crossAxisSpacing: 4,
                          mainAxisSpacing: 4,
                        ),
                        itemCount: totalSemanas,
                        itemBuilder: (context, index) {
                          final weekNumber = orderedWeeks[index];
                          final semana = weekMap[weekNumber];
                          return _buildWeekItem(weekNumber, semana, context);
                        },
                      );
                    },
                  );
                },
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildWeekItem(
      int weekNumber,
      KardexSemana? semana,
      BuildContext context,
      ) {
    Color backgroundColor;
    Color borderColor;
    Color textColor;
    Widget? icon;

    if (semana == null) {
      backgroundColor = Colors.grey[100]!;
      borderColor = Colors.grey;
      textColor = Colors.grey[800]!;
    } else if (semana.estado == 'pagado') {
      backgroundColor = Colors.green[100]!;
      borderColor = Colors.green;
      textColor = Colors.green[900]!;
      icon = const Icon(Icons.check_circle, size: 14, color: Colors.green);
    } else {
      backgroundColor = Colors.orange[100]!;
      borderColor = Colors.orange;
      textColor = Colors.orange[800]!;
      icon = const Icon(Icons.access_time, size: 14, color: Colors.orange);
    }

    return InkWell(
      onTap: semana != null
          ? () => _showWeekDetails(context, semana)
          : null,
      child: Container(
        decoration: BoxDecoration(
          color: backgroundColor,
          border: Border.all(color: borderColor),
          borderRadius: BorderRadius.circular(8),
        ),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Text(
              weekNumber.toString(),
              style: TextStyle(fontWeight: FontWeight.bold, color: textColor),
            ),
            if (semana != null && semana.estado != 'pagado' && semana.montoPagado > 0)
              Text(
                '\$${semana.montoPagado}',
                style: TextStyle(fontSize: 10, color: textColor),
              ),
            if (icon != null) icon,
          ],
        ),
      ),
    );
  }

  void _showWeekDetails(BuildContext context, KardexSemana semana) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('Semana ${semana.numeroSemana}'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('Monto: \$${semana.montoPagado}'),
            Text('Estado: ${semana.estado}'),
            const SizedBox(height: 10),
            Text('Recibos: ${semana.reciboIds.isEmpty ? "Ninguno" : ""}'),
            ...semana.reciboIds
                .map(
                  (reciboId) => TextButton(
                onPressed: () => _showReceiptDetails(context, semana.kardexId, reciboId),
                child: Text('Ver Recibo $reciboId'),
              ),
            )
                .toList(),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cerrar'),
          ),
        ],
      ),
    );
  }

  void _showReceiptDetails(BuildContext context, String kardexId, String reciboId) {
    context.beamToNamed('/admin/alumnos/$kardexId/recibos/$reciboId');
  }
}
