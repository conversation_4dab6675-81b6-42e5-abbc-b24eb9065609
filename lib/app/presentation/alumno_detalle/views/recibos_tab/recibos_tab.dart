import 'package:beamer/beamer.dart';
import 'package:flutter/material.dart';
import 'package:gemini_app/app/presentation/alumno_detalle/views/pagos_tab/widgets/historial_recibos/receipt_info_styles.dart';
import 'package:gemini_app/app/presentation/alumno_detalle/views/pagos_tab/widgets/historial_recibos/receipt_tile_widget.dart';
import 'package:get/get.dart';
import 'package:gemini_app/app/domain/modelos/recibo.dart';
import 'package:gemini_app/app/presentation/alumno_detalle/controller/detalle_alumno.controller.dart';
import 'package:google_fonts/google_fonts.dart';


class RecibosTab extends StatelessWidget {
  const RecibosTab({super.key});

  @override
  Widget build(BuildContext context) {
    final controller = Get.find<DetalleAlumnoController>();
    final theme = Theme.of(context);
    final isDarkMode = theme.brightness == Brightness.dark;

    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      decoration: BoxDecoration(
        color: isDarkMode ? Colors.grey[900] : Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(16),
        child: Scaffold(
          backgroundColor: Colors.transparent,
          appBar: PreferredSize(
            preferredSize: const Size.fromHeight(kToolbarHeight),
            child: Container(
              width: double.infinity,
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: ReceiptInfoStyles.headerBackgroundColor(context),
                border: Border(
                  bottom: BorderSide(
                    color: theme.dividerColor.withOpacity(0.1),
                    width: 1,
                  ),
                ),
              ),
              child: Row(
                children: [
                  Icon(
                    Icons.receipt_long,
                    color: ReceiptInfoStyles.iconColor(context),
                    size: 24,
                  ),
                  const SizedBox(width: 12),
                  Text(
                    'Historial de Recibos',
                    style: GoogleFonts.poppins(
                      fontSize: 18,
                      fontWeight: FontWeight.w600,
                      color: ReceiptInfoStyles.headerTextColor(context),
                    ),
                  ),
                ],
              ),
            ),
          ),
          body: Column(
            children: [
              Expanded(
                child: StreamBuilder<List<Recibo>>(
                  stream: controller.recibosStream,
                  builder: (context, snapshot) {
                    if (snapshot.connectionState == ConnectionState.waiting) {
                      return const Center(
                          child: CircularProgressIndicator(strokeWidth: 2));
                    }

                    if (snapshot.hasError) {
                      return Center(
                        child: Text(
                          'Error: ${snapshot.error}',
                          style: GoogleFonts.inter(
                            fontSize: 14,
                            color: Colors.red[800],
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      );
                    }

                    final recibos = snapshot.data ?? [];
                    if (recibos.isEmpty) {
                      return Center(
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Icon(
                              Icons.receipt_outlined,
                              size: 48,
                              color: Colors.grey[400],
                            ),
                            const SizedBox(height: 16),
                            Text(
                              'No hay recibos registrados',
                              style: GoogleFonts.inter(
                                fontSize: 16,
                                color: Colors.grey[600],
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                          ],
                        ),
                      );
                    }

                    return ListView.separated(
                      padding: const EdgeInsets.all(16),
                      itemCount: recibos.length,
                      separatorBuilder: (context, index) => Divider(
                        height: 1,
                        color: theme.dividerColor.withOpacity(0.1),
                      ),
                      itemBuilder: (context, index) {
                        final recibo = recibos[index];
                        return ReceiptTileWidget(
                          recibo: recibo,
                          onTap: () {
                            context.beamToNamed(
                                '/admin/alumnos/${recibo.alumnoId}/recibos/${recibo.id}');
                          },
                        );
                      },
                    );
                  },
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}