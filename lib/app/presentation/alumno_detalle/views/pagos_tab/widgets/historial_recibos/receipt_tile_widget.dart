import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:gemini_app/app/domain/casos_de_uso/cancelar_recibo_usecase.dart';
import 'package:gemini_app/di/injection.dart';
import 'package:gemini_app/app/domain/modelos/recibo.dart';
import 'package:gemini_app/app/presentation/alumno_detalle/controller/detalle_alumno.controller.dart';
import 'package:intl/intl.dart';
import 'package:google_fonts/google_fonts.dart';

class ReceiptTileWidget extends StatefulWidget {
  final Recibo recibo;
  final VoidCallback onTap;

  const ReceiptTileWidget({
    super.key,
    required this.recibo,
    required this.onTap,
  });

  @override
  State<ReceiptTileWidget> createState() => _ReceiptTileWidgetState();
}

class _ReceiptTileWidgetState extends State<ReceiptTileWidget> {
  bool _isCancelling = false;
  bool get isCanceled => widget.recibo.estado?.toLowerCase() == 'cancelado';
  bool get isPaid => widget.recibo.estado?.toLowerCase() == 'pagado';

  // Estilos según estado
  Color _getStatusColor(BuildContext context) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;

    if (isPaid) {
      return isDarkMode ? Colors.green[900]!.withOpacity(0.3) : Colors.green[50]!;
    } else if (isCanceled) {
      return isDarkMode ? Colors.red[900]!.withOpacity(0.2) : Colors.red[50]!;
    } else {
      return isDarkMode ? Colors.teal[900]!.withOpacity(0.2) : Colors.teal[50]!;
    }
  }

  Color _getStatusTextColor(BuildContext context) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;

    if (isPaid) {
      return isDarkMode ? Colors.green[200]! : Colors.green[800]!;
    } else if (isCanceled) {
      return isDarkMode ? Colors.red[200]! : Colors.red[800]!;
    } else {
      return isDarkMode ? Colors.teal[200]! : Colors.teal[800]!;
    }
  }

  Color _getAmountColor(BuildContext context) {
    if (isPaid) {
      return Colors.green[700]!;
    } else if (isCanceled) {
      return Colors.red[700]!;
    } else {
      return Colors.teal[700]!;
    }
  }

  // Cambios en el build method para pantallas pequeñas
  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final isDarkMode = theme.brightness == Brightness.dark;
    final screenWidth = MediaQuery.of(context).size.width;
    final isSmallScreen = screenWidth < 400; // Aumentamos el umbral para pantallas pequeñas

    return Container(
      margin: const EdgeInsets.symmetric(vertical: 6, horizontal: 8), // Menos margen en móviles
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(12), // Radio de borde más pequeño
        boxShadow: [
          if (isPaid)
            BoxShadow(
              color: Colors.green.withOpacity(isDarkMode ? 0.2 : 0.05),
              blurRadius: 6,
              spreadRadius: 0.5,
            ),
          if (isCanceled)
            BoxShadow(
              color: Colors.red.withOpacity(isDarkMode ? 0.1 : 0.05),
              blurRadius: 4,
            ),
        ],
      ),
      child: Material(
        borderRadius: BorderRadius.circular(12),
        color: _getStatusColor(context),
        child: InkWell(
          onTap: widget.onTap,
          borderRadius: BorderRadius.circular(12),
          highlightColor: Colors.transparent,
          splashColor: isPaid
              ? Colors.green.withOpacity(0.1)
              : isCanceled
              ? Colors.transparent
              : Colors.teal.withOpacity(0.1),
          child: Padding(
            padding: EdgeInsets.all(isSmallScreen ? 10 : 16), // Padding más ajustado
            child: Column( // Cambiamos a columna para mejor flujo en móviles
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Icono de estado
                    Container(
                      width: isSmallScreen ? 36 : 48,
                      height: isSmallScreen ? 36 : 48,
                      decoration: BoxDecoration(
                        color: isPaid
                            ? Colors.green.withOpacity(0.1)
                            : isCanceled
                            ? Colors.red.withOpacity(0.1)
                            : Colors.teal.withOpacity(0.1),
                        borderRadius: BorderRadius.circular(10),
                        border: Border.all(
                          color: _getStatusTextColor(context).withOpacity(0.3),
                          width: 1.2,
                        ),
                      ),
                      child: Icon(
                        isPaid
                            ? Icons.check_circle_outline
                            : isCanceled
                            ? Icons.block
                            : Icons.pending_actions,
                        color: _getStatusTextColor(context),
                        size: isSmallScreen ? 18 : 24,
                      ),
                    ),
                    SizedBox(width: isSmallScreen ? 10 : 16),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          // Fila superior con concepto y monto
                          Row(
                            children: [
                              Expanded(
                                child: Text(
                                  widget.recibo.concepto ?? 'Sin concepto',
                                  style: GoogleFonts.inter(
                                    fontSize: isSmallScreen ? 15 : 16,
                                    fontWeight: FontWeight.w600,
                                    color: _getStatusTextColor(context),
                                  ),
                                  maxLines: 2, // Permitir 2 líneas
                                  overflow: TextOverflow.ellipsis,
                                ),
                              ),
                              if (!isSmallScreen) // En móviles, el monto va abajo
                                Container(
                                  padding: EdgeInsets.symmetric(
                                    horizontal: isSmallScreen ? 6 : 12,
                                    vertical: isSmallScreen ? 3 : 6,
                                  ),
                                  decoration: BoxDecoration(
                                    color: isPaid
                                        ? Colors.green.withOpacity(0.1)
                                        : isCanceled
                                        ? Colors.red.withOpacity(0.1)
                                        : Colors.teal.withOpacity(0.1),
                                    borderRadius: BorderRadius.circular(10),
                                    border: Border.all(
                                      color: _getStatusTextColor(context).withOpacity(0.2),
                                    ),
                                  ),
                                  child: Text(
                                    '\$${widget.recibo.montoTotal?.toStringAsFixed(2) ?? '0.00'}',
                                    style: GoogleFonts.spaceMono(
                                      fontSize: isSmallScreen ? 13 : 14,
                                      fontWeight: FontWeight.bold,
                                      color: _getAmountColor(context),
                                    ),
                                  ),
                                ),
                            ],
                          ),

                          // En móviles, mostramos el monto aquí
                          if (isSmallScreen) ...[
                            const SizedBox(height: 4),
                            Container(
                              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                              decoration: BoxDecoration(
                                color: isPaid
                                    ? Colors.green.withOpacity(0.1)
                                    : isCanceled
                                    ? Colors.red.withOpacity(0.1)
                                    : Colors.teal.withOpacity(0.1),
                                borderRadius: BorderRadius.circular(8),
                                border: Border.all(
                                  color: _getStatusTextColor(context).withOpacity(0.2),
                                ),
                              ),
                              child: Text(
                                '\$${widget.recibo.montoTotal?.toStringAsFixed(2) ?? '0.00'}',
                                style: GoogleFonts.spaceMono(
                                  fontSize: 13,
                                  fontWeight: FontWeight.bold,
                                  color: _getAmountColor(context),
                                ),
                              ),
                            ),
                          ],

                          const SizedBox(height: 6),

                          // Información de ID y fecha
                          Row(
                            children: [
                              Text(
                                'ID: ${widget.recibo.id?.substring(0, 6) ?? 'N/A'}', // Mostrar solo parte del ID
                                style: GoogleFonts.inter(
                                  fontSize: isSmallScreen ? 12 : 13,
                                  color: _getStatusTextColor(context).withOpacity(0.8),
                                ),
                              ),
                              const Spacer(),
                              Text(
                                widget.recibo.fechaEmision != null
                                    ? DateFormat('dd/MM/yy').format(widget.recibo.fechaEmision!)
                                    : 'N/A',
                                style: GoogleFonts.inter(
                                  fontSize: isSmallScreen ? 12 : 13,
                                  color: _getStatusTextColor(context).withOpacity(0.8),
                                ),
                              ),
                            ],
                          ),
                        ],
                      ),
                    ),
                  ],
                ),

                // Detalles (si existen)
                if (widget.recibo.detalles?.isNotEmpty ?? false) ...[
                  const SizedBox(height: 8),
                  Container(
                    padding: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      color: isDarkMode
                          ? Colors.black.withOpacity(0.1)
                          : Colors.white.withOpacity(0.3),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'Detalles:',
                          style: GoogleFonts.inter(
                            fontSize: isSmallScreen ? 12 : 13,
                            fontWeight: FontWeight.w500,
                            color: _getStatusTextColor(context).withOpacity(0.8),
                          ),
                        ),
                        const SizedBox(height: 4),
                        ...(widget.recibo.detalles ?? []).map((detail) => Padding(
                          padding: const EdgeInsets.only(top: 4),
                          child: Row(
                            children: [
                              Container(
                                width: 4,
                                height: 4,
                                margin: const EdgeInsets.only(right: 6),
                                decoration: BoxDecoration(
                                  color: _getStatusTextColor(context),
                                  shape: BoxShape.circle,
                                ),
                              ),
                              Expanded(
                                child: Text(
                                  detail.concepto ?? 'Sin detalle',
                                  style: GoogleFonts.inter(
                                    fontSize: isSmallScreen ? 12 : 13,
                                    color: _getStatusTextColor(context).withOpacity(0.8),
                                  ),
                                  maxLines: 1,
                                  overflow: TextOverflow.ellipsis,
                                ),
                              ),
                              Text(
                                '\$${detail.monto?.toStringAsFixed(2) ?? '0.00'}',
                                style: GoogleFonts.spaceMono(
                                  fontSize: isSmallScreen ? 12 : 13,
                                  color: _getStatusTextColor(context).withOpacity(0.9),
                                  fontWeight: FontWeight.w500,
                                ),
                              ),
                            ],
                          ),
                        )),
                      ],
                    ),
                  ),
                ],

                // Botones de acción (fila inferior en móviles)
                const SizedBox(height: 8),
                Row(
                  mainAxisAlignment: MainAxisAlignment.end,
                  children: [
                    if (!isCanceled && !_isCancelling && !isPaid)
                      TextButton(
                        onPressed: _handleCancel,
                        style: TextButton.styleFrom(
                          padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                          minimumSize: Size.zero,
                        ),
                        child: Text(
                          'CANCELAR',
                          style: GoogleFonts.inter(
                            fontSize: 12,
                            fontWeight: FontWeight.bold,
                            color: Colors.red,
                          ),
                        ),
                      ),
                    if (_isCancelling)
                      const Padding(
                        padding: EdgeInsets.symmetric(horizontal: 12),
                        child: SizedBox(
                          width: 20,
                          height: 20,
                          child: CircularProgressIndicator(strokeWidth: 2),
                        ),
                      ),
                    const SizedBox(width: 8),
                    TextButton(
                      onPressed: widget.onTap,
                      style: TextButton.styleFrom(
                        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                        minimumSize: Size.zero,
                      ),
                      child: Text(
                        'DETALLES',
                        style: GoogleFonts.inter(
                          fontSize: 12,
                          fontWeight: FontWeight.bold,
                          color: _getStatusTextColor(context).withOpacity(0.8),
                        ),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildCompactInfoRow(BuildContext context) {
    return Row(
      children: [
        Text(
          'ID: ${widget.recibo.id ?? 'N/A'}',
          style: GoogleFonts.inter(
            fontSize: 11,
            color: _getStatusTextColor(context).withOpacity(0.8),
          ),
        ),
        const Spacer(),
        Text(
          widget.recibo.fechaEmision != null
              ? DateFormat('dd/MM/yy').format(widget.recibo.fechaEmision!)
              : 'N/A',
          style: GoogleFonts.inter(
            fontSize: 11,
            color: _getStatusTextColor(context).withOpacity(0.8),
          ),
        ),
      ],
    );
  }

  Widget _buildRegularInfoRow(BuildContext context) {
    return Row(
      children: [
        Icon(
          Icons.confirmation_number_outlined,
          size: 14,
          color: _getStatusTextColor(context).withOpacity(0.7),
        ),
        const SizedBox(width: 4),
        Text(
          'ID: ${widget.recibo.id ?? 'N/A'}',
          style: GoogleFonts.inter(
            fontSize: 13,
            color: _getStatusTextColor(context).withOpacity(0.8),
          ),
        ),
        const Spacer(),
        Container(
          padding: const EdgeInsets.symmetric(
            horizontal: 10,
            vertical: 4,
          ),
          decoration: BoxDecoration(
            color: _getStatusTextColor(context).withOpacity(0.1),
            borderRadius: BorderRadius.circular(20),
            border: Border.all(
              color: _getStatusTextColor(context).withOpacity(0.3),
            ),
          ),
          child: Text(
            widget.recibo.estado?.toUpperCase() ?? 'PENDIENTE',
            style: GoogleFonts.inter(
              fontSize: 10,
              fontWeight: FontWeight.bold,
              color: _getStatusTextColor(context),
              letterSpacing: 0.5,
            ),
          ),
        ),
        const SizedBox(width: 12),
        Icon(
          Icons.calendar_today_outlined,
          size: 14,
          color: _getStatusTextColor(context).withOpacity(0.7),
        ),
        const SizedBox(width: 4),
        Text(
          widget.recibo.fechaEmision != null
              ? DateFormat('dd MMM yy').format(widget.recibo.fechaEmision!)
              : 'N/A',
          style: GoogleFonts.inter(
            fontSize: 13,
            color: _getStatusTextColor(context).withOpacity(0.8),
          ),
        ),
      ],
    );
  }

  Widget _buildCompactActionButtons(BuildContext context) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        if (!isCanceled && !_isCancelling && !isPaid)
          IconButton(
            icon: const Icon(Icons.cancel_outlined, size: 20),
            color: Colors.red,
            onPressed: _handleCancel,
            splashRadius: 16,
            padding: EdgeInsets.zero,
          ),
        if (_isCancelling)
          const Padding(
            padding: EdgeInsets.only(top: 4),
            child: SizedBox(
              width: 20,
              height: 20,
              child: CircularProgressIndicator(strokeWidth: 2),
            ),
          ),
        IconButton(
          icon: const Icon(Icons.chevron_right, size: 20),
          color: _getStatusTextColor(context).withOpacity(0.7),
          onPressed: widget.onTap,
          splashRadius: 16,
          padding: EdgeInsets.zero,
        ),
      ],
    );
  }

  Widget _buildRegularActionButtons(BuildContext context) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        if (!isCanceled && !_isCancelling && !isPaid)
          _buildActionButton(
            context,
            icon: Icons.cancel_outlined,
            color: Colors.red,
            onPressed: _handleCancel,
            tooltip: 'Cancelar recibo',
          ),
        if (_isCancelling)
          const Padding(
            padding: EdgeInsets.only(top: 8),
            child: SizedBox(
              width: 24,
              height: 24,
              child: CircularProgressIndicator(strokeWidth: 2),
            ),
          ),
        const SizedBox(height: 8),
        _buildActionButton(
          context,
          icon: Icons.chevron_right,
          color: _getStatusTextColor(context).withOpacity(0.7),
          onPressed: widget.onTap,
          tooltip: 'Ver detalles',
          isMainAction: isPaid,
        ),
      ],
    );
  }

  Widget _buildActionButton(
    BuildContext context, {
    required IconData icon,
    required Color color,
    required VoidCallback? onPressed,
    required String tooltip,
    bool isMainAction = false,
  }) {
    return Tooltip(
      message: tooltip,
      child: Container(
        decoration: isMainAction
            ? BoxDecoration(
                color: color.withOpacity(0.1),
                borderRadius: BorderRadius.circular(12),
                border: Border.all(
                  color: color.withOpacity(0.3),
                  width: 1.5,
                ),
              )
            : null,
        child: IconButton(
          icon: Icon(icon),
          iconSize: isMainAction ? 28 : 24,
          color: color,
          onPressed: onPressed,
          splashRadius: 20,
        ),
      ),
    );
  }

  Future<void> _handleCancel() async {
    if (_isCancelling) return;

    final bool? confirm = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Cancelar recibo'),
        content: Text('¿Cancelar recibo ${widget.recibo.id ?? 'N/A'}?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context, false),
            child: const Text('No'),
          ),
          TextButton(
            onPressed: () => Navigator.pop(context, true),
            child: const Text('Sí'),
          ),
        ],
      ),
    );

    if (confirm != true) return;

    setState(() => _isCancelling = true);

    try {
      final useCase = getIt<CancelarReciboUseCase>();
      await useCase.execute(widget.recibo.id ?? '');

      final alumnoController = Get.find<DetalleAlumnoController>();
      await alumnoController.loadAlumnoData(widget.recibo.alumnoId ?? '');

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Recibo ${widget.recibo.id ?? 'N/A'} cancelado'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error al cancelar: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() => _isCancelling = false);
      }
    }
  }
}
