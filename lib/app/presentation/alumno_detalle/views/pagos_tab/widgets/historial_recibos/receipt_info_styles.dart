import 'package:flutter/material.dart';

class ReceiptInfoStyles {
  static Color iconColor(BuildContext context) {
    final theme = Theme.of(context);
    return theme.brightness == Brightness.dark
        ? Colors.blue[200]!
        : Colors.blue[600]!;
  }

  static Color headerBackgroundColor(BuildContext context) {
    final theme = Theme.of(context);
    return theme.brightness == Brightness.dark
        ? Colors.grey[850]!
        : Colors.white;
  }

  static Color headerTextColor(BuildContext context) {
    final theme = Theme.of(context);
    return theme.textTheme.titleMedium?.color ?? Colors.black;
  }

  // Nuevos estilos para recibos cancelados
  static Color cancelledReceiptColor(BuildContext context) {
    final theme = Theme.of(context);
    return theme.brightness == Brightness.dark
        ? Colors.grey[800]!.withOpacity(0.5)
        : Colors.grey[200]!;
  }

  static Color cancelledTextColor(BuildContext context) {
    final theme = Theme.of(context);
    return theme.brightness == Brightness.dark
        ? Colors.grey[500]!
        : Colors.grey[600]!;
  }

  static Color cancelledAmountColor(BuildContext context) {
    return Colors.red[400]!;
  }
}
