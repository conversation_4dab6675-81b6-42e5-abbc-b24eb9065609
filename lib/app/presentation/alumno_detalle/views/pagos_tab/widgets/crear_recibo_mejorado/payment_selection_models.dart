import '../../../../../../domain/modelos/kardex.dart';
import '../../../../../../domain/modelos/costo_adicional.dart';

/// Modelo para la selección manual de pagos
class PaymentSelection {
  final double totalAmount;
  final List<WeekSelection> weekSelections;
  final List<CostSelection> costSelections;
  final bool isAutoMode;

  PaymentSelection({
    required this.totalAmount,
    required this.weekSelections,
    required this.costSelections,
    this.isAutoMode = true,
  });

  double get totalSelected {
    final weekTotal = weekSelections
        .where((w) => w.isSelected)
        .fold(0.0, (sum, w) => sum + w.amount);
    final costTotal = costSelections
        .where((c) => c.isSelected)
        .fold(0.0, (sum, c) => sum + c.amount);
    return weekTotal + costTotal;
  }

  double get remainingAmount => totalAmount - totalSelected;

  bool get isValid => totalSelected <= totalAmount;

  PaymentSelection copyWith({
    double? totalAmount,
    List<WeekSelection>? weekSelections,
    List<CostSelection>? costSelections,
    bool? isAutoMode,
  }) {
    return PaymentSelection(
      totalAmount: totalAmount ?? this.totalAmount,
      weekSelections: weekSelections ?? this.weekSelections,
      costSelections: costSelections ?? this.costSelections,
      isAutoMode: isAutoMode ?? this.isAutoMode,
    );
  }
}

/// Modelo para la selección de semanas
class WeekSelection {
  final int weekNumber;
  final double amount;
  final double weeklyAmount;
  final bool isExisting;
  final bool isSelected;
  final bool isPending;
  final double pendingAmount;

  WeekSelection({
    required this.weekNumber,
    required this.amount,
    required this.weeklyAmount,
    required this.isExisting,
    required this.isSelected,
    required this.isPending,
    required this.pendingAmount,
  });

  WeekSelection copyWith({
    int? weekNumber,
    double? amount,
    double? weeklyAmount,
    bool? isExisting,
    bool? isSelected,
    bool? isPending,
    double? pendingAmount,
  }) {
    return WeekSelection(
      weekNumber: weekNumber ?? this.weekNumber,
      amount: amount ?? this.amount,
      weeklyAmount: weeklyAmount ?? this.weeklyAmount,
      isExisting: isExisting ?? this.isExisting,
      isSelected: isSelected ?? this.isSelected,
      isPending: isPending ?? this.isPending,
      pendingAmount: pendingAmount ?? this.pendingAmount,
    );
  }

  static WeekSelection fromKardexSemana(KardexSemana semana) {
    final pendingAmount = semana.monto - semana.montoPagado;
    return WeekSelection(
      weekNumber: semana.numeroSemana,
      amount: pendingAmount,
      weeklyAmount: semana.monto,
      isExisting: true,
      isSelected: false,
      isPending: !semana.pagada,
      pendingAmount: pendingAmount,
    );
  }

  static WeekSelection createNew({
    required int weekNumber,
    required double weeklyAmount,
    bool isSelected = false,
  }) {
    return WeekSelection(
      weekNumber: weekNumber,
      amount: weeklyAmount,
      weeklyAmount: weeklyAmount,
      isExisting: false,
      isSelected: isSelected,
      isPending: false,
      pendingAmount: weeklyAmount,
    );
  }
}

/// Modelo para la selección de costos adicionales
class CostSelection {
  final CostoAdicional costoAdicional;
  final double amount;
  final bool isSelected;
  final double pendingAmount;

  CostSelection({
    required this.costoAdicional,
    required this.amount,
    required this.isSelected,
    required this.pendingAmount,
  });

  CostSelection copyWith({
    CostoAdicional? costoAdicional,
    double? amount,
    bool? isSelected,
    double? pendingAmount,
  }) {
    return CostSelection(
      costoAdicional: costoAdicional ?? this.costoAdicional,
      amount: amount ?? this.amount,
      isSelected: isSelected ?? this.isSelected,
      pendingAmount: pendingAmount ?? this.pendingAmount,
    );
  }

  static CostSelection fromCostoAdicional(CostoAdicional costo) {
    final pendingAmount = costo.monto - costo.montoPagado;
    return CostSelection(
      costoAdicional: costo,
      amount: pendingAmount,
      isSelected: false,
      pendingAmount: pendingAmount,
    );
  }
}

/// Modo de distribución de pagos
enum PaymentMode {
  automatic, // Distribución automática (actual)
  manual,    // Selección manual de semanas y costos
}
