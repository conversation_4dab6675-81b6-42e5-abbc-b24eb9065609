import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:google_fonts/google_fonts.dart';
import '../../../../../../domain/entities/alumno.dart';
import '../../../../../../domain/modelos/kardex.dart';
import '../../../../../../domain/modelos/costo_adicional.dart';
import '../../../../../../styles/app_styles.dart';
import 'payment_distribution_engine.dart';

class SimplePaymentForm extends StatefulWidget {
  final Alumno alumno;
  final List<CostoAdicional> costosAdicionales;
  final List<KardexSemana> semanas;
  final double montoPorSemana;
  final Function(Map<String, dynamic>) onGenerarRecibo;

  const SimplePaymentForm({
    super.key,
    required this.alumno,
    required this.costosAdicionales,
    required this.semanas,
    required this.montoPorSemana,
    required this.onGenerarRecibo,
  });

  @override
  State<SimplePaymentForm> createState() => _SimplePaymentFormState();
}

class _SimplePaymentFormState extends State<SimplePaymentForm> {
  final _formKey = GlobalKey<FormState>();
  final _montoController = TextEditingController();
  final _notasController = TextEditingController();

  PaymentDistribution? _currentDistribution;
  String _metodoPago = 'Efectivo';
  final Set<String> _costosSeleccionados = {};

  final List<String> _metodosPago = [
    'Efectivo',
    'Transferencia',
    'Tarjeta de Débito',
    'Tarjeta de Crédito',
    'Cheque'
  ];

  @override
  void initState() {
    super.initState();
    _montoController.addListener(_onAmountChanged);
  }

  @override
  void dispose() {
    _montoController.dispose();
    _notasController.dispose();
    super.dispose();
  }

  void _onAmountChanged() {
    final amount = double.tryParse(_montoController.text) ?? 0;
    if (amount > 0) {
      _calculateDistribution();
    } else {
      setState(() {
        _currentDistribution = null;
      });
    }
  }

  void _calculateDistribution() {
    final amount = double.tryParse(_montoController.text) ?? 0;
    if (amount <= 0) return;

    // Obtener costos adicionales seleccionados
    final costosSeleccionados = widget.costosAdicionales
        .where((costo) => _costosSeleccionados.contains(costo.id))
        .toList();

    final distribution = PaymentDistributionEngine.calculateDistribution(
      amount: amount,
      costosAdicionales: costosSeleccionados,
      semanas: widget.semanas,
      montoPorSemana: widget.montoPorSemana,
    );

    setState(() {
      _currentDistribution = distribution;
    });
  }

  void _onCostoToggled(String costoId, bool isSelected) {
    setState(() {
      if (isSelected) {
        _costosSeleccionados.add(costoId);
      } else {
        _costosSeleccionados.remove(costoId);
      }
    });
    _calculateDistribution();
  }

  double _calculateTotalPendiente() {
    double total = 0;

    // Sumar semanas pendientes
    for (final semana in widget.semanas) {
      if (!semana.pagada) {
        total += semana.monto - semana.montoPagado;
      }
    }

    // Sumar costos adicionales pendientes
    for (final costo in widget.costosAdicionales) {
      if (!costo.pagado) {
        total += costo.monto - costo.montoPagado;
      }
    }

    return total;
  }

  @override
  Widget build(BuildContext context) {
    return Form(
      key: _formKey,
      child: SingleChildScrollView(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildAmountSection(),
            const SizedBox(height: 24),
            _buildPaymentMethodSection(),
            const SizedBox(height: 24),
            if (widget.costosAdicionales.isNotEmpty) ...[
              _buildCostosAdicionalesSection(),
              const SizedBox(height: 24),
            ],
            if (_currentDistribution != null) ...[
              _buildDistributionSection(),
              const SizedBox(height: 24),
            ],
            _buildNotesSection(),
            const SizedBox(height: 32),
            _buildGenerateButton(),
          ],
        ),
      ),
    );
  }

  Widget _buildAmountSection() {
    final totalPendiente = _calculateTotalPendiente();

    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.payments,
                color: AppStyles.primaryColor,
                size: 24,
              ),
              const SizedBox(width: 8),
              Text(
                'Monto a Pagar',
                style: GoogleFonts.poppins(
                  fontSize: 18,
                  fontWeight: FontWeight.w600,
                  color: AppStyles.textColor,
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          TextFormField(
            controller: _montoController,
            keyboardType: const TextInputType.numberWithOptions(decimal: true),
            inputFormatters: [
              FilteringTextInputFormatter.allow(RegExp(r'^\d*\.?\d{0,2}')),
            ],
            style: GoogleFonts.poppins(
              fontSize: 24,
              fontWeight: FontWeight.w600,
              color: AppStyles.textColor,
            ),
            decoration: InputDecoration(
              prefixText: '\$ ',
              prefixStyle: GoogleFonts.poppins(
                fontSize: 24,
                fontWeight: FontWeight.w600,
                color: AppStyles.primaryColor,
              ),
              hintText: '0.00',
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
              ),
              focusedBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
                borderSide: BorderSide(color: AppStyles.primaryColor, width: 2),
              ),
            ),
            validator: (value) {
              if (value == null || value.isEmpty) {
                return 'Ingrese un monto válido';
              }
              final amount = double.tryParse(value);
              if (amount == null || amount <= 0) {
                return 'El monto debe ser mayor a cero';
              }
              return null;
            },
          ),
          const SizedBox(height: 16),
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: AppStyles.primaryColor.withValues(alpha: 0.05),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'Total Pendiente:',
                  style: GoogleFonts.poppins(
                    fontSize: 14,
                    color: AppStyles.secondaryTextColor,
                  ),
                ),
                Text(
                  '\$${totalPendiente.toStringAsFixed(2)}',
                  style: GoogleFonts.poppins(
                    fontSize: 14,
                    fontWeight: FontWeight.w600,
                    color: AppStyles.primaryColor,
                  ),
                ),
              ],
            ),
          ),
          const SizedBox(height: 12),
          _buildQuickSuggestions(),
        ],
      ),
    );
  }

  Widget _buildQuickSuggestions() {
    final suggestions = <double>[];

    // Próxima semana
    if (widget.montoPorSemana > 0) {
      suggestions.add(widget.montoPorSemana);
      suggestions.add(widget.montoPorSemana * 2);
      suggestions.add(widget.montoPorSemana * 4);
    }

    // Montos redondos
    suggestions.addAll([500.0, 1000.0, 2000.0]);

    return Wrap(
      spacing: 8,
      runSpacing: 8,
      children: suggestions.take(6).map((amount) {
        String label = '\$${amount.toStringAsFixed(0)}';
        if (amount == widget.montoPorSemana) {
          label += ' (1 sem)';
        } else if (amount == widget.montoPorSemana * 2) {
          label += ' (2 sem)';
        } else if (amount == widget.montoPorSemana * 4) {
          label += ' (4 sem)';
        }

        return GestureDetector(
          onTap: () {
            _montoController.text = amount.toStringAsFixed(2);
          },
          child: Container(
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
            decoration: BoxDecoration(
              color: AppStyles.primaryColor.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(16),
            ),
            child: Text(
              label,
              style: GoogleFonts.poppins(
                fontSize: 11,
                fontWeight: FontWeight.w500,
                color: AppStyles.primaryColor,
              ),
            ),
          ),
        );
      }).toList(),
    );
  }

  Widget _buildPaymentMethodSection() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.payment,
                color: AppStyles.primaryColor,
                size: 24,
              ),
              const SizedBox(width: 8),
              Text(
                'Método de Pago',
                style: GoogleFonts.poppins(
                  fontSize: 18,
                  fontWeight: FontWeight.w600,
                  color: AppStyles.textColor,
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          DropdownButtonFormField<String>(
            value: _metodoPago,
            decoration: InputDecoration(
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
              ),
              focusedBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
                borderSide: BorderSide(color: AppStyles.primaryColor, width: 2),
              ),
            ),
            items: _metodosPago.map((metodo) {
              return DropdownMenuItem<String>(
                value: metodo,
                child: Text(metodo),
              );
            }).toList(),
            onChanged: (value) {
              setState(() {
                _metodoPago = value!;
              });
            },
          ),
        ],
      ),
    );
  }

  Widget _buildCostosAdicionalesSection() {
    final costosPendientes = widget.costosAdicionales
        .where((costo) => !costo.pagado)
        .toList();

    if (costosPendientes.isEmpty) {
      return const SizedBox.shrink();
    }

    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.add_circle_outline,
                color: AppStyles.primaryColor,
                size: 24,
              ),
              const SizedBox(width: 8),
              Text(
                'Costos Adicionales',
                style: GoogleFonts.poppins(
                  fontSize: 18,
                  fontWeight: FontWeight.w600,
                  color: AppStyles.textColor,
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Text(
            'Selecciona los costos que deseas pagar primero',
            style: GoogleFonts.poppins(
              fontSize: 14,
              color: AppStyles.secondaryTextColor,
            ),
          ),
          const SizedBox(height: 16),
          ...costosPendientes.map((costo) => _buildCostoToggle(costo)),
        ],
      ),
    );
  }

  Widget _buildCostoToggle(CostoAdicional costo) {
    final pendiente = costo.monto - costo.montoPagado;
    final isSelected = _costosSeleccionados.contains(costo.id);

    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      child: CheckboxListTile(
        value: isSelected,
        onChanged: (value) => _onCostoToggled(costo.id, value ?? false),
        title: Text(
          costo.nombre,
          style: GoogleFonts.poppins(
            fontSize: 16,
            fontWeight: FontWeight.w600,
            color: AppStyles.textColor,
          ),
        ),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            if (costo.descripcion.isNotEmpty) ...[
              Text(
                costo.descripcion,
                style: GoogleFonts.poppins(
                  fontSize: 14,
                  color: AppStyles.secondaryTextColor,
                ),
              ),
              const SizedBox(height: 4),
            ],
            Text(
              'Pendiente: \$${pendiente.toStringAsFixed(2)}',
              style: GoogleFonts.poppins(
                fontSize: 14,
                fontWeight: FontWeight.w600,
                color: Colors.orange[700],
              ),
            ),
          ],
        ),
        activeColor: AppStyles.primaryColor,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(8),
        ),
        tileColor: isSelected ? AppStyles.primaryColor.withValues(alpha: 0.1) : null,
      ),
    );
  }
}
