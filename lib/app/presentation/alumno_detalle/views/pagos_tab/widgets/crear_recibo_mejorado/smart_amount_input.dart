import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:google_fonts/google_fonts.dart';
import '../../../../../domain/modelos/kardex.dart';
import '../../../../../domain/modelos/costo_adicional.dart';
import '../../../../../styles/app_styles.dart';
import 'payment_distribution_engine.dart';

class SmartAmountInput extends StatefulWidget {
  final TextEditingController controller;
  final List<CostoAdicional> costosAdicionales;
  final List<KardexSemana> semanas;
  final double montoPorSemana;

  const SmartAmountInput({
    Key? key,
    required this.controller,
    required this.costosAdicionales,
    required this.semanas,
    required this.montoPorSemana,
  }) : super(key: key);

  @override
  State<SmartAmountInput> createState() => _SmartAmountInputState();
}

class _SmartAmountInputState extends State<SmartAmountInput> {
  double _totalPendiente = 0;
  double _proximaSemana = 0;
  List<String> _sugerencias = [];

  @override
  void initState() {
    super.initState();
    _calculateSuggestions();
  }

  void _calculateSuggestions() {
    _totalPendiente = PaymentDistributionEngine.calculateTotalPending(
      costosAdicionales: widget.costosAdicionales,
      semanas: widget.semanas,
    );

    _proximaSemana = widget.montoPorSemana;

    _sugerencias = _generateSuggestions();
  }

  List<String> _generateSuggestions() {
    final suggestions = <String>[];

    // Sugerencia 1: Próxima semana
    if (_proximaSemana > 0) {
      suggestions.add(_proximaSemana.toStringAsFixed(2));
    }

    // Sugerencia 2: Dos semanas
    if (_proximaSemana > 0) {
      suggestions.add((_proximaSemana * 2).toStringAsFixed(2));
    }

    // Sugerencia 3: Total pendiente
    if (_totalPendiente > 0) {
      suggestions.add(_totalPendiente.toStringAsFixed(2));
    }

    // Sugerencia 4: Montos redondos comunes
    final montosComunes = [100.0, 200.0, 500.0, 1000.0];
    for (final monto in montosComunes) {
      if (monto <= _totalPendiente * 1.5 && !suggestions.contains(monto.toStringAsFixed(2))) {
        suggestions.add(monto.toStringAsFixed(2));
      }
    }

    // Remover duplicados y ordenar
    final uniqueSuggestions = suggestions.toSet().toList();
    uniqueSuggestions.sort((a, b) => double.parse(a).compareTo(double.parse(b)));

    return uniqueSuggestions.take(4).toList();
  }

  void _selectSuggestion(String amount) {
    widget.controller.text = amount;
    widget.controller.selection = TextSelection.fromPosition(
      TextPosition(offset: widget.controller.text.length),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.payments,
                color: AppStyles.primaryColor,
                size: 24,
              ),
              const SizedBox(width: 8),
              Text(
                'Monto a Pagar',
                style: GoogleFonts.poppins(
                  fontSize: 18,
                  fontWeight: FontWeight.w600,
                  color: AppStyles.textColor,
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          
          // Input principal
          TextFormField(
            controller: widget.controller,
            keyboardType: const TextInputType.numberWithOptions(decimal: true),
            inputFormatters: [
              FilteringTextInputFormatter.allow(RegExp(r'^\d*\.?\d{0,2}')),
            ],
            style: GoogleFonts.poppins(
              fontSize: 24,
              fontWeight: FontWeight.w600,
              color: AppStyles.textColor,
            ),
            decoration: InputDecoration(
              prefixText: '\$ ',
              prefixStyle: GoogleFonts.poppins(
                fontSize: 24,
                fontWeight: FontWeight.w600,
                color: AppStyles.primaryColor,
              ),
              hintText: '0.00',
              hintStyle: GoogleFonts.poppins(
                fontSize: 24,
                fontWeight: FontWeight.w400,
                color: Colors.grey[400],
              ),
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
                borderSide: BorderSide(color: Colors.grey[300]!),
              ),
              focusedBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
                borderSide: BorderSide(color: AppStyles.primaryColor, width: 2),
              ),
              errorBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
                borderSide: const BorderSide(color: Colors.red, width: 2),
              ),
              contentPadding: const EdgeInsets.symmetric(
                horizontal: 16,
                vertical: 16,
              ),
            ),
            validator: (value) {
              if (value == null || value.isEmpty) {
                return 'Ingrese un monto válido';
              }
              final amount = double.tryParse(value);
              if (amount == null || amount <= 0) {
                return 'El monto debe ser mayor a cero';
              }
              if (amount > 999999) {
                return 'El monto es demasiado alto';
              }
              return null;
            },
          ),

          const SizedBox(height: 16),

          // Información contextual
          _buildContextInfo(),

          const SizedBox(height: 16),

          // Sugerencias rápidas
          if (_sugerencias.isNotEmpty) _buildQuickSuggestions(),
        ],
      ),
    );
  }

  Widget _buildContextInfo() {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: AppStyles.primaryColor.withOpacity(0.05),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: AppStyles.primaryColor.withOpacity(0.2),
        ),
      ),
      child: Column(
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'Total Pendiente:',
                style: GoogleFonts.poppins(
                  fontSize: 14,
                  color: AppStyles.secondaryTextColor,
                ),
              ),
              Text(
                '\$${_totalPendiente.toStringAsFixed(2)}',
                style: GoogleFonts.poppins(
                  fontSize: 14,
                  fontWeight: FontWeight.w600,
                  color: AppStyles.primaryColor,
                ),
              ),
            ],
          ),
          if (_proximaSemana > 0) ...[
            const SizedBox(height: 4),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'Próxima Semana:',
                  style: GoogleFonts.poppins(
                    fontSize: 14,
                    color: AppStyles.secondaryTextColor,
                  ),
                ),
                Text(
                  '\$${_proximaSemana.toStringAsFixed(2)}',
                  style: GoogleFonts.poppins(
                    fontSize: 14,
                    fontWeight: FontWeight.w600,
                    color: Colors.green[600],
                  ),
                ),
              ],
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildQuickSuggestions() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Sugerencias Rápidas',
          style: GoogleFonts.poppins(
            fontSize: 14,
            fontWeight: FontWeight.w500,
            color: AppStyles.secondaryTextColor,
          ),
        ),
        const SizedBox(height: 8),
        Wrap(
          spacing: 8,
          runSpacing: 8,
          children: _sugerencias.map((suggestion) {
            final amount = double.parse(suggestion);
            String label = '\$${amount.toStringAsFixed(2)}';
            
            // Agregar etiquetas descriptivas
            if (amount == _proximaSemana) {
              label += ' (1 semana)';
            } else if (amount == _proximaSemana * 2) {
              label += ' (2 semanas)';
            } else if (amount == _totalPendiente) {
              label += ' (Todo)';
            }

            return GestureDetector(
              onTap: () => _selectSuggestion(suggestion),
              child: Container(
                padding: const EdgeInsets.symmetric(
                  horizontal: 12,
                  vertical: 8,
                ),
                decoration: BoxDecoration(
                  color: AppStyles.primaryColor.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(20),
                  border: Border.all(
                    color: AppStyles.primaryColor.withOpacity(0.3),
                  ),
                ),
                child: Text(
                  label,
                  style: GoogleFonts.poppins(
                    fontSize: 12,
                    fontWeight: FontWeight.w500,
                    color: AppStyles.primaryColor,
                  ),
                ),
              ),
            );
          }).toList(),
        ),
      ],
    );
  }
}
