import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:google_fonts/google_fonts.dart';
import '../../../../../../domain/entities/alumno.dart';
import '../../../../../../domain/modelos/kardex.dart';
import '../../../../../../domain/modelos/costo_adicional.dart';
import '../../../../../../styles/app_styles.dart';
import 'payment_distribution_engine.dart';

class InlinePaymentForm extends StatefulWidget {
  final Alumno alumno;
  final List<CostoAdicional> costosAdicionales;
  final List<KardexSemana> semanas;
  final double montoPorSemana;
  final Function(Map<String, dynamic>) onGenerarRecibo;

  const InlinePaymentForm({
    Key? key,
    required this.alumno,
    required this.costosAdicionales,
    required this.semanas,
    required this.montoPorSemana,
    required this.onGenerarRecibo,
  }) : super(key: key);

  @override
  State<InlinePaymentForm> createState() => _InlinePaymentFormState();
}

class _InlinePaymentFormState extends State<InlinePaymentForm>
    with TickerProviderStateMixin {
  final _formKey = GlobalKey<FormState>();
  final _montoController = TextEditingController();
  final _notasController = TextEditingController();

  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;

  PaymentDistribution? _currentDistribution;
  bool _showDistribution = false;
  String _metodoPago = 'Efectivo';

  // Control de costos adicionales
  final Map<String, bool> _costosSeleccionados = {};

  final List<String> _metodosPago = [
    'Efectivo',
    'Transferencia',
    'Tarjeta de Débito',
    'Tarjeta de Crédito',
    'Cheque'
  ];

  @override
  void initState() {
    super.initState();
    _setupAnimations();
    _montoController.addListener(_onAmountChanged);
    _initializeCostosSelection();
  }

  void _setupAnimations() {
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 400),
      vsync: this,
    );

    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));
  }

  void _initializeCostosSelection() {
    // Inicializar todos los costos como no seleccionados
    for (final costo in widget.costosAdicionales) {
      _costosSeleccionados[costo.id] = false;
    }
  }

  @override
  void dispose() {
    _animationController.dispose();
    _montoController.dispose();
    _notasController.dispose();
    super.dispose();
  }

  void _onAmountChanged() {
    final amount = double.tryParse(_montoController.text) ?? 0;
    if (amount > 0) {
      _calculateDistribution();
    } else {
      setState(() {
        _currentDistribution = null;
        _showDistribution = false;
      });
      _animationController.reverse();
    }
  }

  void _calculateDistribution() {
    final amount = double.tryParse(_montoController.text) ?? 0;
    if (amount <= 0) return;

    // Filtrar solo los costos seleccionados
    final costosSeleccionados = widget.costosAdicionales
        .where((costo) => _costosSeleccionados[costo.id] == true)
        .toList();

    final distribution = PaymentDistributionEngine.calculateDistribution(
      amount: amount,
      costosAdicionales: costosSeleccionados,
      semanas: widget.semanas,
      montoPorSemana: widget.montoPorSemana,
    );

    setState(() {
      _currentDistribution = distribution;
      _showDistribution = true;
    });

    if (!_animationController.isAnimating) {
      _animationController.forward();
    }
  }

  void _onCostoToggled(String costoId, bool selected) {
    setState(() {
      _costosSeleccionados[costoId] = selected;
    });
    _calculateDistribution();
  }

  @override
  Widget build(BuildContext context) {
    return Form(
      key: _formKey,
      child: SingleChildScrollView(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildAmountSection(),
            const SizedBox(height: 24),
            _buildPaymentMethodSection(),
            const SizedBox(height: 24),
            _buildCostosAdicionalesSection(),
            const SizedBox(height: 24),
            if (_showDistribution && _currentDistribution != null) ...[
              _buildDistributionSection(),
              const SizedBox(height: 24),
            ],
            _buildNotesSection(),
            const SizedBox(height: 32),
            _buildGenerateButton(),
          ],
        ),
      ),
    );
  }

  Widget _buildAmountSection() {
    final totalPendiente = _calculateTotalPendiente();

    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.payments,
                color: AppStyles.primaryColor,
                size: 24,
              ),
              const SizedBox(width: 8),
              Text(
                'Monto a Pagar',
                style: GoogleFonts.poppins(
                  fontSize: 18,
                  fontWeight: FontWeight.w600,
                  color: AppStyles.textColor,
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),

          // Input principal
          TextFormField(
            controller: _montoController,
            keyboardType: const TextInputType.numberWithOptions(decimal: true),
            inputFormatters: [
              FilteringTextInputFormatter.allow(RegExp(r'^\d*\.?\d{0,2}')),
            ],
            style: GoogleFonts.poppins(
              fontSize: 24,
              fontWeight: FontWeight.w600,
              color: AppStyles.textColor,
            ),
            decoration: InputDecoration(
              prefixText: '\$ ',
              prefixStyle: GoogleFonts.poppins(
                fontSize: 24,
                fontWeight: FontWeight.w600,
                color: AppStyles.primaryColor,
              ),
              hintText: '0.00',
              hintStyle: GoogleFonts.poppins(
                fontSize: 24,
                fontWeight: FontWeight.w400,
                color: Colors.grey[400],
              ),
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
                borderSide: BorderSide(color: Colors.grey[300]!),
              ),
              focusedBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
                borderSide: BorderSide(color: AppStyles.primaryColor, width: 2),
              ),
              contentPadding: const EdgeInsets.symmetric(
                horizontal: 16,
                vertical: 16,
              ),
            ),
            validator: (value) {
              if (value == null || value.isEmpty) {
                return 'Ingrese un monto válido';
              }
              final amount = double.tryParse(value);
              if (amount == null || amount <= 0) {
                return 'El monto debe ser mayor a cero';
              }
              return null;
            },
          ),

          const SizedBox(height: 16),

          // Información contextual
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: AppStyles.primaryColor.withOpacity(0.05),
              borderRadius: BorderRadius.circular(8),
              border: Border.all(
                color: AppStyles.primaryColor.withOpacity(0.2),
              ),
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'Total Pendiente:',
                  style: GoogleFonts.poppins(
                    fontSize: 14,
                    color: AppStyles.secondaryTextColor,
                  ),
                ),
                Text(
                  '\$${totalPendiente.toStringAsFixed(2)}',
                  style: GoogleFonts.poppins(
                    fontSize: 14,
                    fontWeight: FontWeight.w600,
                    color: AppStyles.primaryColor,
                  ),
                ),
              ],
            ),
          ),

          const SizedBox(height: 12),

          // Sugerencias rápidas
          _buildQuickSuggestions(),
        ],
      ),
    );
  }

  Widget _buildQuickSuggestions() {
    final suggestions = <String>[];

    // Próxima semana
    if (widget.montoPorSemana > 0) {
      suggestions.add(widget.montoPorSemana.toStringAsFixed(2));
    }

    // Dos semanas
    if (widget.montoPorSemana > 0) {
      suggestions.add((widget.montoPorSemana * 2).toStringAsFixed(2));
    }

    // Montos redondos
    final montosComunes = [100.0, 200.0, 500.0, 1000.0];
    for (final monto in montosComunes) {
      if (!suggestions.contains(monto.toStringAsFixed(2))) {
        suggestions.add(monto.toStringAsFixed(2));
      }
    }

    if (suggestions.isEmpty) return const SizedBox.shrink();

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Sugerencias:',
          style: GoogleFonts.poppins(
            fontSize: 12,
            fontWeight: FontWeight.w500,
            color: AppStyles.secondaryTextColor,
          ),
        ),
        const SizedBox(height: 8),
        Wrap(
          spacing: 8,
          runSpacing: 8,
          children: suggestions.take(4).map((suggestion) {
            final amount = double.parse(suggestion);
            String label = '\$${amount.toStringAsFixed(2)}';

            if (amount == widget.montoPorSemana) {
              label += ' (1 sem)';
            } else if (amount == widget.montoPorSemana * 2) {
              label += ' (2 sem)';
            }

            return GestureDetector(
              onTap: () {
                _montoController.text = suggestion;
                _montoController.selection = TextSelection.fromPosition(
                  TextPosition(offset: _montoController.text.length),
                );
              },
              child: Container(
                padding: const EdgeInsets.symmetric(
                  horizontal: 12,
                  vertical: 6,
                ),
                decoration: BoxDecoration(
                  color: AppStyles.primaryColor.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(16),
                  border: Border.all(
                    color: AppStyles.primaryColor.withOpacity(0.3),
                  ),
                ),
                child: Text(
                  label,
                  style: GoogleFonts.poppins(
                    fontSize: 11,
                    fontWeight: FontWeight.w500,
                    color: AppStyles.primaryColor,
                  ),
                ),
              ),
            );
          }).toList(),
        ),
      ],
    );
  }

  double _calculateTotalPendiente() {
    double total = 0;

    // Sumar semanas pendientes
    for (final semana in widget.semanas) {
      if (!semana.pagada) {
        total += semana.monto - semana.montoPagado;
      }
    }

    // Sumar costos adicionales pendientes
    for (final costo in widget.costosAdicionales) {
      if (!costo.pagado) {
        total += costo.monto - costo.montoPagado;
      }
    }

    return total;
  }

  Widget _buildPaymentMethodSection() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.payment,
                color: AppStyles.primaryColor,
                size: 24,
              ),
              const SizedBox(width: 8),
              Text(
                'Método de Pago',
                style: GoogleFonts.poppins(
                  fontSize: 18,
                  fontWeight: FontWeight.w600,
                  color: AppStyles.textColor,
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          DropdownButtonFormField<String>(
            value: _metodoPago,
            decoration: InputDecoration(
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
                borderSide: BorderSide(color: Colors.grey[300]!),
              ),
              focusedBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
                borderSide: BorderSide(color: AppStyles.primaryColor, width: 2),
              ),
              contentPadding: const EdgeInsets.symmetric(
                horizontal: 16,
                vertical: 16,
              ),
            ),
            items: _metodosPago.map((metodo) {
              return DropdownMenuItem<String>(
                value: metodo,
                child: Text(
                  metodo,
                  style: GoogleFonts.poppins(
                    fontSize: 16,
                    color: AppStyles.textColor,
                  ),
                ),
              );
            }).toList(),
            onChanged: (value) {
              setState(() {
                _metodoPago = value!;
              });
            },
          ),
        ],
      ),
    );
  }

  Widget _buildCostosAdicionalesSection() {
    final costosPendientes = widget.costosAdicionales
        .where((costo) => !costo.pagado)
        .toList();

    if (costosPendientes.isEmpty) {
      return const SizedBox.shrink();
    }

    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.add_circle_outline,
                color: AppStyles.primaryColor,
                size: 24,
              ),
              const SizedBox(width: 8),
              Text(
                'Costos Adicionales',
                style: GoogleFonts.poppins(
                  fontSize: 18,
                  fontWeight: FontWeight.w600,
                  color: AppStyles.textColor,
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Text(
            'Selecciona los costos adicionales que deseas incluir en este pago',
            style: GoogleFonts.poppins(
              fontSize: 14,
              color: AppStyles.secondaryTextColor,
            ),
          ),
          const SizedBox(height: 16),
          ...costosPendientes.map((costo) => _buildCostoToggle(costo)),
        ],
      ),
    );
  }

  Widget _buildCostoToggle(CostoAdicional costo) {
    final pendiente = costo.monto - costo.montoPagado;
    final isSelected = _costosSeleccionados[costo.id] ?? false;

    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: isSelected
            ? AppStyles.primaryColor.withOpacity(0.1)
            : Colors.grey[50],
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: isSelected
              ? AppStyles.primaryColor
              : Colors.grey[300]!,
          width: isSelected ? 2 : 1,
        ),
      ),
      child: Row(
        children: [
          Checkbox(
            value: isSelected,
            onChanged: (value) => _onCostoToggled(costo.id, value ?? false),
            activeColor: AppStyles.primaryColor,
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  costo.nombre,
                  style: GoogleFonts.poppins(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                    color: AppStyles.textColor,
                  ),
                ),
                if (costo.descripcion.isNotEmpty) ...[
                  const SizedBox(height: 4),
                  Text(
                    costo.descripcion,
                    style: GoogleFonts.poppins(
                      fontSize: 14,
                      color: AppStyles.secondaryTextColor,
                    ),
                  ),
                ],
                const SizedBox(height: 8),
                Row(
                  children: [
                    Text(
                      'Pendiente: ',
                      style: GoogleFonts.poppins(
                        fontSize: 14,
                        color: AppStyles.secondaryTextColor,
                      ),
                    ),
                    Text(
                      '\$${pendiente.toStringAsFixed(2)}',
                      style: GoogleFonts.poppins(
                        fontSize: 14,
                        fontWeight: FontWeight.w600,
                        color: Colors.orange[700],
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDistributionSection() {
    if (_currentDistribution == null) return const SizedBox.shrink();

    return FadeTransition(
      opacity: _fadeAnimation,
      child: Container(
        padding: const EdgeInsets.all(20),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(16),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.05),
              blurRadius: 10,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.account_balance_wallet,
                  color: AppStyles.primaryColor,
                  size: 24,
                ),
                const SizedBox(width: 8),
                Text(
                  'Distribución del Pago',
                  style: GoogleFonts.poppins(
                    fontSize: 18,
                    fontWeight: FontWeight.w600,
                    color: AppStyles.textColor,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),

            // Resumen de distribución
            _buildDistributionSummary(),

            const SizedBox(height: 16),

            // Detalles de semanas
            if (_currentDistribution!.weekDistributions.isNotEmpty) ...[
              _buildWeekDistributionDetails(),
              const SizedBox(height: 16),
            ],

            // Detalles de costos adicionales
            if (_currentDistribution!.costDistributions.isNotEmpty) ...[
              _buildCostDistributionDetails(),
              const SizedBox(height: 16),
            ],

            // Monto restante si lo hay
            if (_currentDistribution!.remainingAmount > 0.01) ...[
              _buildRemainingAmount(),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildDistributionSummary() {
    final distribution = _currentDistribution!;

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: AppStyles.primaryColor.withOpacity(0.05),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: AppStyles.primaryColor.withOpacity(0.2),
        ),
      ),
      child: Column(
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'Monto Total:',
                style: GoogleFonts.poppins(
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                  color: AppStyles.textColor,
                ),
              ),
              Text(
                '\$${distribution.totalAmount.toStringAsFixed(2)}',
                style: GoogleFonts.poppins(
                  fontSize: 16,
                  fontWeight: FontWeight.w700,
                  color: AppStyles.primaryColor,
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'Distribuido:',
                style: GoogleFonts.poppins(
                  fontSize: 14,
                  color: AppStyles.secondaryTextColor,
                ),
              ),
              Text(
                '\$${distribution.totalDistributed.toStringAsFixed(2)}',
                style: GoogleFonts.poppins(
                  fontSize: 14,
                  fontWeight: FontWeight.w600,
                  color: Colors.green[700],
                ),
              ),
            ],
          ),
          if (distribution.remainingAmount > 0.01) ...[
            const SizedBox(height: 8),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'Restante:',
                  style: GoogleFonts.poppins(
                    fontSize: 14,
                    color: AppStyles.secondaryTextColor,
                  ),
                ),
                Text(
                  '\$${distribution.remainingAmount.toStringAsFixed(2)}',
                  style: GoogleFonts.poppins(
                    fontSize: 14,
                    fontWeight: FontWeight.w600,
                    color: Colors.orange[700],
                  ),
                ),
              ],
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildWeekDistributionDetails() {
    final weekDistributions = _currentDistribution!.weekDistributions;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Semanas (${weekDistributions.length})',
          style: GoogleFonts.poppins(
            fontSize: 16,
            fontWeight: FontWeight.w600,
            color: AppStyles.textColor,
          ),
        ),
        const SizedBox(height: 12),
        ...weekDistributions.map((week) => Container(
          margin: const EdgeInsets.only(bottom: 8),
          padding: const EdgeInsets.all(12),
          decoration: BoxDecoration(
            color: week.isExisting ? Colors.orange[50] : Colors.green[50],
            borderRadius: BorderRadius.circular(8),
            border: Border.all(
              color: week.isExisting ? Colors.orange[200]! : Colors.green[200]!,
            ),
          ),
          child: Row(
            children: [
              Icon(
                week.isExisting ? Icons.schedule : Icons.add_circle,
                size: 16,
                color: week.isExisting ? Colors.orange[700] : Colors.green[700],
              ),
              const SizedBox(width: 8),
              Expanded(
                child: Text(
                  'Semana ${week.numeroSemana} ${week.isExisting ? "(Pendiente)" : "(Nueva)"}',
                  style: GoogleFonts.poppins(
                    fontSize: 14,
                    fontWeight: FontWeight.w500,
                    color: AppStyles.textColor,
                  ),
                ),
              ),
              Text(
                '\$${week.amount.toStringAsFixed(2)}',
                style: GoogleFonts.poppins(
                  fontSize: 14,
                  fontWeight: FontWeight.w600,
                  color: week.isExisting ? Colors.orange[700] : Colors.green[700],
                ),
              ),
              if (!week.isFullyPaid) ...[
                const SizedBox(width: 8),
                Icon(
                  Icons.info_outline,
                  size: 16,
                  color: Colors.amber[700],
                ),
              ],
            ],
          ),
        )),
      ],
    );
  }

  Widget _buildCostDistributionDetails() {
    final costDistributions = _currentDistribution!.costDistributions;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Costos Adicionales (${costDistributions.length})',
          style: GoogleFonts.poppins(
            fontSize: 16,
            fontWeight: FontWeight.w600,
            color: AppStyles.textColor,
          ),
        ),
        const SizedBox(height: 12),
        ...costDistributions.map((cost) => Container(
          margin: const EdgeInsets.only(bottom: 8),
          padding: const EdgeInsets.all(12),
          decoration: BoxDecoration(
            color: Colors.blue[50],
            borderRadius: BorderRadius.circular(8),
            border: Border.all(
              color: Colors.blue[200]!,
            ),
          ),
          child: Row(
            children: [
              Icon(
                cost.isFullyPaid ? Icons.check_circle : Icons.schedule,
                size: 16,
                color: cost.isFullyPaid ? Colors.green[700] : Colors.blue[700],
              ),
              const SizedBox(width: 8),
              Expanded(
                child: Text(
                  cost.costoAdicional.nombre,
                  style: GoogleFonts.poppins(
                    fontSize: 14,
                    fontWeight: FontWeight.w500,
                    color: AppStyles.textColor,
                  ),
                ),
              ),
              Text(
                '\$${cost.amount.toStringAsFixed(2)}',
                style: GoogleFonts.poppins(
                  fontSize: 14,
                  fontWeight: FontWeight.w600,
                  color: Colors.blue[700],
                ),
              ),
              if (!cost.isFullyPaid) ...[
                const SizedBox(width: 8),
                Text(
                  '(Parcial)',
                  style: GoogleFonts.poppins(
                    fontSize: 12,
                    color: Colors.amber[700],
                  ),
                ),
              ],
            ],
          ),
        )),
      ],
    );
  }

  Widget _buildRemainingAmount() {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.amber[50],
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: Colors.amber[300]!,
        ),
      ),
      child: Row(
        children: [
          Icon(
            Icons.info_outline,
            color: Colors.amber[700],
            size: 20,
          ),
          const SizedBox(width: 8),
          Expanded(
            child: Text(
              'Monto restante: \$${_currentDistribution!.remainingAmount.toStringAsFixed(2)}',
              style: GoogleFonts.poppins(
                fontSize: 14,
                fontWeight: FontWeight.w500,
                color: Colors.amber[800],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildNotesSection() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.note_add,
                color: AppStyles.primaryColor,
                size: 24,
              ),
              const SizedBox(width: 8),
              Text(
                'Notas (Opcional)',
                style: GoogleFonts.poppins(
                  fontSize: 18,
                  fontWeight: FontWeight.w600,
                  color: AppStyles.textColor,
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          TextFormField(
            controller: _notasController,
            maxLines: 3,
            decoration: InputDecoration(
              hintText: 'Agregar notas o comentarios sobre este pago...',
              hintStyle: GoogleFonts.poppins(
                fontSize: 14,
                color: Colors.grey[400],
              ),
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
                borderSide: BorderSide(color: Colors.grey[300]!),
              ),
              focusedBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
                borderSide: BorderSide(color: AppStyles.primaryColor, width: 2),
              ),
              contentPadding: const EdgeInsets.all(16),
            ),
            style: GoogleFonts.poppins(
              fontSize: 14,
              color: AppStyles.textColor,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildGenerateButton() {
    final canGenerate = _formKey.currentState?.validate() ?? false;
    final hasAmount = (double.tryParse(_montoController.text) ?? 0) > 0;
    final hasDistribution = _currentDistribution != null &&
                           _currentDistribution!.totalDistributed > 0;

    return SizedBox(
      width: double.infinity,
      height: 56,
      child: ElevatedButton(
        onPressed: (canGenerate && hasAmount && hasDistribution)
            ? _generateRecibo
            : null,
        style: ElevatedButton.styleFrom(
          backgroundColor: AppStyles.primaryColor,
          foregroundColor: Colors.white,
          elevation: 2,
          shadowColor: AppStyles.primaryColor.withOpacity(0.3),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
          ),
          disabledBackgroundColor: Colors.grey[300],
          disabledForegroundColor: Colors.grey[600],
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.receipt_long,
              size: 24,
            ),
            const SizedBox(width: 12),
            Text(
              'Generar Recibo',
              style: GoogleFonts.poppins(
                fontSize: 18,
                fontWeight: FontWeight.w600,
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _generateRecibo() {
    if (!_formKey.currentState!.validate()) return;
    if (_currentDistribution == null) return;

    final amount = double.tryParse(_montoController.text) ?? 0;
    if (amount <= 0) return;

    // Crear los detalles del pago
    final detalles = _currentDistribution!.toDetallesPago(widget.alumno.id);

    // Crear el concepto del recibo
    String concepto = 'Pago de ';
    final conceptos = <String>[];

    if (_currentDistribution!.weekDistributions.isNotEmpty) {
      final semanas = _currentDistribution!.weekDistributions.length;
      conceptos.add('$semanas semana${semanas > 1 ? 's' : ''}');
    }

    if (_currentDistribution!.costDistributions.isNotEmpty) {
      final costos = _currentDistribution!.costDistributions.length;
      conceptos.add('$costos costo${costos > 1 ? 's' : ''} adicional${costos > 1 ? 'es' : ''}');
    }

    concepto += conceptos.join(' y ');

    // Agregar notas si las hay
    final notas = _notasController.text.trim();
    if (notas.isNotEmpty) {
      concepto += ' - $notas';
    }

    // Preparar los datos para el callback
    final reciboData = {
      'alumno': widget.alumno,
      'monto': amount,
      'metodoPago': _metodoPago,
      'concepto': concepto,
      'detalles': detalles,
      'distribution': _currentDistribution,
      'notas': notas,
    };

    // Llamar al callback
    widget.onGenerarRecibo(reciboData);
  }
}
