import '../../../../../../domain/modelos/costo_adicional.dart';
import '../../../../../../domain/modelos/kardex.dart';
import 'payment_distribution_engine.dart';
import 'payment_selection_models.dart';

class EnhancedPaymentEngine {
  /// Calcula la distribución automática (comportamiento actual mejorado)
  static PaymentDistribution calculateAutomaticDistribution({
    required double amount,
    required List<CostoAdicional> selectedCosts,
    required List<KardexSemana> semanas,
    required double montoPorSemana,
  }) {
    return PaymentDistributionEngine.calculateDistribution(
      amount: amount,
      costosAdicionales: selectedCosts,
      semanas: semanas,
      montoPorSemana: montoPorSemana,
    );
  }

  /// Calcula la distribución manual basada en las selecciones del usuario
  static PaymentDistribution calculateManualDistribution({
    required PaymentSelection selection,
    required double montoPorSemana,
  }) {
    final costDistributions = <CostDistribution>[];
    final weekDistributions = <WeekDistribution>[];

    // Procesar costos adicionales seleccionados
    for (final costSelection in selection.costSelections) {
      if (costSelection.isSelected && costSelection.amount > 0) {
        costDistributions.add(CostDistribution(
          costoAdicional: costSelection.costoAdicional,
          amount: costSelection.amount,
          pendingBefore: costSelection.pendingAmount,
          pendingAfter: costSelection.pendingAmount - costSelection.amount,
          isFullyPaid: costSelection.amount >= costSelection.pendingAmount,
        ));
      }
    }

    // Procesar semanas seleccionadas
    for (final weekSelection in selection.weekSelections) {
      if (weekSelection.isSelected && weekSelection.amount > 0) {
        weekDistributions.add(WeekDistribution(
          numeroSemana: weekSelection.weekNumber,
          amount: weekSelection.amount,
          weeklyAmount: weekSelection.weeklyAmount,
          isExisting: weekSelection.isExisting,
          isFullyPaid: weekSelection.amount >= weekSelection.pendingAmount,
          pendingBefore: weekSelection.pendingAmount,
          pendingAfter: weekSelection.pendingAmount - weekSelection.amount,
        ));
      }
    }

    return PaymentDistribution(
      totalAmount: selection.totalAmount,
      costDistributions: costDistributions,
      weekDistributions: weekDistributions,
      remainingAmount: selection.remainingAmount,
      isComplete: selection.remainingAmount <= 0.01,
    );
  }

  /// Inicializa las selecciones para el modo manual
  static PaymentSelection initializePaymentSelection({
    required double totalAmount,
    required List<CostoAdicional> costosAdicionales,
    required List<KardexSemana> semanas,
    required double montoPorSemana,
  }) {
    // Crear selecciones de costos adicionales
    final costSelections = costosAdicionales
        .where((costo) => !costo.pagado)
        .map((costo) => CostSelection.fromCostoAdicional(costo))
        .toList();

    // Crear selecciones de semanas existentes
    final weekSelections = <WeekSelection>[];
    
    // Agregar semanas pendientes
    for (final semana in semanas) {
      if (!semana.pagada) {
        weekSelections.add(WeekSelection.fromKardexSemana(semana));
      }
    }

    // Agregar algunas semanas futuras para selección
    final maxWeek = semanas.isNotEmpty 
        ? semanas.map((s) => s.numeroSemana).reduce((a, b) => a > b ? a : b)
        : 0;
    
    for (int i = 1; i <= 5; i++) {
      final weekNumber = maxWeek + i;
      weekSelections.add(WeekSelection.createNew(
        weekNumber: weekNumber,
        weeklyAmount: montoPorSemana,
      ));
    }

    return PaymentSelection(
      totalAmount: totalAmount,
      weekSelections: weekSelections,
      costSelections: costSelections,
      isAutoMode: false,
    );
  }

  /// Actualiza una selección de semana con distribución inteligente
  static PaymentSelection updateWeekSelection({
    required PaymentSelection selection,
    required int weekNumber,
    required bool isSelected,
    double? customAmount,
  }) {
    if (!isSelected) {
      // Si se deselecciona, simplemente poner amount en 0
      final updatedWeeks = selection.weekSelections.map((week) {
        if (week.weekNumber == weekNumber) {
          return week.copyWith(isSelected: false, amount: 0);
        }
        return week;
      }).toList();
      return selection.copyWith(weekSelections: updatedWeeks);
    }

    // Si se selecciona, calcular cuánto se puede asignar
    final currentWeek = selection.weekSelections.firstWhere(
      (week) => week.weekNumber == weekNumber,
    );

    // Calcular el monto disponible (excluyendo la semana actual)
    final otherWeeksTotal = selection.weekSelections
        .where((week) => week.weekNumber != weekNumber && week.isSelected)
        .fold(0.0, (sum, week) => sum + week.amount);

    final otherCostsTotal = selection.costSelections
        .where((cost) => cost.isSelected)
        .fold(0.0, (sum, cost) => sum + cost.amount);

    final availableAmount = selection.totalAmount - otherWeeksTotal - otherCostsTotal;

    // Determinar el monto a asignar
    final targetAmount = customAmount ??
        (availableAmount >= currentWeek.pendingAmount
            ? currentWeek.pendingAmount
            : availableAmount);

    final finalAmount = targetAmount.clamp(0.0,
        [currentWeek.pendingAmount, availableAmount].reduce((a, b) => a < b ? a : b)).toDouble();

    final updatedWeeks = selection.weekSelections.map((week) {
      if (week.weekNumber == weekNumber) {
        return week.copyWith(
          isSelected: finalAmount > 0,
          amount: finalAmount,
        );
      }
      return week;
    }).toList();

    return selection.copyWith(weekSelections: updatedWeeks);
  }

  /// Actualiza el monto de una semana específica
  static PaymentSelection updateWeekAmount({
    required PaymentSelection selection,
    required int weekNumber,
    required double newAmount,
  }) {
    final currentWeek = selection.weekSelections.firstWhere(
      (week) => week.weekNumber == weekNumber,
    );

    // Calcular el monto disponible (excluyendo la semana actual)
    final otherWeeksTotal = selection.weekSelections
        .where((week) => week.weekNumber != weekNumber && week.isSelected)
        .fold(0.0, (sum, week) => sum + week.amount);

    final otherCostsTotal = selection.costSelections
        .where((cost) => cost.isSelected)
        .fold(0.0, (sum, cost) => sum + cost.amount);

    final availableAmount = selection.totalAmount - otherWeeksTotal - otherCostsTotal;

    // Validar que el nuevo monto no exceda los límites
    final maxAllowed = [currentWeek.pendingAmount, availableAmount].reduce((a, b) => a < b ? a : b);
    final finalAmount = newAmount.clamp(0, maxAllowed);

    final updatedWeeks = selection.weekSelections.map((week) {
      if (week.weekNumber == weekNumber) {
        return week.copyWith(
          isSelected: finalAmount > 0,
          amount: finalAmount,
        );
      }
      return week;
    }).toList();

    return selection.copyWith(weekSelections: updatedWeeks);
  }

  /// Actualiza una selección de costo adicional con distribución inteligente
  static PaymentSelection updateCostSelection({
    required PaymentSelection selection,
    required String costoId,
    required bool isSelected,
    double? customAmount,
  }) {
    if (!isSelected) {
      // Si se deselecciona, simplemente poner amount en 0
      final updatedCosts = selection.costSelections.map((cost) {
        if (cost.costoAdicional.id == costoId) {
          return cost.copyWith(isSelected: false, amount: 0);
        }
        return cost;
      }).toList();
      return selection.copyWith(costSelections: updatedCosts);
    }

    // Si se selecciona, calcular cuánto se puede asignar
    final currentCost = selection.costSelections.firstWhere(
      (cost) => cost.costoAdicional.id == costoId,
    );

    // Calcular el monto disponible (excluyendo el costo actual)
    final otherCostsTotal = selection.costSelections
        .where((cost) => cost.costoAdicional.id != costoId && cost.isSelected)
        .fold(0.0, (sum, cost) => sum + cost.amount);

    final otherWeeksTotal = selection.weekSelections
        .where((week) => week.isSelected)
        .fold(0.0, (sum, week) => sum + week.amount);

    final availableAmount = selection.totalAmount - otherCostsTotal - otherWeeksTotal;

    // Determinar el monto a asignar
    final targetAmount = customAmount ??
        (availableAmount >= currentCost.pendingAmount
            ? currentCost.pendingAmount
            : availableAmount);

    final finalAmount = targetAmount.clamp(0,
        [currentCost.pendingAmount, availableAmount].reduce((a, b) => a < b ? a : b));

    final updatedCosts = selection.costSelections.map((cost) {
      if (cost.costoAdicional.id == costoId) {
        return cost.copyWith(
          isSelected: finalAmount > 0,
          amount: finalAmount,
        );
      }
      return cost;
    }).toList();

    return selection.copyWith(costSelections: updatedCosts);
  }

  /// Actualiza el monto de un costo específico
  static PaymentSelection updateCostAmount({
    required PaymentSelection selection,
    required String costoId,
    required double newAmount,
  }) {
    final currentCost = selection.costSelections.firstWhere(
      (cost) => cost.costoAdicional.id == costoId,
    );

    // Calcular el monto disponible (excluyendo el costo actual)
    final otherCostsTotal = selection.costSelections
        .where((cost) => cost.costoAdicional.id != costoId && cost.isSelected)
        .fold(0.0, (sum, cost) => sum + cost.amount);

    final otherWeeksTotal = selection.weekSelections
        .where((week) => week.isSelected)
        .fold(0.0, (sum, week) => sum + week.amount);

    final availableAmount = selection.totalAmount - otherCostsTotal - otherWeeksTotal;

    // Validar que el nuevo monto no exceda los límites
    final maxAllowed = [currentCost.pendingAmount, availableAmount].reduce((a, b) => a < b ? a : b);
    final finalAmount = newAmount.clamp(0, maxAllowed);

    final updatedCosts = selection.costSelections.map((cost) {
      if (cost.costoAdicional.id == costoId) {
        return cost.copyWith(
          isSelected: finalAmount > 0,
          amount: finalAmount,
        );
      }
      return cost;
    }).toList();

    return selection.copyWith(costSelections: updatedCosts);
  }

  /// Aplica distribución automática a las selecciones pendientes
  static PaymentSelection applyAutomaticDistribution({
    required PaymentSelection selection,
  }) {
    if (selection.remainingAmount <= 0) return selection;

    double remainingAmount = selection.remainingAmount;
    final updatedWeeks = <WeekSelection>[];
    final updatedCosts = <CostSelection>[];

    // Primero, distribuir a semanas pendientes
    for (final week in selection.weekSelections) {
      if (week.isPending && !week.isSelected && remainingAmount > 0) {
        final amountToAssign = remainingAmount >= week.pendingAmount 
            ? week.pendingAmount 
            : remainingAmount;
        
        updatedWeeks.add(week.copyWith(
          isSelected: true,
          amount: amountToAssign,
        ));
        remainingAmount -= amountToAssign;
      } else {
        updatedWeeks.add(week);
      }
    }

    // Luego, distribuir a costos adicionales no seleccionados
    for (final cost in selection.costSelections) {
      if (!cost.isSelected && remainingAmount > 0) {
        final amountToAssign = remainingAmount >= cost.pendingAmount 
            ? cost.pendingAmount 
            : remainingAmount;
        
        updatedCosts.add(cost.copyWith(
          isSelected: true,
          amount: amountToAssign,
        ));
        remainingAmount -= amountToAssign;
      } else {
        updatedCosts.add(cost);
      }
    }

    return selection.copyWith(
      weekSelections: updatedWeeks,
      costSelections: updatedCosts,
    );
  }

  /// Limpia todas las selecciones
  static PaymentSelection clearAllSelections({
    required PaymentSelection selection,
  }) {
    final clearedWeeks = selection.weekSelections
        .map((week) => week.copyWith(isSelected: false, amount: 0))
        .toList();
    
    final clearedCosts = selection.costSelections
        .map((cost) => cost.copyWith(isSelected: false, amount: 0))
        .toList();

    return selection.copyWith(
      weekSelections: clearedWeeks,
      costSelections: clearedCosts,
    );
  }

  /// Valida que la selección sea válida
  static List<String> validateSelection(PaymentSelection selection) {
    final errors = <String>[];

    if (selection.totalAmount <= 0) {
      errors.add('El monto total debe ser mayor a cero');
    }

    if (selection.totalSelected > selection.totalAmount + 0.01) {
      errors.add('La selección excede el monto total disponible');
    }

    if (selection.totalSelected <= 0) {
      errors.add('Debe seleccionar al menos un item para pagar');
    }

    return errors;
  }
}
