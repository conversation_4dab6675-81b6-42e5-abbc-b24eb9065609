import '../../../../../domain/modelos/kardex.dart';
import '../../../../../domain/modelos/costo_adicional.dart';
import '../../../../../domain/modelos/detalle_pago_unificado.dart';

class PaymentDistribution {
  final double totalAmount;
  final List<CostDistribution> costDistributions;
  final List<WeekDistribution> weekDistributions;
  final double remainingAmount;
  final bool isComplete;

  PaymentDistribution({
    required this.totalAmount,
    required this.costDistributions,
    required this.weekDistributions,
    required this.remainingAmount,
    required this.isComplete,
  });

  double get totalDistributed => 
      costDistributions.fold(0.0, (sum, cost) => sum + cost.amount) +
      weekDistributions.fold(0.0, (sum, week) => sum + week.amount);

  List<DetallePagoUnificado> toDetallesPago(String alumnoId) {
    final detalles = <DetallePagoUnificado>[];
    final timestamp = DateTime.now().millisecondsSinceEpoch;

    // Agregar costos adicionales
    for (int i = 0; i < costDistributions.length; i++) {
      final cost = costDistributions[i];
      if (cost.amount > 0) {
        detalles.add(DetallePagoUnificado(
          id: 'DET-COST-$timestamp-$i',
          tipo: 'costo_adicional',
          concepto: cost.costoAdicional.nombre,
          referenciaId: cost.costoAdicional.id,
          monto: cost.amount,
          fecha: DateTime.now(),
          alumnoId: alumnoId,
        ));
      }
    }

    // Agregar semanas
    for (int i = 0; i < weekDistributions.length; i++) {
      final week = weekDistributions[i];
      if (week.amount > 0) {
        detalles.add(DetallePagoUnificado(
          id: 'DET-WEEK-$timestamp-$i',
          tipo: 'semana',
          concepto: week.isExisting 
              ? 'Semana ${week.numeroSemana} (Pendiente)'
              : 'Semana ${week.numeroSemana}',
          numeroSemana: week.numeroSemana,
          monto: week.amount,
          fecha: DateTime.now(),
          alumnoId: alumnoId,
        ));
      }
    }

    return detalles;
  }
}

class CostDistribution {
  final CostoAdicional costoAdicional;
  final double amount;
  final double pendingBefore;
  final double pendingAfter;
  final bool isFullyPaid;

  CostDistribution({
    required this.costoAdicional,
    required this.amount,
    required this.pendingBefore,
    required this.pendingAfter,
    required this.isFullyPaid,
  });
}

class WeekDistribution {
  final int numeroSemana;
  final double amount;
  final double weeklyAmount;
  final bool isExisting;
  final bool isFullyPaid;
  final double pendingBefore;
  final double pendingAfter;

  WeekDistribution({
    required this.numeroSemana,
    required this.amount,
    required this.weeklyAmount,
    required this.isExisting,
    required this.isFullyPaid,
    required this.pendingBefore,
    required this.pendingAfter,
  });
}

class PaymentDistributionEngine {
  static PaymentDistribution calculateDistribution({
    required double amount,
    required List<CostoAdicional> costosAdicionales,
    required List<KardexSemana> semanas,
    required double montoPorSemana,
  }) {
    if (amount <= 0) {
      return PaymentDistribution(
        totalAmount: amount,
        costDistributions: [],
        weekDistributions: [],
        remainingAmount: amount,
        isComplete: false,
      );
    }

    double remainingAmount = amount;
    final costDistributions = <CostDistribution>[];
    final weekDistributions = <WeekDistribution>[];

    // 1. Distribuir a costos adicionales pendientes
    final costosPendientes = costosAdicionales
        .where((costo) => !costo.pagado)
        .toList()
      ..sort((a, b) => a.fechaVencimiento?.compareTo(b.fechaVencimiento ?? DateTime.now()) ?? 0);

    for (final costo in costosPendientes) {
      if (remainingAmount <= 0) break;

      final pendiente = costo.monto - costo.montoPagado;
      if (pendiente > 0) {
        final montoAsignado = remainingAmount >= pendiente ? pendiente : remainingAmount;
        
        costDistributions.add(CostDistribution(
          costoAdicional: costo,
          amount: montoAsignado,
          pendingBefore: pendiente,
          pendingAfter: pendiente - montoAsignado,
          isFullyPaid: montoAsignado >= pendiente,
        ));

        remainingAmount -= montoAsignado;
      }
    }

    // 2. Distribuir a semanas pendientes (ordenadas por número)
    final semanasPendientes = semanas
        .where((semana) => !semana.pagada)
        .toList()
      ..sort((a, b) => a.numeroSemana.compareTo(b.numeroSemana));

    for (final semana in semanasPendientes) {
      if (remainingAmount <= 0) break;

      final pendiente = semana.monto - semana.montoPagado;
      if (pendiente > 0) {
        final montoAsignado = remainingAmount >= pendiente ? pendiente : remainingAmount;
        
        weekDistributions.add(WeekDistribution(
          numeroSemana: semana.numeroSemana,
          amount: montoAsignado,
          weeklyAmount: semana.monto,
          isExisting: true,
          isFullyPaid: montoAsignado >= pendiente,
          pendingBefore: pendiente,
          pendingAfter: pendiente - montoAsignado,
        ));

        remainingAmount -= montoAsignado;
      }
    }

    // 3. Distribuir a nuevas semanas si queda dinero
    if (remainingAmount > 0 && montoPorSemana > 0) {
      final ultimaSemanaExistente = semanas.isNotEmpty 
          ? semanas.map((s) => s.numeroSemana).reduce((a, b) => a > b ? a : b)
          : 0;

      int siguienteSemana = ultimaSemanaExistente + 1;
      
      while (remainingAmount > 0) {
        // Verificar que no exista ya esta semana
        final semanaExiste = semanas.any((s) => s.numeroSemana == siguienteSemana);
        if (semanaExiste) {
          siguienteSemana++;
          continue;
        }

        final montoAsignado = remainingAmount >= montoPorSemana ? montoPorSemana : remainingAmount;
        
        weekDistributions.add(WeekDistribution(
          numeroSemana: siguienteSemana,
          amount: montoAsignado,
          weeklyAmount: montoPorSemana,
          isExisting: false,
          isFullyPaid: montoAsignado >= montoPorSemana,
          pendingBefore: montoPorSemana,
          pendingAfter: montoPorSemana - montoAsignado,
        ));

        remainingAmount -= montoAsignado;
        siguienteSemana++;

        // Evitar bucle infinito - máximo 52 semanas adicionales
        if (siguienteSemana > ultimaSemanaExistente + 52) {
          break;
        }
      }
    }

    return PaymentDistribution(
      totalAmount: amount,
      costDistributions: costDistributions,
      weekDistributions: weekDistributions,
      remainingAmount: remainingAmount,
      isComplete: remainingAmount <= 0.01, // Considerar completo si queda menos de 1 centavo
    );
  }

  static String getDistributionSummary(PaymentDistribution distribution) {
    final buffer = StringBuffer();
    
    if (distribution.costDistributions.isNotEmpty) {
      buffer.writeln('Costos Adicionales:');
      for (final cost in distribution.costDistributions) {
        buffer.writeln('• ${cost.costoAdicional.nombre}: \$${cost.amount.toStringAsFixed(2)}');
      }
    }

    if (distribution.weekDistributions.isNotEmpty) {
      buffer.writeln('${distribution.costDistributions.isNotEmpty ? '\n' : ''}Semanas:');
      for (final week in distribution.weekDistributions) {
        final status = week.isFullyPaid ? 'Pagada' : 'Parcial';
        final type = week.isExisting ? 'Pendiente' : 'Nueva';
        buffer.writeln('• Semana ${week.numeroSemana} ($type): \$${week.amount.toStringAsFixed(2)} - $status');
      }
    }

    if (distribution.remainingAmount > 0.01) {
      buffer.writeln('\nSobrante: \$${distribution.remainingAmount.toStringAsFixed(2)}');
    }

    return buffer.toString().trim();
  }

  static double calculateTotalPending({
    required List<CostoAdicional> costosAdicionales,
    required List<KardexSemana> semanas,
  }) {
    double total = 0;

    // Sumar costos adicionales pendientes
    for (final costo in costosAdicionales) {
      if (!costo.pagado) {
        total += costo.monto - costo.montoPagado;
      }
    }

    // Sumar semanas pendientes
    for (final semana in semanas) {
      if (!semana.pagada) {
        total += semana.monto - semana.montoPagado;
      }
    }

    return total;
  }

  static List<String> validateDistribution(PaymentDistribution distribution) {
    final errors = <String>[];

    if (distribution.totalAmount <= 0) {
      errors.add('El monto debe ser mayor a cero');
    }

    if (distribution.totalDistributed > distribution.totalAmount + 0.01) {
      errors.add('La distribución excede el monto total');
    }

    for (final cost in distribution.costDistributions) {
      if (cost.amount > cost.pendingBefore + 0.01) {
        errors.add('El monto asignado a ${cost.costoAdicional.nombre} excede lo pendiente');
      }
    }

    return errors;
  }
}
