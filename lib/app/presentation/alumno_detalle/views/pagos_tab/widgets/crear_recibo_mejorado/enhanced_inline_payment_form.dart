import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:google_fonts/google_fonts.dart';
import '../../../../../../domain/entities/alumno.dart';
import '../../../../../../domain/modelos/kardex.dart';
import '../../../../../../domain/modelos/costo_adicional.dart';
import '../../../../../../styles/app_styles.dart';
import 'payment_distribution_engine.dart';
import 'enhanced_payment_engine.dart';
import 'payment_selection_models.dart';

class EnhancedInlinePaymentForm extends StatefulWidget {
  final Alumno alumno;
  final List<CostoAdicional> costosAdicionales;
  final List<KardexSemana> semanas;
  final double montoPorSemana;
  final Function(Map<String, dynamic>) onGenerarRecibo;

  const EnhancedInlinePaymentForm({
    Key? key,
    required this.alumno,
    required this.costosAdicionales,
    required this.semanas,
    required this.montoPorSemana,
    required this.onGenerarRecibo,
  }) : super(key: key);

  @override
  State<EnhancedInlinePaymentForm> createState() => _EnhancedInlinePaymentFormState();
}

class _EnhancedInlinePaymentFormState extends State<EnhancedInlinePaymentForm>
    with TickerProviderStateMixin {
  final _formKey = GlobalKey<FormState>();
  final _montoController = TextEditingController();
  final _notasController = TextEditingController();

  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;

  PaymentDistribution? _currentDistribution;
  PaymentSelection? _currentSelection;
  bool _showDistribution = false;
  String _metodoPago = 'Efectivo';
  PaymentMode _paymentMode = PaymentMode.automatic;

  // Control de costos adicionales para modo automático
  final Map<String, bool> _costosSeleccionados = {};

  final List<String> _metodosPago = [
    'Efectivo',
    'Transferencia',
    'Tarjeta de Débito',
    'Tarjeta de Crédito',
    'Cheque'
  ];

  @override
  void initState() {
    super.initState();
    _setupAnimations();
    _montoController.addListener(_onAmountChanged);
    _initializeCostosSelection();
  }

  void _setupAnimations() {
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 400),
      vsync: this,
    );

    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));
  }

  void _initializeCostosSelection() {
    // Inicializar todos los costos como no seleccionados
    for (final costo in widget.costosAdicionales) {
      _costosSeleccionados[costo.id] = false;
    }
  }

  @override
  void dispose() {
    _animationController.dispose();
    _montoController.dispose();
    _notasController.dispose();
    super.dispose();
  }

  void _onAmountChanged() {
    final amount = double.tryParse(_montoController.text) ?? 0;
    if (amount > 0) {
      _calculateDistribution();
    } else {
      setState(() {
        _currentDistribution = null;
        _currentSelection = null;
        _showDistribution = false;
      });
      _animationController.reverse();
    }
  }

  void _calculateDistribution() {
    final amount = double.tryParse(_montoController.text) ?? 0;
    if (amount <= 0) return;

    if (_paymentMode == PaymentMode.automatic) {
      _calculateAutomaticDistribution(amount);
    } else {
      _calculateManualDistribution(amount);
    }
  }

  void _calculateAutomaticDistribution(double amount) {
    // Filtrar solo los costos seleccionados
    final costosSeleccionados = widget.costosAdicionales
        .where((costo) => _costosSeleccionados[costo.id] == true)
        .toList();

    final distribution = EnhancedPaymentEngine.calculateAutomaticDistribution(
      amount: amount,
      selectedCosts: costosSeleccionados,
      semanas: widget.semanas,
      montoPorSemana: widget.montoPorSemana,
    );

    setState(() {
      _currentDistribution = distribution;
      _currentSelection = null;
      _showDistribution = true;
    });

    if (!_animationController.isAnimating) {
      _animationController.forward();
    }
  }

  void _calculateManualDistribution(double amount) {
    if (_currentSelection == null) {
      _currentSelection = EnhancedPaymentEngine.initializePaymentSelection(
        totalAmount: amount,
        costosAdicionales: widget.costosAdicionales,
        semanas: widget.semanas,
        montoPorSemana: widget.montoPorSemana,
      );
    } else {
      _currentSelection = _currentSelection!.copyWith(totalAmount: amount);
    }

    final distribution = EnhancedPaymentEngine.calculateManualDistribution(
      selection: _currentSelection!,
      montoPorSemana: widget.montoPorSemana,
    );

    setState(() {
      _currentDistribution = distribution;
      _showDistribution = true;
    });

    if (!_animationController.isAnimating) {
      _animationController.forward();
    }
  }

  void _onPaymentModeChanged(PaymentMode mode) {
    setState(() {
      _paymentMode = mode;
      _currentSelection = null;
      _currentDistribution = null;
    });
    _calculateDistribution();
  }

  void _onWeekSelectionChanged(int weekNumber, bool isSelected) {
    if (_currentSelection == null) return;

    final updatedSelection = EnhancedPaymentEngine.updateWeekSelection(
      selection: _currentSelection!,
      weekNumber: weekNumber,
      isSelected: isSelected,
    );

    setState(() {
      _currentSelection = updatedSelection;
    });

    _calculateManualDistribution(_currentSelection!.totalAmount);
  }

  void _onCostSelectionChanged(String costoId, bool isSelected) {
    if (_paymentMode == PaymentMode.automatic) {
      setState(() {
        _costosSeleccionados[costoId] = isSelected;
      });
      _calculateDistribution();
    } else {
      if (_currentSelection == null) return;

      final updatedSelection = EnhancedPaymentEngine.updateCostSelection(
        selection: _currentSelection!,
        costoId: costoId,
        isSelected: isSelected,
      );

      setState(() {
        _currentSelection = updatedSelection;
      });

      _calculateManualDistribution(_currentSelection!.totalAmount);
    }
  }

  void _applyAutomaticToRemaining() {
    if (_currentSelection == null) return;

    final updatedSelection = EnhancedPaymentEngine.applyAutomaticDistribution(
      selection: _currentSelection!,
    );

    setState(() {
      _currentSelection = updatedSelection;
    });

    _calculateManualDistribution(_currentSelection!.totalAmount);
  }

  void _clearAllSelections() {
    if (_paymentMode == PaymentMode.automatic) {
      setState(() {
        for (final key in _costosSeleccionados.keys) {
          _costosSeleccionados[key] = false;
        }
      });
      _calculateDistribution();
    } else {
      if (_currentSelection == null) return;

      final updatedSelection = EnhancedPaymentEngine.clearAllSelections(
        selection: _currentSelection!,
      );

      setState(() {
        _currentSelection = updatedSelection;
      });

      _calculateManualDistribution(_currentSelection!.totalAmount);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Form(
      key: _formKey,
      child: SingleChildScrollView(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildAmountSection(),
            const SizedBox(height: 24),
            _buildPaymentModeSection(),
            const SizedBox(height: 24),
            _buildPaymentMethodSection(),
            const SizedBox(height: 24),
            if (_paymentMode == PaymentMode.automatic) ...[
              _buildCostosAdicionalesSection(),
              const SizedBox(height: 24),
            ],
            if (_paymentMode == PaymentMode.manual) ...[
              _buildManualSelectionSection(),
              const SizedBox(height: 24),
            ],
            if (_showDistribution && _currentDistribution != null) ...[
              _buildDistributionSection(),
              const SizedBox(height: 24),
            ],
            _buildNotesSection(),
            const SizedBox(height: 32),
            _buildGenerateButton(),
          ],
        ),
      ),
    );
  }
}
