import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:google_fonts/google_fonts.dart';
import '../../../../../../domain/entities/alumno.dart';
import '../../../../../../domain/modelos/kardex.dart';
import '../../../../../../domain/modelos/costo_adicional.dart';
import '../../../../../../styles/app_styles.dart';
import 'payment_distribution_engine.dart';
import 'enhanced_payment_engine.dart';
import 'payment_selection_models.dart';

class EnhancedInlinePaymentForm extends StatefulWidget {
  final Alumno alumno;
  final List<CostoAdicional> costosAdicionales;
  final List<KardexSemana> semanas;
  final double montoPorSemana;
  final Function(Map<String, dynamic>) onGenerarRecibo;

  const EnhancedInlinePaymentForm({
    Key? key,
    required this.alumno,
    required this.costosAdicionales,
    required this.semanas,
    required this.montoPorSemana,
    required this.onGenerarRecibo,
  }) : super(key: key);

  @override
  State<EnhancedInlinePaymentForm> createState() => _EnhancedInlinePaymentFormState();
}

class _EnhancedInlinePaymentFormState extends State<EnhancedInlinePaymentForm>
    with TickerProviderStateMixin {
  final _formKey = GlobalKey<FormState>();
  final _montoController = TextEditingController();
  final _notasController = TextEditingController();

  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;

  PaymentDistribution? _currentDistribution;
  PaymentSelection? _currentSelection;
  bool _showDistribution = false;
  String _metodoPago = 'Efectivo';

  final List<String> _metodosPago = [
    'Efectivo',
    'Transferencia',
    'Tarjeta de Débito',
    'Tarjeta de Crédito',
    'Cheque'
  ];

  @override
  void initState() {
    super.initState();
    _setupAnimations();
    _montoController.addListener(_onAmountChanged);
  }

  void _setupAnimations() {
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 400),
      vsync: this,
    );

    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));
  }

  @override
  void dispose() {
    _animationController.dispose();
    _montoController.dispose();
    _notasController.dispose();
    super.dispose();
  }

  void _onAmountChanged() {
    final amount = double.tryParse(_montoController.text) ?? 0;
    if (amount > 0) {
      _calculateDistribution();
    } else {
      setState(() {
        _currentDistribution = null;
        _currentSelection = null;
        _showDistribution = false;
      });
      _animationController.reverse();
    }
  }

  void _calculateDistribution() {
    final amount = double.tryParse(_montoController.text) ?? 0;
    if (amount <= 0) return;

    if (_currentSelection == null) {
      _currentSelection = EnhancedPaymentEngine.initializePaymentSelection(
        totalAmount: amount,
        costosAdicionales: widget.costosAdicionales,
        semanas: widget.semanas,
        montoPorSemana: widget.montoPorSemana,
      );

      // Aplicar distribución automática inmediatamente para mostrar el flujo
      _currentSelection = EnhancedPaymentEngine.simpleDistribution(
        selection: _currentSelection!,
      );
    } else {
      _currentSelection = _currentSelection!.copyWith(totalAmount: amount);
      // Re-aplicar distribución automática cuando cambia el monto
      _currentSelection = EnhancedPaymentEngine.simpleDistribution(
        selection: _currentSelection!,
      );
    }

    final distribution = EnhancedPaymentEngine.calculateManualDistribution(
      selection: _currentSelection!,
      montoPorSemana: widget.montoPorSemana,
    );

    setState(() {
      _currentDistribution = distribution;
      _showDistribution = true;
    });

    if (!_animationController.isAnimating) {
      _animationController.forward();
    }
  }

  void _onWeekSelectionChanged(int weekNumber, bool isSelected) {
    if (_currentSelection == null) return;

    final updatedSelection = EnhancedPaymentEngine.updateWeekSelection(
      selection: _currentSelection!,
      weekNumber: weekNumber,
      isSelected: isSelected,
    );

    setState(() {
      _currentSelection = updatedSelection;
    });

    _recalculateDistribution();
  }

  void _onCostSelectionChanged(String costoId, bool isSelected) {
    if (_currentSelection == null) return;

    final updatedSelection = EnhancedPaymentEngine.updateCostSelection(
      selection: _currentSelection!,
      costoId: costoId,
      isSelected: isSelected,
    );

    setState(() {
      _currentSelection = updatedSelection;
    });

    _recalculateDistribution();
  }

  void _recalculateDistribution() {
    if (_currentSelection == null) return;

    final distribution = EnhancedPaymentEngine.calculateManualDistribution(
      selection: _currentSelection!,
      montoPorSemana: widget.montoPorSemana,
    );

    setState(() {
      _currentDistribution = distribution;
    });
  }

  void _onCostAmountChanged(String costoId, double newAmount) {
    if (_currentSelection == null) return;

    final updatedSelection = EnhancedPaymentEngine.updateCostAmount(
      selection: _currentSelection!,
      costoId: costoId,
      newAmount: newAmount,
    );

    setState(() {
      _currentSelection = updatedSelection;
    });

    _calculateManualDistribution(_currentSelection!.totalAmount);
  }

  void _onWeekAmountChanged(int weekNumber, double newAmount) {
    if (_currentSelection == null) return;

    final updatedSelection = EnhancedPaymentEngine.updateWeekAmount(
      selection: _currentSelection!,
      weekNumber: weekNumber,
      newAmount: newAmount,
    );

    setState(() {
      _currentSelection = updatedSelection;
    });

    _calculateManualDistribution(_currentSelection!.totalAmount);
  }

  void _applySimpleDistribution() {
    if (_currentSelection == null) return;

    final updatedSelection = EnhancedPaymentEngine.simpleDistribution(
      selection: _currentSelection!,
    );

    setState(() {
      _currentSelection = updatedSelection;
    });

    _calculateManualDistribution(_currentSelection!.totalAmount);
  }

  void _applyAutomaticToRemaining() {
    if (_currentSelection == null) return;

    final updatedSelection = EnhancedPaymentEngine.applyAutomaticDistribution(
      selection: _currentSelection!,
    );

    setState(() {
      _currentSelection = updatedSelection;
    });

    _calculateManualDistribution(_currentSelection!.totalAmount);
  }

  void _clearAllSelections() {
    if (_currentSelection == null) return;

    final updatedSelection = EnhancedPaymentEngine.clearAllSelections(
      selection: _currentSelection!,
    );

    setState(() {
      _currentSelection = updatedSelection;
    });

    _recalculateDistribution();
  }

  void _applyAutoDistribution() {
    if (_currentSelection == null) return;

    final updatedSelection = EnhancedPaymentEngine.simpleDistribution(
      selection: _currentSelection!,
    );

    setState(() {
      _currentSelection = updatedSelection;
    });

    _recalculateDistribution();
  }

  @override
  Widget build(BuildContext context) {
    return Form(
      key: _formKey,
      child: SingleChildScrollView(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildAmountSection(),
            const SizedBox(height: 24),
            _buildPaymentMethodSection(),
            const SizedBox(height: 24),
            _buildSelectionSection(),
            const SizedBox(height: 24),
            if (_showDistribution && _currentDistribution != null) ...[
              _buildDistributionSection(),
              const SizedBox(height: 24),
            ],
            _buildNotesSection(),
            const SizedBox(height: 32),
            _buildGenerateButton(),
          ],
        ),
      ),
    );
  }

  Widget _buildAmountSection() {
    final totalPendiente = _calculateTotalPendiente();

    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.payments,
                color: AppStyles.primaryColor,
                size: 24,
              ),
              const SizedBox(width: 8),
              Text(
                'Monto a Pagar',
                style: GoogleFonts.poppins(
                  fontSize: 18,
                  fontWeight: FontWeight.w600,
                  color: AppStyles.textColor,
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),

          // Input principal
          TextFormField(
            controller: _montoController,
            keyboardType: const TextInputType.numberWithOptions(decimal: true),
            inputFormatters: [
              FilteringTextInputFormatter.allow(RegExp(r'^\d*\.?\d{0,2}')),
            ],
            style: GoogleFonts.poppins(
              fontSize: 24,
              fontWeight: FontWeight.w600,
              color: AppStyles.textColor,
            ),
            decoration: InputDecoration(
              prefixText: '\$ ',
              prefixStyle: GoogleFonts.poppins(
                fontSize: 24,
                fontWeight: FontWeight.w600,
                color: AppStyles.primaryColor,
              ),
              hintText: '0.00',
              hintStyle: GoogleFonts.poppins(
                fontSize: 24,
                fontWeight: FontWeight.w400,
                color: Colors.grey[400],
              ),
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
                borderSide: BorderSide(color: Colors.grey[300]!),
              ),
              focusedBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
                borderSide: BorderSide(color: AppStyles.primaryColor, width: 2),
              ),
              contentPadding: const EdgeInsets.symmetric(
                horizontal: 16,
                vertical: 16,
              ),
            ),
            validator: (value) {
              if (value == null || value.isEmpty) {
                return 'Ingrese un monto válido';
              }
              final amount = double.tryParse(value);
              if (amount == null || amount <= 0) {
                return 'El monto debe ser mayor a cero';
              }
              return null;
            },
          ),

          const SizedBox(height: 16),

          // Información contextual
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: AppStyles.primaryColor.withOpacity(0.05),
              borderRadius: BorderRadius.circular(8),
              border: Border.all(
                color: AppStyles.primaryColor.withOpacity(0.2),
              ),
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'Total Pendiente:',
                  style: GoogleFonts.poppins(
                    fontSize: 14,
                    color: AppStyles.secondaryTextColor,
                  ),
                ),
                Text(
                  '\$${totalPendiente.toStringAsFixed(2)}',
                  style: GoogleFonts.poppins(
                    fontSize: 14,
                    fontWeight: FontWeight.w600,
                    color: AppStyles.primaryColor,
                  ),
                ),
              ],
            ),
          ),

          const SizedBox(height: 12),

          // Sugerencias rápidas
          _buildQuickSuggestions(),
        ],
      ),
    );
  }

  Widget _buildQuickSuggestions() {
    final suggestions = <String>[];

    // Próxima semana
    if (widget.montoPorSemana > 0) {
      suggestions.add(widget.montoPorSemana.toStringAsFixed(2));
    }

    // Dos semanas
    if (widget.montoPorSemana > 0) {
      suggestions.add((widget.montoPorSemana * 2).toStringAsFixed(2));
    }

    // Montos redondos
    final montosComunes = [100.0, 200.0, 500.0, 1000.0];
    for (final monto in montosComunes) {
      if (!suggestions.contains(monto.toStringAsFixed(2))) {
        suggestions.add(monto.toStringAsFixed(2));
      }
    }

    if (suggestions.isEmpty) return const SizedBox.shrink();

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Sugerencias:',
          style: GoogleFonts.poppins(
            fontSize: 12,
            fontWeight: FontWeight.w500,
            color: AppStyles.secondaryTextColor,
          ),
        ),
        const SizedBox(height: 8),
        Wrap(
          spacing: 8,
          runSpacing: 8,
          children: suggestions.take(4).map((suggestion) {
            final amount = double.parse(suggestion);
            String label = '\$${amount.toStringAsFixed(2)}';

            if (amount == widget.montoPorSemana) {
              label += ' (1 sem)';
            } else if (amount == widget.montoPorSemana * 2) {
              label += ' (2 sem)';
            }

            return GestureDetector(
              onTap: () {
                _montoController.text = suggestion;
                _montoController.selection = TextSelection.fromPosition(
                  TextPosition(offset: _montoController.text.length),
                );
              },
              child: Container(
                padding: const EdgeInsets.symmetric(
                  horizontal: 12,
                  vertical: 6,
                ),
                decoration: BoxDecoration(
                  color: AppStyles.primaryColor.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(16),
                  border: Border.all(
                    color: AppStyles.primaryColor.withOpacity(0.3),
                  ),
                ),
                child: Text(
                  label,
                  style: GoogleFonts.poppins(
                    fontSize: 11,
                    fontWeight: FontWeight.w500,
                    color: AppStyles.primaryColor,
                  ),
                ),
              ),
            );
          }).toList(),
        ),
      ],
    );
  }

  double _calculateTotalPendiente() {
    double total = 0;

    // Sumar semanas pendientes
    for (final semana in widget.semanas) {
      if (!semana.pagada) {
        total += semana.monto - semana.montoPagado;
      }
    }

    // Sumar costos adicionales pendientes
    for (final costo in widget.costosAdicionales) {
      if (!costo.pagado) {
        total += costo.monto - costo.montoPagado;
      }
    }

    return total;
  }

  Widget _buildSelectionSection() {
    if (_currentSelection == null) return const SizedBox.shrink();

    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header con información del flujo
          Row(
            children: [
              Icon(
                Icons.assignment_turned_in,
                color: AppStyles.primaryColor,
                size: 24,
              ),
              const SizedBox(width: 8),
              Expanded(
                child: Text(
                  'Distribución del Pago',
                  style: GoogleFonts.poppins(
                    fontSize: 18,
                    fontWeight: FontWeight.w600,
                    color: AppStyles.textColor,
                  ),
                ),
              ),
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                decoration: BoxDecoration(
                  color: _currentSelection!.remainingAmount > 0
                      ? Colors.orange[50]
                      : Colors.green[50],
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(
                    color: _currentSelection!.remainingAmount > 0
                        ? Colors.orange[300]!
                        : Colors.green[300]!,
                  ),
                ),
                child: Text(
                  _currentSelection!.remainingAmount > 0
                      ? 'Restante: \$${_currentSelection!.remainingAmount.toStringAsFixed(2)}'
                      : '✓ Completo',
                  style: GoogleFonts.poppins(
                    fontSize: 12,
                    fontWeight: FontWeight.w600,
                    color: _currentSelection!.remainingAmount > 0
                        ? Colors.orange[700]
                        : Colors.green[700],
                  ),
                ),
              ),
            ],
          ),

          const SizedBox(height: 16),

          // Costos adicionales (si los hay)
          if (widget.costosAdicionales.isNotEmpty) ...[
            _buildCostosSection(),
            const SizedBox(height: 20),
          ],

          // Semanas
          _buildSemanasSection(),

          const SizedBox(height: 16),

          // Botón simple de auto-distribución
          SizedBox(
            width: double.infinity,
            child: ElevatedButton.icon(
              onPressed: _applyAutoDistribution,
              icon: const Icon(Icons.auto_awesome, size: 18),
              label: const Text('Distribuir Automáticamente'),
              style: ElevatedButton.styleFrom(
                backgroundColor: AppStyles.primaryColor,
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(vertical: 12),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildCostosSection() {
    final availableCosts = _currentSelection!.costSelections
        .where((cost) => cost.pendingAmount > 0)
        .toList();

    if (availableCosts.isEmpty) return const SizedBox.shrink();

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Costos Adicionales',
          style: GoogleFonts.poppins(
            fontSize: 16,
            fontWeight: FontWeight.w600,
            color: AppStyles.textColor,
          ),
        ),
        const SizedBox(height: 8),
        Text(
          'Selecciona los costos que deseas pagar primero',
          style: GoogleFonts.poppins(
            fontSize: 12,
            color: AppStyles.secondaryTextColor,
          ),
        ),
        const SizedBox(height: 12),
        ...availableCosts.map((cost) => _buildSimpleCostToggle(cost)),
      ],
    );
  }

  Widget _buildSimpleCostToggle(CostSelection cost) {
    return Container(
      margin: const EdgeInsets.only(bottom: 6),
      child: CheckboxListTile(
        value: cost.isSelected,
        onChanged: (value) => _onCostSelectionChanged(cost.costoAdicional.id, value ?? false),
        title: Text(
          cost.costoAdicional.nombre,
          style: GoogleFonts.poppins(
            fontSize: 14,
            fontWeight: FontWeight.w500,
            color: AppStyles.textColor,
          ),
        ),
        subtitle: cost.isSelected
            ? Text(
                'Asignado: \$${cost.amount.toStringAsFixed(2)}',
                style: GoogleFonts.poppins(
                  fontSize: 12,
                  color: Colors.blue[700],
                  fontWeight: FontWeight.w600,
                ),
              )
            : Text(
                'Pendiente: \$${cost.pendingAmount.toStringAsFixed(2)}',
                style: GoogleFonts.poppins(
                  fontSize: 12,
                  color: AppStyles.secondaryTextColor,
                ),
              ),
        activeColor: AppStyles.primaryColor,
        contentPadding: const EdgeInsets.symmetric(horizontal: 8),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(8),
        ),
        tileColor: cost.isSelected ? Colors.blue[50] : null,
      ),
    );
  }

  Widget _buildSemanasSection() {
    final pendingWeeks = _currentSelection!.weekSelections
        .where((w) => w.isPending)
        .toList();
    final selectedWeeks = _currentSelection!.weekSelections
        .where((w) => w.isSelected && !w.isPending)
        .toList();

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Semanas del Curso',
          style: GoogleFonts.poppins(
            fontSize: 16,
            fontWeight: FontWeight.w600,
            color: AppStyles.textColor,
          ),
        ),
        const SizedBox(height: 8),
        Text(
          'Las semanas se pagan automáticamente: pendientes primero, luego nuevas',
          style: GoogleFonts.poppins(
            fontSize: 12,
            color: AppStyles.secondaryTextColor,
          ),
        ),
        const SizedBox(height: 12),

        // Semanas pendientes
        if (pendingWeeks.isNotEmpty) ...[
          Text(
            'Pendientes (${pendingWeeks.length})',
            style: GoogleFonts.poppins(
              fontSize: 14,
              fontWeight: FontWeight.w600,
              color: Colors.orange[700],
            ),
          ),
          const SizedBox(height: 8),
          Wrap(
            spacing: 8,
            runSpacing: 8,
            children: pendingWeeks.map((week) => _buildWeekChip(week)).toList(),
          ),
          const SizedBox(height: 16),
        ],

        // Semanas nuevas seleccionadas
        if (selectedWeeks.isNotEmpty) ...[
          Text(
            'Nuevas Seleccionadas (${selectedWeeks.length})',
            style: GoogleFonts.poppins(
              fontSize: 14,
              fontWeight: FontWeight.w600,
              color: Colors.green[700],
            ),
          ),
          const SizedBox(height: 8),
          Wrap(
            spacing: 8,
            runSpacing: 8,
            children: selectedWeeks.map((week) => _buildWeekChip(week)).toList(),
          ),
        ],
      ],
    );
  }

  Widget _buildWeekChip(WeekSelection week) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
      decoration: BoxDecoration(
        color: week.isSelected
            ? (week.isPending ? Colors.orange[100] : Colors.green[100])
            : Colors.grey[100],
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: week.isSelected
              ? (week.isPending ? Colors.orange[300]! : Colors.green[300]!)
              : Colors.grey[300]!,
        ),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            week.isPending ? Icons.schedule : Icons.add_circle,
            size: 14,
            color: week.isSelected
                ? (week.isPending ? Colors.orange[700] : Colors.green[700])
                : Colors.grey[600],
          ),
          const SizedBox(width: 4),
          Text(
            'S${week.weekNumber}',
            style: GoogleFonts.poppins(
              fontSize: 12,
              fontWeight: FontWeight.w600,
              color: week.isSelected
                  ? (week.isPending ? Colors.orange[700] : Colors.green[700])
                  : Colors.grey[600],
            ),
          ),
          if (week.isSelected) ...[
            const SizedBox(width: 4),
            Text(
              '\$${week.amount.toStringAsFixed(0)}',
              style: GoogleFonts.poppins(
                fontSize: 11,
                fontWeight: FontWeight.w500,
                color: week.isPending ? Colors.orange[600] : Colors.green[600],
              ),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildPaymentModeSection() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.tune,
                color: AppStyles.primaryColor,
                size: 24,
              ),
              const SizedBox(width: 8),
              Text(
                'Modo de Distribución',
                style: GoogleFonts.poppins(
                  fontSize: 18,
                  fontWeight: FontWeight.w600,
                  color: AppStyles.textColor,
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          Row(
            children: [
              Expanded(
                child: _buildModeOption(
                  mode: PaymentMode.automatic,
                  title: 'Automático',
                  subtitle: 'Distribución inteligente',
                  icon: Icons.auto_awesome,
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: _buildModeOption(
                  mode: PaymentMode.manual,
                  title: 'Manual',
                  subtitle: 'Selección personalizada',
                  icon: Icons.tune,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildModeOption({
    required PaymentMode mode,
    required String title,
    required String subtitle,
    required IconData icon,
  }) {
    final isSelected = _paymentMode == mode;

    return GestureDetector(
      onTap: () => _onPaymentModeChanged(mode),
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: isSelected
              ? AppStyles.primaryColor.withOpacity(0.1)
              : Colors.grey[50],
          borderRadius: BorderRadius.circular(12),
          border: Border.all(
            color: isSelected
                ? AppStyles.primaryColor
                : Colors.grey[300]!,
            width: isSelected ? 2 : 1,
          ),
        ),
        child: Column(
          children: [
            Icon(
              icon,
              color: isSelected
                  ? AppStyles.primaryColor
                  : Colors.grey[600],
              size: 32,
            ),
            const SizedBox(height: 8),
            Text(
              title,
              style: GoogleFonts.poppins(
                fontSize: 14,
                fontWeight: FontWeight.w600,
                color: isSelected
                    ? AppStyles.primaryColor
                    : AppStyles.textColor,
              ),
            ),
            const SizedBox(height: 4),
            Text(
              subtitle,
              style: GoogleFonts.poppins(
                fontSize: 12,
                color: AppStyles.secondaryTextColor,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPaymentMethodSection() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.payment,
                color: AppStyles.primaryColor,
                size: 24,
              ),
              const SizedBox(width: 8),
              Text(
                'Método de Pago',
                style: GoogleFonts.poppins(
                  fontSize: 18,
                  fontWeight: FontWeight.w600,
                  color: AppStyles.textColor,
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          DropdownButtonFormField<String>(
            value: _metodoPago,
            decoration: InputDecoration(
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
                borderSide: BorderSide(color: Colors.grey[300]!),
              ),
              focusedBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
                borderSide: BorderSide(color: AppStyles.primaryColor, width: 2),
              ),
              contentPadding: const EdgeInsets.symmetric(
                horizontal: 16,
                vertical: 16,
              ),
            ),
            items: _metodosPago.map((metodo) {
              return DropdownMenuItem<String>(
                value: metodo,
                child: Text(
                  metodo,
                  style: GoogleFonts.poppins(
                    fontSize: 16,
                    color: AppStyles.textColor,
                  ),
                ),
              );
            }).toList(),
            onChanged: (value) {
              setState(() {
                _metodoPago = value!;
              });
            },
          ),
        ],
      ),
    );
  }

  Widget _buildCostosAdicionalesSection() {
    final costosPendientes = widget.costosAdicionales
        .where((costo) => !costo.pagado)
        .toList();

    if (costosPendientes.isEmpty) {
      return const SizedBox.shrink();
    }

    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.add_circle_outline,
                color: AppStyles.primaryColor,
                size: 24,
              ),
              const SizedBox(width: 8),
              Text(
                'Costos Adicionales',
                style: GoogleFonts.poppins(
                  fontSize: 18,
                  fontWeight: FontWeight.w600,
                  color: AppStyles.textColor,
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Text(
            'Selecciona los costos adicionales que deseas incluir en este pago',
            style: GoogleFonts.poppins(
              fontSize: 14,
              color: AppStyles.secondaryTextColor,
            ),
          ),
          const SizedBox(height: 16),
          ...costosPendientes.map((costo) => _buildCostoToggle(costo)),
        ],
      ),
    );
  }

  Widget _buildCostoToggle(CostoAdicional costo) {
    final pendiente = costo.monto - costo.montoPagado;
    final isSelected = _costosSeleccionados[costo.id] ?? false;

    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: isSelected
            ? AppStyles.primaryColor.withOpacity(0.1)
            : Colors.grey[50],
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: isSelected
              ? AppStyles.primaryColor
              : Colors.grey[300]!,
          width: isSelected ? 2 : 1,
        ),
      ),
      child: Row(
        children: [
          Checkbox(
            value: isSelected,
            onChanged: (value) => _onCostSelectionChanged(costo.id, value ?? false),
            activeColor: AppStyles.primaryColor,
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  costo.nombre,
                  style: GoogleFonts.poppins(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                    color: AppStyles.textColor,
                  ),
                ),
                if (costo.descripcion.isNotEmpty) ...[
                  const SizedBox(height: 4),
                  Text(
                    costo.descripcion,
                    style: GoogleFonts.poppins(
                      fontSize: 14,
                      color: AppStyles.secondaryTextColor,
                    ),
                  ),
                ],
                const SizedBox(height: 8),
                Row(
                  children: [
                    Text(
                      'Pendiente: ',
                      style: GoogleFonts.poppins(
                        fontSize: 14,
                        color: AppStyles.secondaryTextColor,
                      ),
                    ),
                    Text(
                      '\$${pendiente.toStringAsFixed(2)}',
                      style: GoogleFonts.poppins(
                        fontSize: 14,
                        fontWeight: FontWeight.w600,
                        color: Colors.orange[700],
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildManualSelectionSection() {
    if (_currentSelection == null) return const SizedBox.shrink();

    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.checklist,
                color: AppStyles.primaryColor,
                size: 24,
              ),
              const SizedBox(width: 8),
              Text(
                'Selección Manual',
                style: GoogleFonts.poppins(
                  fontSize: 18,
                  fontWeight: FontWeight.w600,
                  color: AppStyles.textColor,
                ),
              ),
              const Spacer(),
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: _currentSelection!.remainingAmount > 0
                      ? Colors.orange[50]
                      : Colors.green[50],
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(
                    color: _currentSelection!.remainingAmount > 0
                        ? Colors.orange[300]!
                        : Colors.green[300]!,
                  ),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.end,
                  children: [
                    Text(
                      'Distribuido: \$${_currentSelection!.totalSelected.toStringAsFixed(2)}',
                      style: GoogleFonts.poppins(
                        fontSize: 12,
                        color: AppStyles.secondaryTextColor,
                      ),
                    ),
                    Text(
                      _currentSelection!.remainingAmount > 0
                          ? 'Restante: \$${_currentSelection!.remainingAmount.toStringAsFixed(2)}'
                          : '✓ Completo',
                      style: GoogleFonts.poppins(
                        fontSize: 14,
                        fontWeight: FontWeight.w600,
                        color: _currentSelection!.remainingAmount > 0
                            ? Colors.orange[700]
                            : Colors.green[700],
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),

          // Información simple
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: Colors.green[50],
              borderRadius: BorderRadius.circular(8),
              border: Border.all(color: Colors.green[200]!),
            ),
            child: Row(
              children: [
                Icon(Icons.info_outline, color: Colors.green[700], size: 16),
                const SizedBox(width: 8),
                Expanded(
                  child: Text(
                    'Selecciona costos adicionales si los hay, luego presiona "Distribuir" para asignar automáticamente a semanas',
                    style: GoogleFonts.poppins(
                      fontSize: 12,
                      color: Colors.green[700],
                    ),
                  ),
                ),
              ],
            ),
          ),

          const SizedBox(height: 16),

          // Botones simplificados
          Row(
            children: [
              Expanded(
                flex: 2,
                child: ElevatedButton.icon(
                  onPressed: _applySimpleDistribution,
                  icon: const Icon(Icons.auto_awesome, size: 18),
                  label: const Text('Distribuir Automáticamente'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: AppStyles.primaryColor,
                    foregroundColor: Colors.white,
                    padding: const EdgeInsets.symmetric(vertical: 12),
                  ),
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: OutlinedButton.icon(
                  onPressed: _clearAllSelections,
                  icon: const Icon(Icons.refresh, size: 16),
                  label: const Text('Reiniciar'),
                  style: OutlinedButton.styleFrom(
                    foregroundColor: AppStyles.primaryColor,
                    padding: const EdgeInsets.symmetric(vertical: 12),
                  ),
                ),
              ),
            ],
          ),

          const SizedBox(height: 20),

          // Sección de semanas
          _buildWeekSelectionSection(),

          const SizedBox(height: 20),

          // Sección de costos adicionales
          _buildCostSelectionSection(),
        ],
      ),
    );
  }

  Widget _buildWeekSelectionSection() {
    if (_currentSelection == null) return const SizedBox.shrink();

    final pendingWeeks = _currentSelection!.weekSelections
        .where((w) => w.isPending)
        .toList();
    final futureWeeks = _currentSelection!.weekSelections
        .where((w) => !w.isExisting)
        .toList();

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Semanas',
          style: GoogleFonts.poppins(
            fontSize: 16,
            fontWeight: FontWeight.w600,
            color: AppStyles.textColor,
          ),
        ),
        const SizedBox(height: 12),

        if (pendingWeeks.isNotEmpty) ...[
          Text(
            'Pendientes',
            style: GoogleFonts.poppins(
              fontSize: 14,
              fontWeight: FontWeight.w500,
              color: Colors.orange[700],
            ),
          ),
          const SizedBox(height: 8),
          ...pendingWeeks.map((week) => _buildWeekToggle(week)),
          const SizedBox(height: 12),
        ],

        if (futureWeeks.isNotEmpty) ...[
          Text(
            'Futuras',
            style: GoogleFonts.poppins(
              fontSize: 14,
              fontWeight: FontWeight.w500,
              color: Colors.green[700],
            ),
          ),
          const SizedBox(height: 8),
          ...futureWeeks.map((week) => _buildWeekToggle(week)),
        ],
      ],
    );
  }

  Widget _buildWeekToggle(WeekSelection week) {
    return Container(
      margin: const EdgeInsets.only(bottom: 6),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: week.isSelected
            ? (week.isExisting ? Colors.orange[50] : Colors.green[50])
            : Colors.grey[50],
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: week.isSelected
              ? (week.isExisting ? Colors.orange[300]! : Colors.green[300]!)
              : Colors.grey[300]!,
          width: week.isSelected ? 2 : 1,
        ),
      ),
      child: Row(
        children: [
          Icon(
            week.isSelected
                ? (week.isExisting ? Icons.schedule : Icons.add_circle)
                : Icons.radio_button_unchecked,
            color: week.isSelected
                ? (week.isExisting ? Colors.orange[600] : Colors.green[600])
                : Colors.grey[400],
            size: 20,
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Semana ${week.weekNumber}',
                  style: GoogleFonts.poppins(
                    fontSize: 14,
                    fontWeight: FontWeight.w600,
                    color: AppStyles.textColor,
                  ),
                ),
                Text(
                  week.isExisting ? 'Pendiente' : 'Nueva',
                  style: GoogleFonts.poppins(
                    fontSize: 12,
                    color: week.isExisting ? Colors.orange[600] : Colors.green[600],
                  ),
                ),
              ],
            ),
          ),
          Column(
            crossAxisAlignment: CrossAxisAlignment.end,
            children: [
              if (week.isSelected) ...[
                Text(
                  '\$${week.amount.toStringAsFixed(2)}',
                  style: GoogleFonts.poppins(
                    fontSize: 14,
                    fontWeight: FontWeight.w700,
                    color: AppStyles.primaryColor,
                  ),
                ),
                Text(
                  'asignado',
                  style: GoogleFonts.poppins(
                    fontSize: 10,
                    color: AppStyles.secondaryTextColor,
                  ),
                ),
              ] else ...[
                Text(
                  '\$${week.pendingAmount.toStringAsFixed(2)}',
                  style: GoogleFonts.poppins(
                    fontSize: 12,
                    color: AppStyles.secondaryTextColor,
                  ),
                ),
                Text(
                  'disponible',
                  style: GoogleFonts.poppins(
                    fontSize: 10,
                    color: AppStyles.secondaryTextColor,
                  ),
                ),
              ],
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildCostSelectionSection() {
    if (_currentSelection == null) return const SizedBox.shrink();

    final availableCosts = _currentSelection!.costSelections;
    if (availableCosts.isEmpty) return const SizedBox.shrink();

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Costos Adicionales',
          style: GoogleFonts.poppins(
            fontSize: 16,
            fontWeight: FontWeight.w600,
            color: AppStyles.textColor,
          ),
        ),
        const SizedBox(height: 12),
        ...availableCosts.map((cost) => _buildCostToggleManual(cost)),
      ],
    );
  }

  Widget _buildCostToggleManual(CostSelection cost) {
    return Container(
      margin: const EdgeInsets.only(bottom: 6),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: cost.isSelected
            ? Colors.blue[50]
            : Colors.grey[50],
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: cost.isSelected
              ? Colors.blue[300]!
              : Colors.grey[300]!,
          width: cost.isSelected ? 2 : 1,
        ),
      ),
      child: Row(
        children: [
          Checkbox(
            value: cost.isSelected,
            onChanged: (value) => _onCostSelectionChanged(cost.costoAdicional.id, value ?? false),
            activeColor: AppStyles.primaryColor,
          ),
          const SizedBox(width: 8),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  cost.costoAdicional.nombre,
                  style: GoogleFonts.poppins(
                    fontSize: 14,
                    fontWeight: FontWeight.w600,
                    color: AppStyles.textColor,
                  ),
                ),
                if (cost.costoAdicional.descripcion.isNotEmpty) ...[
                  const SizedBox(height: 2),
                  Text(
                    cost.costoAdicional.descripcion,
                    style: GoogleFonts.poppins(
                      fontSize: 12,
                      color: AppStyles.secondaryTextColor,
                    ),
                  ),
                ],
              ],
            ),
          ),
          Column(
            crossAxisAlignment: CrossAxisAlignment.end,
            children: [
              if (cost.isSelected) ...[
                Text(
                  '\$${cost.amount.toStringAsFixed(2)}',
                  style: GoogleFonts.poppins(
                    fontSize: 14,
                    fontWeight: FontWeight.w700,
                    color: Colors.blue[700],
                  ),
                ),
                Text(
                  'asignado',
                  style: GoogleFonts.poppins(
                    fontSize: 10,
                    color: AppStyles.secondaryTextColor,
                  ),
                ),
              ] else ...[
                Text(
                  '\$${cost.pendingAmount.toStringAsFixed(2)}',
                  style: GoogleFonts.poppins(
                    fontSize: 12,
                    color: AppStyles.secondaryTextColor,
                  ),
                ),
                Text(
                  'pendiente',
                  style: GoogleFonts.poppins(
                    fontSize: 10,
                    color: AppStyles.secondaryTextColor,
                  ),
                ),
              ],
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildDistributionSection() {
    if (_currentDistribution == null) return const SizedBox.shrink();

    return FadeTransition(
      opacity: _fadeAnimation,
      child: Container(
        padding: const EdgeInsets.all(20),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(16),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.05),
              blurRadius: 10,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.account_balance_wallet,
                  color: AppStyles.primaryColor,
                  size: 24,
                ),
                const SizedBox(width: 8),
                Text(
                  'Distribución del Pago',
                  style: GoogleFonts.poppins(
                    fontSize: 18,
                    fontWeight: FontWeight.w600,
                    color: AppStyles.textColor,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),

            // Resumen de distribución
            _buildDistributionSummary(),

            const SizedBox(height: 16),

            // Detalles de semanas
            if (_currentDistribution!.weekDistributions.isNotEmpty) ...[
              _buildWeekDistributionDetails(),
              const SizedBox(height: 16),
            ],

            // Detalles de costos adicionales
            if (_currentDistribution!.costDistributions.isNotEmpty) ...[
              _buildCostDistributionDetails(),
              const SizedBox(height: 16),
            ],

            // Monto restante si lo hay
            if (_currentDistribution!.remainingAmount > 0.01) ...[
              _buildRemainingAmount(),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildDistributionSummary() {
    final distribution = _currentDistribution!;

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: AppStyles.primaryColor.withOpacity(0.05),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: AppStyles.primaryColor.withOpacity(0.2),
        ),
      ),
      child: Column(
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'Monto Total:',
                style: GoogleFonts.poppins(
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                  color: AppStyles.textColor,
                ),
              ),
              Text(
                '\$${distribution.totalAmount.toStringAsFixed(2)}',
                style: GoogleFonts.poppins(
                  fontSize: 16,
                  fontWeight: FontWeight.w700,
                  color: AppStyles.primaryColor,
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'Distribuido:',
                style: GoogleFonts.poppins(
                  fontSize: 14,
                  color: AppStyles.secondaryTextColor,
                ),
              ),
              Text(
                '\$${distribution.totalDistributed.toStringAsFixed(2)}',
                style: GoogleFonts.poppins(
                  fontSize: 14,
                  fontWeight: FontWeight.w600,
                  color: Colors.green[700],
                ),
              ),
            ],
          ),
          if (distribution.remainingAmount > 0.01) ...[
            const SizedBox(height: 8),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'Restante:',
                  style: GoogleFonts.poppins(
                    fontSize: 14,
                    color: AppStyles.secondaryTextColor,
                  ),
                ),
                Text(
                  '\$${distribution.remainingAmount.toStringAsFixed(2)}',
                  style: GoogleFonts.poppins(
                    fontSize: 14,
                    fontWeight: FontWeight.w600,
                    color: Colors.orange[700],
                  ),
                ),
              ],
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildWeekDistributionDetails() {
    final weekDistributions = _currentDistribution!.weekDistributions;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Semanas (${weekDistributions.length})',
          style: GoogleFonts.poppins(
            fontSize: 16,
            fontWeight: FontWeight.w600,
            color: AppStyles.textColor,
          ),
        ),
        const SizedBox(height: 12),
        ...weekDistributions.map((week) => Container(
          margin: const EdgeInsets.only(bottom: 8),
          padding: const EdgeInsets.all(12),
          decoration: BoxDecoration(
            color: week.isExisting ? Colors.orange[50] : Colors.green[50],
            borderRadius: BorderRadius.circular(8),
            border: Border.all(
              color: week.isExisting ? Colors.orange[200]! : Colors.green[200]!,
            ),
          ),
          child: Row(
            children: [
              Icon(
                week.isExisting ? Icons.schedule : Icons.add_circle,
                size: 16,
                color: week.isExisting ? Colors.orange[700] : Colors.green[700],
              ),
              const SizedBox(width: 8),
              Expanded(
                child: Text(
                  'Semana ${week.numeroSemana} ${week.isExisting ? "(Pendiente)" : "(Nueva)"}',
                  style: GoogleFonts.poppins(
                    fontSize: 14,
                    fontWeight: FontWeight.w500,
                    color: AppStyles.textColor,
                  ),
                ),
              ),
              Text(
                '\$${week.amount.toStringAsFixed(2)}',
                style: GoogleFonts.poppins(
                  fontSize: 14,
                  fontWeight: FontWeight.w600,
                  color: week.isExisting ? Colors.orange[700] : Colors.green[700],
                ),
              ),
            ],
          ),
        )),
      ],
    );
  }

  Widget _buildCostDistributionDetails() {
    final costDistributions = _currentDistribution!.costDistributions;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Costos Adicionales (${costDistributions.length})',
          style: GoogleFonts.poppins(
            fontSize: 16,
            fontWeight: FontWeight.w600,
            color: AppStyles.textColor,
          ),
        ),
        const SizedBox(height: 12),
        ...costDistributions.map((cost) => Container(
          margin: const EdgeInsets.only(bottom: 8),
          padding: const EdgeInsets.all(12),
          decoration: BoxDecoration(
            color: Colors.blue[50],
            borderRadius: BorderRadius.circular(8),
            border: Border.all(
              color: Colors.blue[200]!,
            ),
          ),
          child: Row(
            children: [
              Icon(
                cost.isFullyPaid ? Icons.check_circle : Icons.schedule,
                size: 16,
                color: cost.isFullyPaid ? Colors.green[700] : Colors.blue[700],
              ),
              const SizedBox(width: 8),
              Expanded(
                child: Text(
                  cost.costoAdicional.nombre,
                  style: GoogleFonts.poppins(
                    fontSize: 14,
                    fontWeight: FontWeight.w500,
                    color: AppStyles.textColor,
                  ),
                ),
              ),
              Text(
                '\$${cost.amount.toStringAsFixed(2)}',
                style: GoogleFonts.poppins(
                  fontSize: 14,
                  fontWeight: FontWeight.w600,
                  color: Colors.blue[700],
                ),
              ),
            ],
          ),
        )),
      ],
    );
  }

  Widget _buildRemainingAmount() {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.amber[50],
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: Colors.amber[300]!,
        ),
      ),
      child: Row(
        children: [
          Icon(
            Icons.info_outline,
            color: Colors.amber[700],
            size: 20,
          ),
          const SizedBox(width: 8),
          Expanded(
            child: Text(
              'Monto restante: \$${_currentDistribution!.remainingAmount.toStringAsFixed(2)}',
              style: GoogleFonts.poppins(
                fontSize: 14,
                fontWeight: FontWeight.w500,
                color: Colors.amber[800],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildNotesSection() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.note_add,
                color: AppStyles.primaryColor,
                size: 24,
              ),
              const SizedBox(width: 8),
              Text(
                'Notas (Opcional)',
                style: GoogleFonts.poppins(
                  fontSize: 18,
                  fontWeight: FontWeight.w600,
                  color: AppStyles.textColor,
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          TextFormField(
            controller: _notasController,
            maxLines: 3,
            decoration: InputDecoration(
              hintText: 'Agregar notas o comentarios sobre este pago...',
              hintStyle: GoogleFonts.poppins(
                fontSize: 14,
                color: Colors.grey[400],
              ),
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
                borderSide: BorderSide(color: Colors.grey[300]!),
              ),
              focusedBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
                borderSide: BorderSide(color: AppStyles.primaryColor, width: 2),
              ),
              contentPadding: const EdgeInsets.all(16),
            ),
            style: GoogleFonts.poppins(
              fontSize: 14,
              color: AppStyles.textColor,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildGenerateButton() {
    final canGenerate = _formKey.currentState?.validate() ?? false;
    final hasAmount = (double.tryParse(_montoController.text) ?? 0) > 0;
    final hasDistribution = _currentDistribution != null &&
                           _currentDistribution!.totalDistributed > 0;

    return SizedBox(
      width: double.infinity,
      height: 56,
      child: ElevatedButton(
        onPressed: (canGenerate && hasAmount && hasDistribution)
            ? _generateRecibo
            : null,
        style: ElevatedButton.styleFrom(
          backgroundColor: AppStyles.primaryColor,
          foregroundColor: Colors.white,
          elevation: 2,
          shadowColor: AppStyles.primaryColor.withOpacity(0.3),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
          ),
          disabledBackgroundColor: Colors.grey[300],
          disabledForegroundColor: Colors.grey[600],
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.receipt_long,
              size: 24,
            ),
            const SizedBox(width: 12),
            Text(
              'Generar Recibo',
              style: GoogleFonts.poppins(
                fontSize: 18,
                fontWeight: FontWeight.w600,
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _generateRecibo() {
    if (!_formKey.currentState!.validate()) return;
    if (_currentDistribution == null) return;

    final amount = double.tryParse(_montoController.text) ?? 0;
    if (amount <= 0) return;

    // Crear los detalles del pago
    final detalles = _currentDistribution!.toDetallesPago(widget.alumno.id);

    // Crear el concepto del recibo
    String concepto = 'Pago de ';
    final conceptos = <String>[];

    if (_currentDistribution!.weekDistributions.isNotEmpty) {
      final semanas = _currentDistribution!.weekDistributions.length;
      conceptos.add('$semanas semana${semanas > 1 ? 's' : ''}');
    }

    if (_currentDistribution!.costDistributions.isNotEmpty) {
      final costos = _currentDistribution!.costDistributions.length;
      conceptos.add('$costos costo${costos > 1 ? 's' : ''} adicional${costos > 1 ? 'es' : ''}');
    }

    concepto += conceptos.join(' y ');

    // Agregar notas si las hay
    final notas = _notasController.text.trim();
    if (notas.isNotEmpty) {
      concepto += ' - $notas';
    }

    // Preparar los datos para el callback
    final reciboData = {
      'alumno': widget.alumno,
      'monto': amount,
      'metodoPago': _metodoPago,
      'concepto': concepto,
      'detalles': detalles,
      'distribution': _currentDistribution,
      'notas': notas,
    };

    // Llamar al callback
    widget.onGenerarRecibo(reciboData);
  }

  Widget _buildAmountInput({
    required double currentAmount,
    required double maxAmount,
    required Function(double) onChanged,
    required String label,
  }) {
    final controller = TextEditingController(text: currentAmount.toStringAsFixed(2));

    // Calcular el monto disponible total
    final availableAmount = _currentSelection?.remainingAmount ?? 0;
    final totalAvailable = availableAmount + currentAmount;
    final effectiveMax = [maxAmount, totalAvailable].reduce((a, b) => a < b ? a : b);

    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: AppStyles.primaryColor.withOpacity(0.3)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Expanded(
                child: Text(
                  label,
                  style: GoogleFonts.poppins(
                    fontSize: 12,
                    fontWeight: FontWeight.w500,
                    color: AppStyles.textColor,
                  ),
                ),
              ),
              Text(
                'Disponible: \$${effectiveMax.toStringAsFixed(2)}',
                style: GoogleFonts.poppins(
                  fontSize: 11,
                  color: effectiveMax > 0 ? Colors.green[600] : Colors.red[600],
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Row(
            children: [
              Expanded(
                flex: 2,
                child: TextFormField(
                  controller: controller,
                  keyboardType: const TextInputType.numberWithOptions(decimal: true),
                  inputFormatters: [
                    FilteringTextInputFormatter.allow(RegExp(r'^\d*\.?\d{0,2}')),
                  ],
                  style: GoogleFonts.poppins(
                    fontSize: 14,
                    fontWeight: FontWeight.w600,
                    color: AppStyles.textColor,
                  ),
                  decoration: InputDecoration(
                    prefixText: '\$ ',
                    prefixStyle: GoogleFonts.poppins(
                      fontSize: 14,
                      fontWeight: FontWeight.w600,
                      color: AppStyles.primaryColor,
                    ),
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(8),
                      borderSide: BorderSide(color: Colors.grey[300]!),
                    ),
                    focusedBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(8),
                      borderSide: BorderSide(color: AppStyles.primaryColor, width: 2),
                    ),
                    contentPadding: const EdgeInsets.symmetric(
                      horizontal: 12,
                      vertical: 8,
                    ),
                    isDense: true,
                  ),
                  onChanged: (value) {
                    final amount = double.tryParse(value) ?? 0;
                    onChanged(amount);
                  },
                ),
              ),
              const SizedBox(width: 8),
              // Botones de monto rápido
              Expanded(
                flex: 3,
                child: Wrap(
                  spacing: 4,
                  runSpacing: 4,
                  children: [
                    _buildQuickAmountButton(
                      label: 'Máx',
                      amount: effectiveMax,
                      onTap: () {
                        controller.text = effectiveMax.toStringAsFixed(2);
                        onChanged(effectiveMax);
                      },
                    ),
                    _buildQuickAmountButton(
                      label: '50%',
                      amount: effectiveMax * 0.5,
                      onTap: () {
                        final amount = effectiveMax * 0.5;
                        controller.text = amount.toStringAsFixed(2);
                        onChanged(amount);
                      },
                    ),
                    _buildQuickAmountButton(
                      label: 'Todo',
                      amount: maxAmount,
                      onTap: () {
                        final amount = [maxAmount, effectiveMax].reduce((a, b) => a < b ? a : b);
                        controller.text = amount.toStringAsFixed(2);
                        onChanged(amount);
                      },
                    ),
                  ],
                ),
              ),
            ],
          ),
          if (effectiveMax < maxAmount) ...[
            const SizedBox(height: 4),
            Text(
              'Monto limitado por disponibilidad total',
              style: GoogleFonts.poppins(
                fontSize: 10,
                color: Colors.orange[600],
                fontStyle: FontStyle.italic,
              ),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildQuickAmountButton({
    required String label,
    required double amount,
    required VoidCallback onTap,
  }) {
    return GestureDetector(
      onTap: amount > 0 ? onTap : null,
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
        decoration: BoxDecoration(
          color: amount > 0
              ? AppStyles.primaryColor.withOpacity(0.1)
              : Colors.grey[200],
          borderRadius: BorderRadius.circular(12),
          border: Border.all(
            color: amount > 0
                ? AppStyles.primaryColor.withOpacity(0.3)
                : Colors.grey[300]!,
          ),
        ),
        child: Text(
          label,
          style: GoogleFonts.poppins(
            fontSize: 10,
            fontWeight: FontWeight.w500,
            color: amount > 0
                ? AppStyles.primaryColor
                : Colors.grey[500],
          ),
        ),
      ),
    );
  }
}
