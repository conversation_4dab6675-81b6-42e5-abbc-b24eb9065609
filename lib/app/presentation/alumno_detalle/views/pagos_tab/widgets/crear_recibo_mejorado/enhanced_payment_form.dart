import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:google_fonts/google_fonts.dart';
import '../../../../../../domain/entities/alumno.dart';
import '../../../../../../domain/modelos/kardex.dart';
import '../../../../../../domain/modelos/costo_adicional.dart';
import '../../../../../../styles/app_styles.dart';
import 'payment_distribution_engine.dart';
import 'enhanced_payment_preview.dart';
import 'smart_amount_input.dart';
import 'payment_breakdown_card.dart';

class EnhancedPaymentForm extends StatefulWidget {
  final Alumno alumno;
  final List<CostoAdicional> costosAdicionales;
  final List<KardexSemana> semanas;
  final double montoPorSemana;
  final Function(Map<String, dynamic>) onGenerarRecibo;

  const EnhancedPaymentForm({
    Key? key,
    required this.alumno,
    required this.costosAdicionales,
    required this.semanas,
    required this.montoPorSemana,
    required this.onGenerarRecibo,
  }) : super(key: key);

  @override
  State<EnhancedPaymentForm> createState() => _EnhancedPaymentFormState();
}

class _EnhancedPaymentFormState extends State<EnhancedPaymentForm>
    with TickerProviderStateMixin {
  final _formKey = GlobalKey<FormState>();
  final _montoController = TextEditingController();
  final _notasController = TextEditingController();

  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;

  PaymentDistribution? _currentDistribution;
  bool _showPreview = false;
  String _metodoPago = 'Efectivo';

  final List<String> _metodosPago = [
    'Efectivo',
    'Transferencia',
    'Tarjeta de Débito',
    'Tarjeta de Crédito',
    'Cheque'
  ];

  @override
  void initState() {
    super.initState();
    _setupAnimations();
    _montoController.addListener(_onAmountChanged);
  }

  void _setupAnimations() {
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 600),
      vsync: this,
    );

    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));

    _slideAnimation = Tween<Offset>(
      begin: const Offset(0, 0.3),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeOutCubic,
    ));
  }

  @override
  void dispose() {
    _animationController.dispose();
    _montoController.dispose();
    _notasController.dispose();
    super.dispose();
  }

  void _onAmountChanged() {
    final amount = double.tryParse(_montoController.text) ?? 0;
    if (amount > 0) {
      final distribution = PaymentDistributionEngine.calculateDistribution(
        amount: amount,
        costosAdicionales: widget.costosAdicionales,
        semanas: widget.semanas,
        montoPorSemana: widget.montoPorSemana,
      );

      setState(() {
        _currentDistribution = distribution;
        _showPreview = true;
      });

      if (!_animationController.isAnimating) {
        _animationController.forward();
      }
    } else {
      setState(() {
        _currentDistribution = null;
        _showPreview = false;
      });
      _animationController.reverse();
    }
  }

  void _onGenerarRecibo() async {
    if (!_formKey.currentState!.validate() || _currentDistribution == null) {
      return;
    }

    // Mostrar vista previa del recibo
    final shouldGenerate = await showDialog<bool>(
      context: context,
      builder: (context) => EnhancedPaymentPreview(
        alumno: widget.alumno,
        distribution: _currentDistribution!,
        metodoPago: _metodoPago,
        notas: _notasController.text.trim().isEmpty ? null : _notasController.text.trim(),
      ),
    );

    if (shouldGenerate != true) return;

    try {
      // Mostrar loading
      showDialog(
        context: context,
        barrierDismissible: false,
        builder: (context) => const Center(
          child: CircularProgressIndicator(),
        ),
      );

      final reciboData = {
        'alumno': widget.alumno,
        'distribution': _currentDistribution,
        'metodoPago': _metodoPago,
        'notas': _notasController.text.trim(),
        'montoTotal': double.parse(_montoController.text),
      };

      await widget.onGenerarRecibo(reciboData);

      Navigator.of(context).pop(); // Cerrar loading
      Navigator.of(context).pop(); // Cerrar formulario

      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Row(
            children: [
              Icon(Icons.check_circle, color: Colors.white),
              SizedBox(width: 8),
              Text('Recibo generado correctamente'),
            ],
          ),
          backgroundColor: Colors.green,
        ),
      );

    } catch (e) {
      Navigator.of(context).pop(); // Cerrar loading
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Row(
            children: [
              const Icon(Icons.error, color: Colors.white),
              const SizedBox(width: 8),
              Text('Error al generar el recibo: $e'),
            ],
          ),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey[50],
      appBar: AppBar(
        title: Text(
          'Nuevo Recibo',
          style: GoogleFonts.poppins(
            fontWeight: FontWeight.w600,
            color: Colors.white,
          ),
        ),
        backgroundColor: AppStyles.primaryColor,
        elevation: 0,
        iconTheme: const IconThemeData(color: Colors.white),
      ),
      body: Form(
        key: _formKey,
        child: Column(
          children: [
            Expanded(
              child: SingleChildScrollView(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    _buildStudentHeader(),
                    const SizedBox(height: 24),
                    _buildAmountSection(),
                    const SizedBox(height: 24),
                    _buildPaymentMethodSection(),
                    const SizedBox(height: 24),
                    _buildNotesSection(),
                    if (_showPreview && _currentDistribution != null) ...[
                      const SizedBox(height: 32),
                      _buildDistributionPreview(),
                    ],
                  ],
                ),
              ),
            ),
            _buildBottomActions(),
          ],
        ),
      ),
    );
  }

  Widget _buildStudentHeader() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        children: [
          Container(
            width: 60,
            height: 60,
            decoration: BoxDecoration(
              color: AppStyles.primaryColor.withOpacity(0.1),
              borderRadius: BorderRadius.circular(30),
            ),
            child: Icon(
              Icons.person,
              color: AppStyles.primaryColor,
              size: 30,
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  widget.alumno.nombre,
                  style: GoogleFonts.poppins(
                    fontSize: 18,
                    fontWeight: FontWeight.w600,
                    color: AppStyles.textColor,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  'ID: ${widget.alumno.id}',
                  style: GoogleFonts.poppins(
                    fontSize: 14,
                    color: AppStyles.secondaryTextColor,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildAmountSection() {
    return SmartAmountInput(
      controller: _montoController,
      costosAdicionales: widget.costosAdicionales,
      semanas: widget.semanas,
      montoPorSemana: widget.montoPorSemana,
    );
  }

  Widget _buildPaymentMethodSection() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Método de Pago',
            style: GoogleFonts.poppins(
              fontSize: 16,
              fontWeight: FontWeight.w600,
              color: AppStyles.textColor,
            ),
          ),
          const SizedBox(height: 16),
          DropdownButtonFormField<String>(
            value: _metodoPago,
            decoration: InputDecoration(
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
                borderSide: BorderSide(color: Colors.grey[300]!),
              ),
              focusedBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
                borderSide: BorderSide(color: AppStyles.primaryColor),
              ),
              contentPadding: const EdgeInsets.symmetric(
                horizontal: 16,
                vertical: 12,
              ),
            ),
            items: _metodosPago.map((metodo) {
              return DropdownMenuItem(
                value: metodo,
                child: Text(metodo),
              );
            }).toList(),
            onChanged: (value) {
              setState(() {
                _metodoPago = value!;
              });
            },
          ),
        ],
      ),
    );
  }

  Widget _buildNotesSection() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Notas (Opcional)',
            style: GoogleFonts.poppins(
              fontSize: 16,
              fontWeight: FontWeight.w600,
              color: AppStyles.textColor,
            ),
          ),
          const SizedBox(height: 16),
          TextFormField(
            controller: _notasController,
            maxLines: 3,
            decoration: InputDecoration(
              hintText: 'Agregar notas adicionales...',
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
                borderSide: BorderSide(color: Colors.grey[300]!),
              ),
              focusedBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
                borderSide: BorderSide(color: AppStyles.primaryColor),
              ),
              contentPadding: const EdgeInsets.all(16),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDistributionPreview() {
    return FadeTransition(
      opacity: _fadeAnimation,
      child: SlideTransition(
        position: _slideAnimation,
        child: PaymentBreakdownCard(
          distribution: _currentDistribution!,
          montoPorSemana: widget.montoPorSemana,
        ),
      ),
    );
  }

  Widget _buildBottomActions() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 10,
            offset: const Offset(0, -2),
          ),
        ],
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // Botón de vista previa (solo si hay distribución)
          if (_currentDistribution != null) ...[
            SizedBox(
              width: double.infinity,
              child: OutlinedButton.icon(
                onPressed: () => _showPreviewDialog(),
                icon: const Icon(Icons.preview),
                label: Text(
                  'Vista Previa del Recibo',
                  style: GoogleFonts.poppins(
                    fontSize: 14,
                    fontWeight: FontWeight.w500,
                  ),
                ),
                style: OutlinedButton.styleFrom(
                  padding: const EdgeInsets.symmetric(vertical: 12),
                  side: BorderSide(color: Colors.grey[400]!),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                ),
              ),
            ),
            const SizedBox(height: 12),
          ],

          // Botones principales
          Row(
            children: [
              Expanded(
                child: OutlinedButton(
                  onPressed: () => Navigator.of(context).pop(),
                  style: OutlinedButton.styleFrom(
                    padding: const EdgeInsets.symmetric(vertical: 16),
                    side: BorderSide(color: AppStyles.primaryColor),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                  ),
                  child: Text(
                    'Cancelar',
                    style: GoogleFonts.poppins(
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                      color: AppStyles.primaryColor,
                    ),
                  ),
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                flex: 2,
                child: ElevatedButton(
                  onPressed: _currentDistribution != null ? _onGenerarRecibo : null,
                  style: ElevatedButton.styleFrom(
                    backgroundColor: AppStyles.primaryColor,
                    padding: const EdgeInsets.symmetric(vertical: 16),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                    elevation: 2,
                  ),
                  child: Text(
                    'Generar Recibo',
                    style: GoogleFonts.poppins(
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                      color: Colors.white,
                    ),
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  void _showPreviewDialog() {
    if (_currentDistribution == null) return;

    showDialog(
      context: context,
      builder: (context) => EnhancedPaymentPreview(
        alumno: widget.alumno,
        distribution: _currentDistribution!,
        metodoPago: _metodoPago,
        notas: _notasController.text.trim().isEmpty ? null : _notasController.text.trim(),
      ),
    );
  }
}
