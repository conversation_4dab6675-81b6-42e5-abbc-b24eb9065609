import 'package:flutter/material.dart';
import 'package:gemini_app/app/domain/modelos/kardex.dart';
import 'package:gemini_app/app/presentation/alumno_detalle/views/pagos_tab/widgets/crear_recibo/week_chip.dart';

class WeeksSelection extends StatelessWidget {
  final List<int> weeksToPay;
  final double amountAssignedToWeeks;
  final double amountAssignedToPendingWeek;
  final KardexSemana? pendingWeek;
  final double weeklyAmount;
  final Color primaryColor;
  final Color successColor;
  final Color warningColor;
  final Color textPrimary;

  const WeeksSelection({
    Key? key,
    required this.weeksToPay,
    required this.amountAssignedToWeeks,
    required this.amountAssignedToPendingWeek,
    required this.pendingWeek,
    required this.weeklyAmount,
    required this.primaryColor,
    required this.successColor,
    required this.warningColor,
    required this.textPrimary,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const SizedBox(height: 16),
        _buildSectionTitle('Semanas a Cubrir'),
        const Sized<PERSON><PERSON>(height: 8),
        <PERSON><PERSON><PERSON><PERSON>(
          height: 50,
          child: SingleChildScrollView(
            scrollDirection: Axis.horizontal,
            child: Row(
              children: [
                if (pendingWeek != null && amountAssignedToPendingWeek > 0)
                  Padding(
                    padding: const EdgeInsets.only(right: 8),
                    child: WeekChip(
                      weekNumber: pendingWeek!.numeroSemana,
                      amountPaid: amountAssignedToPendingWeek,
                      totalAmount: pendingWeek!.monto - pendingWeek!.montoPagado,
                      isPending: true,
                      primaryColor: primaryColor,
                      successColor: successColor,
                      warningColor: warningColor,
                    ),
                  ),
                ...weeksToPay.map((semana) {
                  final index = weeksToPay.indexOf(semana);
                  final isLast = index == weeksToPay.length - 1;
                  final isPartial = isLast &&
                      amountAssignedToWeeks < weeklyAmount * weeksToPay.length;

                  final amount = isPartial
                      ? amountAssignedToWeeks - (weeklyAmount * (weeksToPay.length - 1))
                      : weeklyAmount;

                  return Padding(
                    padding: const EdgeInsets.only(right: 8),
                    child: WeekChip(
                      weekNumber: semana,
                      amountPaid: amount,
                      totalAmount: weeklyAmount,
                      primaryColor: primaryColor,
                      successColor: successColor,
                      warningColor: warningColor,
                    ),
                  );
                }).toList(),
              ],
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildSectionTitle(String title) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 12),
      child: Row(
        children: [
          Container(
            width: 4,
            height: 16,
            decoration: BoxDecoration(
              color: textPrimary,
              borderRadius: BorderRadius.circular(2),
            ),
          ),
          const SizedBox(width: 12),
          Text(
            title,
            style: TextStyle(
              fontSize: 15,
              fontWeight: FontWeight.w600,
              color: textPrimary,
            ),
          ),
        ],
      ),
    );
  }
}