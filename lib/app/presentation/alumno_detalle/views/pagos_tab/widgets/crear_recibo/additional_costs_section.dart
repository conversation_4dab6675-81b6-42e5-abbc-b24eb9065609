import 'package:flutter/material.dart';
import 'cost_input_item.dart';

class AdditionalCostsSection extends StatelessWidget {
  final List<Map<String, dynamic>> costs;
  final double totalAmount;
  final Function(int, String) onCostChanged;
  final Color primaryColor;
  final Color textPrimary;
  final Color textSecondary;

  const AdditionalCostsSection({
    Key? key,
    required this.costs,
    required this.totalAmount,
    required this.onCostChanged,
    required this.primaryColor,
    required this.textPrimary,
    required this.textSecondary,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildSectionTitle('Costos Adicionales', textPrimary: textPrimary),
        const SizedBox(height: 16),
        ...List.generate(costs.length, (index) {
          final cost = costs[index];
          final controller = cost['controller'] as TextEditingController;
          final pendiente = (cost['pendiente'] as num).toDouble();
          final nombre = cost['nombre'] as String;

          if (pendiente <= 0) return const SizedBox.shrink();

          // Calculate maximum available amount for this cost
          double montoUsadoEnOtrosCostos = 0;
          for (var i = 0; i < costs.length; i++) {
            if (i != index) {
              montoUsadoEnOtrosCostos +=
                  double.tryParse(costs[i]['controller'].text) ?? 0;
            }
          }

          final double montoMaximo = (totalAmount - montoUsadoEnOtrosCostos)
              .clamp(0, pendiente);
          final bool deshabilitado = montoMaximo <= 0 &&
              (double.tryParse(controller.text) ?? 0) <= 0;

          return CostInputItem(
            label: nombre,
            pendingAmount: pendiente,
            controller: controller,
            maxAmount: montoMaximo,
            disabled: deshabilitado,
            onChanged: (value) => onCostChanged(index, value),
          );
        }),
      ],
    );
  }

  Widget _buildSectionTitle(String title, {required Color textPrimary}) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 12),
      child: Row(
        children: [
          Container(
            width: 4,
            height: 16,
            decoration: BoxDecoration(
              color: textPrimary,
              borderRadius: BorderRadius.circular(2),
            ),
          ),
          const SizedBox(width: 12),
          Text(
            title,
            style: TextStyle(
              fontSize: 15,
              fontWeight: FontWeight.w600,
              color: textPrimary,
            ),
          ),
        ],
      ),
    );
  }
}