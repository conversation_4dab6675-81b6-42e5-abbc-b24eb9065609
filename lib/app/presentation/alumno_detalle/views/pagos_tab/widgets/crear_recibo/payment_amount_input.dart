import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

class PaymentAmountInput extends StatelessWidget {
  final TextEditingController controller;
  final VoidCallback onCalculate;
  final FormFieldValidator<String> validator;
  final ValueChanged<String> onChanged;
  final Color primaryColor;
  final Color textPrimary;

  const PaymentAmountInput({
    Key? key,
    required this.controller,
    required this.onCalculate,
    required this.validator,
    required this.onChanged,
    required this.primaryColor,
    required this.textPrimary,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: primaryColor.withOpacity(0.1),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: TextFormField(
        controller: controller,
        decoration: InputDecoration(
          labelText: 'Monto total',
          hintText: '0.00',
          floatingLabelBehavior: FloatingLabelBehavior.always,
          prefixIcon: Padding(
            padding: const EdgeInsets.only(left: 16, right: 8),
            child: Text(
              '\$',
              style: TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
                color: textPrimary,
              ),
            ),
          ),
          prefixIconConstraints: const BoxConstraints(
            minWidth: 0,
            minHeight: 0,
          ),
          suffixIcon: IconButton(
            icon: Icon(Icons.calculate, color: primaryColor),
            onPressed: onCalculate,
          ),
          border: OutlineInputBorder(
            borderRadius: BorderRadius.circular(12),
            borderSide: BorderSide.none,
          ),
          filled: true,
          fillColor: Colors.white,
          contentPadding: const EdgeInsets.symmetric(
            horizontal: 16,
            vertical: 18,
          ),
        ),
        style: TextStyle(
          fontSize: 18,
          fontWeight: FontWeight.w600,
          color: textPrimary,
        ),
        keyboardType: const TextInputType.numberWithOptions(decimal: true),
        inputFormatters: [
          FilteringTextInputFormatter.allow(RegExp(r'^\d*\.?\d{0,2}$')),
        ],
        onChanged: onChanged,
        validator: validator,
      ),
    );
  }
}