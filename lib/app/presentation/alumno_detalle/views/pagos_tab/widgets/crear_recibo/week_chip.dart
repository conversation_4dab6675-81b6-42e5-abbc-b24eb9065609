import 'package:flutter/material.dart';

class WeekChip extends StatelessWidget {
  final int weekNumber;
  final double amountPaid;
  final double totalAmount;
  final bool isPending;
  final Color primaryColor;
  final Color successColor;
  final Color warningColor;

  const WeekChip({
    Key? key,
    required this.weekNumber,
    required this.amountPaid,
    required this.totalAmount,
    this.isPending = false,
    required this.primaryColor,
    required this.successColor,
    required this.warningColor,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final isFullyPaid = amountPaid >= totalAmount;
    final isPartial = amountPaid > 0 && amountPaid < totalAmount;
    final chipColor = isFullyPaid
        ? successColor
        : isPartial
            ? warningColor
            : Colors.grey;
    
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      decoration: BoxDecoration(
        color: chipColor.withOpacity(0.1),
        borderRadius: BorderRadius.circular(20),
        border: Border.all(color: chipColor.withOpacity(0.3)),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            isFullyPaid
                ? Icons.check_circle_outline
                : isPartial
                    ? Icons.pending_outlined
                    : Icons.pending_actions_outlined,
            size: 16,
            color: chipColor,
          ),
          const SizedBox(width: 6),
          Text(
            'Semana $weekNumber',
            style: TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.w500,
              color: chipColor,
            ),
          ),
          if (!isFullyPaid)
            Padding(
              padding: const EdgeInsets.only(left: 4),
              child: Text(
                'Pendiente: \$${(totalAmount - amountPaid).toStringAsFixed(2)}',
                style: TextStyle(
                  fontSize: 12,
                  color: chipColor,
                ),
              ),
            ),
        ],
      ),
    );
  }
}