import 'package:flutter/material.dart';
import 'summary_row.dart';

class PaymentSummary extends StatelessWidget {
  final List<Map<String, dynamic>> additionalCosts;
  final List<Map<String, dynamic>> weekItems;
  final double totalAmount;
  final Color primaryColor;
  final Color textPrimary;
  final Color textSecondary;
  final Color cardBackground;

  const PaymentSummary({
    Key? key,
    required this.additionalCosts,
    required this.weekItems,
    required this.totalAmount,
    required this.primaryColor,
    required this.textPrimary,
    required this.textSecondary,
    required this.cardBackground,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.only(top: 24),
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: cardBackground,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        children: [
          Row(
            children: [
              Icon(Icons.receipt_long, size: 20, color: primaryColor),
              const SizedBox(width: 8),
              Text(
                'Resumen del Pago',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                  color: textPrimary,
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          Column(
            children: [
              ...additionalCosts.map((cost) {
                final monto = double.tryParse(cost['controller'].text) ?? 0;
                if (monto <= 0) return const SizedBox.shrink();
                return SummaryRow(
                  label: cost['nombre'],
                  amount: monto,
                  primaryColor: primaryColor,
                  textPrimary: textPrimary,
                  textSecondary: textSecondary,
                );
              }).toList(),
              ...weekItems.map((week) {
                return SummaryRow(
                  label: week['label'],
                  amount: week['amount'],
                  primaryColor: primaryColor,
                  textPrimary: textPrimary,
                  textSecondary: textSecondary,
                );
              }).toList(),
              const Divider(height: 24, color: Color(0xFFE5E7EB)),
              SummaryRow(
                label: 'TOTAL',
                amount: totalAmount,
                isTotal: true,
                primaryColor: primaryColor,
                textPrimary: textPrimary,
                textSecondary: textSecondary,
              ),
            ],
          ),
        ],
      ),
    );
  }
}