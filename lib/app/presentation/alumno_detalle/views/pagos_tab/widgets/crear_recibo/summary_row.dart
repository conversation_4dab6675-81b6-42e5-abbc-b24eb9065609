import 'package:flutter/material.dart';

class SummaryRow extends StatelessWidget {
  final String label;
  final double amount;
  final bool isTotal;
  final Color primaryColor;
  final Color textPrimary;
  final Color textSecondary;

  const SummaryRow({
    Key? key,
    required this.label,
    required this.amount,
    this.isTotal = false,
    required this.primaryColor,
    required this.textPrimary,
    required this.textSecondary,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 12),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            label,
            style: TextStyle(
              fontSize: isTotal ? 15 : 14,
              fontWeight: isTotal ? FontWeight.w600 : FontWeight.normal,
              color: isTotal ? textPrimary : textSecondary,
            ),
          ),
          Text(
            '\$${amount.toStringAsFixed(2)}',
            style: TextStyle(
              fontSize: isTotal ? 16 : 14,
              fontWeight: isTotal ? FontWeight.w700 : FontWeight.w500,
              color: isTotal ? primaryColor : textPrimary,
            ),
          ),
        ],
      ),
    );
  }
}