import 'package:get/get.dart';
import 'package:gemini_app/app/domain/modelos/recibo.dart';
import 'package:gemini_app/app/domain/modelos/detalle_pago_unificado.dart';
import 'package:gemini_app/app/domain/repositories/kardex_repository.dart';
import 'package:gemini_app/app/domain/repositories/recibo_repository.dart';
import 'package:gemini_app/app/presentation/alumno_detalle/controller/detalle_alumno.controller.dart';

class PaymentController extends GetxController {
  final KardexRepository _kardexRepo;
  final ReciboRepository _reciboRepo;
  final DetalleAlumnoController _detalleAlumnoController = Get.find();

  PaymentController(this._kardexRepo, this._reciboRepo);

  Recibo _generarRecibo(
    String id,
    String alumnoId,
    String alumnoNombre,
    double total,
    List<DetallePagoUnificado> detallesKardex,
    String metodoPago,
    String concepto,
  ) {
    return Recibo(
      id: id,
      folio: Recibo.generarFolio(),
      alumnoId: alumnoId,
      alumnoNombre: alumnoNombre,
      fechaEmision: DateTime.now(),
      montoTotal: total,
      metodoPago: metodoPago,
      concepto: concepto,
      detalles: detallesKardex,
      estado: 'activo',
      creadoPor: 'Sistema', // TODO: Reemplazar con usuario actual
    );
  }

  Future<void> crearRecibo(
    String id,
    String alumnoId,
    String alumnoNombre,
    double total,
    List<DetallePagoUnificado> detallesKardex,
    String metodoPago,
    String concepto,
  ) async {
    try {
      final recibo = _generarRecibo(
        id,
        alumnoId,
        alumnoNombre,
        total,
        detallesKardex,
        metodoPago,
        concepto,
      );
      await _reciboRepo.crearRecibo(recibo.toMap());
      await Future.delayed(const Duration(milliseconds: 500));

      // Recargar todos los datos del alumno para actualizar la información
      await _detalleAlumnoController.loadAlumnoData(alumnoId);
    } catch (e) {
      // Si hay un error, igualmente intentamos actualizar los datos
      _detalleAlumnoController.loadAlumnoData(alumnoId);
      rethrow;
    }
  }
}
