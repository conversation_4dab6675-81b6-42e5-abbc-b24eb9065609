import 'dart:async';
import 'package:flutter/material.dart';
import 'package:gemini_app/app/presentation/alumno_detalle/views/pagos_tab/widgets/crear_recibo_mejorado/inline_payment_form.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:get_it/get_it.dart';
import 'package:gemini_app/app/domain/entities/alumno.dart';
import 'package:gemini_app/app/domain/modelos/detalle_pago_unificado.dart';
import 'package:gemini_app/app/domain/modelos/kardex.dart';
import 'package:gemini_app/app/domain/modelos/costo_adicional.dart';
import '../../../../styles/app_styles.dart';
import '../../../../domain/modelos/recibo.dart';
import '../../../../domain/repositories/kardex_repository.dart';
import '../../../../domain/repositories/recibo_repository.dart';
import 'widgets/crear_recibo_mejorado/payment_distribution_engine.dart';

// Widget principal que maneja la sección de pagos
class PaymentSection extends StatelessWidget {
  final Alumno alumno;
  final List<CostoAdicional> costosAdicionales;
  final Function(Map<String, dynamic>) onGenerarRecibo;
  final VoidCallback? onReciboCreated;

  const PaymentSection({
    Key? key,
    required this.costosAdicionales,
    required this.onGenerarRecibo,
    required this.alumno,
    this.onReciboCreated,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return EnhancedPaymentSection(
      alumno: alumno,
      costosAdicionales: costosAdicionales,
      onGenerarRecibo: onGenerarRecibo,
      onReciboCreated: onReciboCreated,
    );
  }
}

// Nuevo widget mejorado que reemplaza la funcionalidad anterior
class EnhancedPaymentSection extends StatefulWidget {
  final Alumno alumno;
  final List<CostoAdicional> costosAdicionales;
  final Function(Map<String, dynamic>) onGenerarRecibo;
  final VoidCallback? onReciboCreated; // Callback para cambiar de tab

  const EnhancedPaymentSection({
    Key? key,
    required this.costosAdicionales,
    required this.onGenerarRecibo,
    required this.alumno,
    this.onReciboCreated,
  }) : super(key: key);

  @override
  _EnhancedPaymentSectionState createState() => _EnhancedPaymentSectionState();
}

class _EnhancedPaymentSectionState extends State<EnhancedPaymentSection> {
  List<KardexSemana> _semanas = [];
  double _montoPorSemana = 0.0;
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadData();
  }

  Future<void> _loadData() async {
    try {
      final kardexRepo = GetIt.instance<KardexRepository>();

      // Load kardex data
      final kardex = await kardexRepo.getKardexByAlumnoId(widget.alumno.id);
      final semanas = await kardexRepo.getSemanas(widget.alumno.id);

      setState(() {
        _montoPorSemana = kardex.montoPorSemana;
        _semanas = semanas;
        _isLoading = false;
      });
    } catch (e) {
      debugPrint('Error loading data: $e');
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _onGenerarRecibo(Map<String, dynamic> reciboData) async {
    try {
      debugPrint('Recibo data received: ${reciboData.keys}');
      debugPrint('Monto value: ${reciboData['monto']}');
      debugPrint('Monto type: ${reciboData['monto'].runtimeType}');

      // Validar que todos los campos requeridos estén presentes
      if (reciboData['distribution'] == null) {
        throw Exception('Distribution is null');
      }
      if (reciboData['metodoPago'] == null) {
        throw Exception('MetodoPago is null');
      }
      if (reciboData['monto'] == null) {
        throw Exception('Monto is null');
      }
      if (reciboData['concepto'] == null) {
        throw Exception('Concepto is null');
      }

      final distribution = reciboData['distribution'] as PaymentDistribution;
      final metodoPago = reciboData['metodoPago'] as String;
      final montoTotal = (reciboData['monto'] as num).toDouble();
      final concepto = reciboData['concepto'] as String;

      debugPrint('All values extracted successfully');
      debugPrint('MontoTotal: $montoTotal');
      debugPrint('MetodoPago: $metodoPago');
      debugPrint('Concepto: $concepto');

      // Generar detalles de pago
      final detalles = distribution.toDetallesPago(widget.alumno.id);

      final kardexRepo = GetIt.instance<KardexRepository>();

      // Generate reciboId for all payment details
      final reciboId = 'REC-${DateTime.now().millisecondsSinceEpoch}';

      // Save each payment detail using KardexRepository
      for (final detalle in detalles) {
        final newDetalle = DetallePagoUnificado(
          id: 'DET-PAGO-${DateTime.now().millisecondsSinceEpoch}-${detalles.indexOf(detalle)}',
          tipo: detalle.tipo,
          concepto: detalle.concepto,
          referenciaId: detalle.referenciaId,
          monto: detalle.monto,
          fecha: detalle.fecha,
          alumnoId: widget.alumno.id,
          reciboId: reciboId,
          numeroSemana: detalle.numeroSemana,
        );
        await kardexRepo.guardarDetallePago(newDetalle);
      }

      // Create receipt using ReciboRepository
      final reciboRepo = GetIt.instance<ReciboRepository>();
      final recibo = Recibo(
        id: reciboId,
        folio: Recibo.generarFolio(),
        alumnoId: widget.alumno.id,
        alumnoNombre: widget.alumno.nombre,
        fechaEmision: DateTime.now(),
        montoTotal: montoTotal,
        metodoPago: metodoPago,
        concepto: concepto,
        detalles: detalles,
      );
      await reciboRepo.crearRecibo(recibo.toMap());

      // Update kardex semanas
      await _updateKardexSemanas(distribution, reciboId, kardexRepo);

      // Update additional costs
      await _updateCostosAdicionales(distribution, reciboId, kardexRepo);

      // Update main kardex
      await _updateMainKardex(montoTotal, kardexRepo);

      debugPrint('Receipt created successfully');

      // Mostrar mensaje de éxito
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Recibo generado exitosamente'),
            backgroundColor: Colors.green,
            duration: Duration(seconds: 3),
          ),
        );

        // Cambiar al tab de información en lugar de navegar de vuelta
        widget.onReciboCreated?.call();
      }

    } catch (e) {
      debugPrint('Error generating receipt: $e');
      debugPrint('Stack trace: ${StackTrace.current}');

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error al generar recibo: ${e.toString()}'),
            backgroundColor: Colors.red,
            duration: Duration(seconds: 5),
          ),
        );
      }
      rethrow;
    }
  }

  Future<void> _updateKardexSemanas(
    PaymentDistribution distribution,
    String reciboId,
    KardexRepository kardexRepo,
  ) async {
    for (final weekDist in distribution.weekDistributions) {
      final semanaId = weekDist.numeroSemana.toString();
      KardexSemana? semana;

      try {
        semana = await kardexRepo.getKardexSemanaById(widget.alumno.id, semanaId);
      } catch (e) {
        // Not found, we'll create a new one
      }

      if (semana != null) {
        // Update existing semana
        final newMontoPagado = semana.montoPagado + weekDist.amount;
        final isPaid = newMontoPagado >= semana.monto;

        final updatedSemana = semana.copyWith(
          montoPagado: newMontoPagado,
          estado: isPaid ? 'pagado' : 'pendiente',
          pagada: isPaid,
          updatedAt: DateTime.now(),
          reciboIds: [...semana.reciboIds, reciboId],
        );

        await kardexRepo.updateKardexSemana(updatedSemana);
      } else {
        // Create new semana
        final montoPorSemana = _montoPorSemana > 0 ? _montoPorSemana : weekDist.weeklyAmount;
        final isPaid = weekDist.amount >= montoPorSemana;
        semana = KardexSemana(
          reciboIds: [reciboId],
          id: semanaId,
          kardexId: widget.alumno.id,
          numeroSemana: weekDist.numeroSemana,
          monto: montoPorSemana,
          montoPagado: weekDist.amount,
          estado: isPaid ? 'pagado' : 'pendiente',
          pagada: isPaid,
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
        );
        await kardexRepo.updateKardexSemana(semana);
      }
    }
  }

  Future<void> _updateCostosAdicionales(
    PaymentDistribution distribution,
    String reciboId,
    KardexRepository kardexRepo,
  ) async {
    debugPrint('Updating ${distribution.costDistributions.length} additional costs');

    for (final costDist in distribution.costDistributions) {
      final costo = costDist.costoAdicional;
      final nuevoMontoPagado = (costo.montoPagado + costDist.amount).clamp(0, costo.monto);
      final estaPagado = nuevoMontoPagado >= costo.monto;

      debugPrint('Updating cost: ${costo.nombre}');
      debugPrint('  Previous paid: ${costo.montoPagado}');
      debugPrint('  Payment amount: ${costDist.amount}');
      debugPrint('  New paid amount: $nuevoMontoPagado');
      debugPrint('  Is fully paid: $estaPagado');

      try {
        final updatedCosto = CostoAdicional(
          id: costo.id,
          kardexId: costo.kardexId,
          nombre: costo.nombre,
          descripcion: costo.descripcion,
          monto: costo.monto,
          montoPagado: nuevoMontoPagado.toDouble(),
          pagado: estaPagado,
          reciboId: reciboId,
          fechaVencimiento: costo.fechaVencimiento,
          createdAt: costo.createdAt,
          updatedAt: DateTime.now(),
        );

        await kardexRepo.actualizarCostoAdicional(updatedCosto);
        debugPrint('Successfully updated cost: ${costo.nombre}');
      } catch (e) {
        debugPrint('Error updating cost ${costo.nombre}: $e');
        rethrow;
      }
    }

    debugPrint('Finished updating additional costs');
  }

  Future<void> _updateMainKardex(double montoTotal, KardexRepository kardexRepo) async {
    try {
      final kardex = await kardexRepo.getKardexByAlumnoId(widget.alumno.id);
      final nuevoTotalPagado = kardex.totalPagado + montoTotal;
      final nuevoSaldoPendiente = kardex.saldoPendiente - montoTotal;

      await kardexRepo.updateKardex(
        kardex.copyWith(
          totalPagado: nuevoTotalPagado,
          saldoPendiente: nuevoSaldoPendiente,
          updatedAt: DateTime.now(),
        ),
      );
    } catch (e) {
      debugPrint('Error updating kardex: $e');
      rethrow;
    }
  }

  @override
  Widget build(BuildContext context) {
    if (_isLoading) {
      return const Scaffold(
        body: Center(
          child: CircularProgressIndicator(),
        ),
      );
    }

    return Scaffold(
      body: Container(
        padding: const EdgeInsets.all(16),
        child: Column(
          children: [
            // Header con información del alumno
            _buildStudentHeader(),

            const SizedBox(height: 24),

            // Formulario de pago mejorado
            Expanded(
              child: _buildEnhancedPaymentForm(),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStudentHeader() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        children: [
          Container(
            width: 60,
            height: 60,
            decoration: BoxDecoration(
              color: AppStyles.primaryColor.withOpacity(0.1),
              borderRadius: BorderRadius.circular(30),
            ),
            child: Icon(
              Icons.person,
              color: AppStyles.primaryColor,
              size: 30,
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  widget.alumno.nombre,
                  style: GoogleFonts.poppins(
                    fontSize: 18,
                    fontWeight: FontWeight.w600,
                    color: AppStyles.textColor,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  'Nuevo Recibo de Pago',
                  style: GoogleFonts.poppins(
                    fontSize: 14,
                    color: AppStyles.secondaryTextColor,
                  ),
                ),
              ],
            ),
          ),
          Icon(
            Icons.receipt_long,
            color: AppStyles.primaryColor,
            size: 28,
          ),
        ],
      ),
    );
  }

  Widget _buildEnhancedPaymentForm() {
    return InlinePaymentForm(
      alumno: widget.alumno,
      costosAdicionales: widget.costosAdicionales,
      semanas: _semanas,
      montoPorSemana: _montoPorSemana,
      onGenerarRecibo: _onGenerarRecibo,
    );
  }

  Widget _buildPaymentStatus() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Estado de Pagos',
            style: GoogleFonts.poppins(
              fontSize: 18,
              fontWeight: FontWeight.w600,
              color: AppStyles.textColor,
            ),
          ),
          const SizedBox(height: 16),

          // Resumen de semanas
          _buildWeeksSummary(),

          const SizedBox(height: 20),

          // Resumen de costos adicionales
          _buildCostsSummary(),
        ],
      ),
    );
  }

  Widget _buildWeeksSummary() {
    final semanasPagadas = _semanas.where((s) => s.pagada).length;
    final semanasTotal = _semanas.length;

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.blue[50],
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.blue[200]!),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(Icons.calendar_today, color: Colors.blue[600], size: 20),
              const SizedBox(width: 8),
              Text(
                'Semanas',
                style: GoogleFonts.poppins(
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                  color: Colors.blue[700],
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Text(
            '$semanasPagadas de $semanasTotal semanas pagadas',
            style: GoogleFonts.poppins(
              fontSize: 14,
              color: AppStyles.textColor,
            ),
          ),
          if (_montoPorSemana > 0) ...[
            const SizedBox(height: 4),
            Text(
              'Costo por semana: \$${_montoPorSemana.toStringAsFixed(2)}',
              style: GoogleFonts.poppins(
                fontSize: 12,
                color: AppStyles.secondaryTextColor,
              ),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildCostsSummary() {
    final costosPagados = widget.costosAdicionales.where((c) => c.pagado).length;
    final costosTotal = widget.costosAdicionales.length;

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.orange[50],
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.orange[200]!),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(Icons.add_circle_outline, color: Colors.orange[600], size: 20),
              const SizedBox(width: 8),
              Text(
                'Costos Adicionales',
                style: GoogleFonts.poppins(
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                  color: Colors.orange[700],
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Text(
            '$costosPagados de $costosTotal costos pagados',
            style: GoogleFonts.poppins(
              fontSize: 14,
              color: AppStyles.textColor,
            ),
          ),
        ],
      ),
    );
  }
}
