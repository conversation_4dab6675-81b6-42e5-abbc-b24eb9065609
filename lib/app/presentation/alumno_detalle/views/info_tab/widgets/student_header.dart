import 'package:flutter/material.dart';
import 'package:gemini_app/app/domain/entities/alumno.dart';
import 'package:gemini_app/app/presentation/alumno_detalle/alumno_detalle.styles.dart';

class StudentHeader extends StatelessWidget {
  final Alumno alumno;
  
  const StudentHeader({
    Key? key,
    required this.alumno,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(24),
      decoration: DetalleAlumnoStyles.headerDecoration(context),
      child: Row(
        children: [
          // Avatar
          Container(
            width: 72,
            height: 72,
            decoration: DetalleAlumnoStyles.avatarDecoration(alumno.nombre),
            child: Center(
              child: Text(
                DetalleAlumnoStyles.getInitials(alumno.nombre),
                style: DetalleAlumnoStyles.avatarTextStyle(),
              ),
            ),
          ),
          const SizedBox(width: 20),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  '${alumno.nombre} ${alumno.apellidoPaterno}',
                  style: DetalleAlumnoStyles.studentNameStyle(context),
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                ),
                const SizedBox(height: 4),
                Text(
                  'ID: ${alumno.id}',
                  style: DetalleAlumnoStyles.studentIdStyle(context),
                ),
                const SizedBox(height: 8),
                Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 12,
                    vertical: 6,
                  ),
                  decoration: BoxDecoration(
                    color: Colors.green.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(20),
                  ),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Icon(
                        Icons.check_circle_outline,
                        size: 16,
                        color: Colors.green,
                      ),
                      const SizedBox(width: 6),
                      Text('Activo', style: DetalleAlumnoStyles.statusStyle()),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}