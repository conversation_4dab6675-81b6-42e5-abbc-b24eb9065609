import 'package:flutter/material.dart';
import 'package:gemini_app/app/presentation/alumno_detalle/alumno_detalle.styles.dart';

class InfoItem extends StatelessWidget {
  final String label;
  final String value;
  final IconData icon;
  
  const InfoItem({
    Key? key,
    required this.label,
    required this.value,
    required this.icon,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 16),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Icon(icon, size: 20, color: Theme.of(context).primaryColor),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(label, style: DetalleAlumnoStyles.infoLabelStyle(context)),
                const SizedBox(height: 2),
                Text(value, style: DetalleAlumnoStyles.infoValueStyle(context)),
              ],
            ),
          ),
        ],
      ),
    );
  }
}