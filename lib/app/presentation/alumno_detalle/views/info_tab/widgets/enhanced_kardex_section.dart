import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:gemini_app/app/domain/modelos/kardex.dart';
import '../../../../../styles/app_styles.dart';

class EnhancedKardexSection extends StatelessWidget {
  final Kardex kardex;
  final List<KardexSemana> semanas;

  const EnhancedKardexSection({
    Key? key,
    required this.kardex,
    required this.semanas,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final paidWeeks = semanas.where((s) => s.pagada).length;
    final pendingWeeks = semanas.where((s) => !s.pagada).length;
    final partialWeeks = semanas.where((s) => s.montoPagado > 0 && !s.pagada).length;
    final progress = kardex.totalSemanasCurso > 0
        ? (paidWeeks / kardex.totalSemanasCurso).clamp(0.0, 1.0)
        : 0.0;

    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            Colors.purple[50]!,
            Colors.indigo[50]!,
          ],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: Colors.purple[100]!),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(10),
                decoration: BoxDecoration(
                  color: Colors.purple[100],
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Icon(
                  Icons.school_outlined,
                  color: Colors.purple[700],
                  size: 24,
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Progreso del Curso',
                      style: GoogleFonts.poppins(
                        fontSize: 16,
                        fontWeight: FontWeight.w700,
                        color: AppStyles.textColor,
                      ),
                    ),
                    Text(
                      kardex.cursoNombre,
                      style: GoogleFonts.poppins(
                        fontSize: 12,
                        color: AppStyles.secondaryTextColor,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ],
                ),
              ),
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                decoration: BoxDecoration(
                  color: progress >= 1.0 ? Colors.green[100] : Colors.purple[100],
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Text(
                  '${(progress * 100).toStringAsFixed(0)}%',
                  style: GoogleFonts.poppins(
                    fontSize: 12,
                    fontWeight: FontWeight.w700,
                    color: progress >= 1.0 ? Colors.green[700] : Colors.purple[700],
                  ),
                ),
              ),
            ],
          ),

          const SizedBox(height: 20),

          // Progress Bar
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    'Progreso de Semanas',
                    style: GoogleFonts.poppins(
                      fontSize: 13,
                      fontWeight: FontWeight.w600,
                      color: AppStyles.textColor,
                    ),
                  ),
                  Text(
                    '$paidWeeks / ${kardex.totalSemanasCurso} semanas',
                    style: GoogleFonts.poppins(
                      fontSize: 12,
                      fontWeight: FontWeight.w600,
                      color: AppStyles.secondaryTextColor,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 8),
              Container(
                height: 8,
                decoration: BoxDecoration(
                  color: Colors.grey[200],
                  borderRadius: BorderRadius.circular(4),
                ),
                child: FractionallySizedBox(
                  alignment: Alignment.centerLeft,
                  widthFactor: progress,
                  child: Container(
                    decoration: BoxDecoration(
                      gradient: LinearGradient(
                        colors: progress >= 1.0
                            ? [Colors.green[400]!, Colors.green[600]!]
                            : [Colors.purple[400]!, Colors.indigo[600]!],
                      ),
                      borderRadius: BorderRadius.circular(4),
                    ),
                  ),
                ),
              ),
            ],
          ),

          const SizedBox(height: 20),

          // Week Status Cards
          Row(
            children: [
              // Paid Weeks
              Expanded(
                child: _buildStatusCard(
                  title: 'Pagadas',
                  count: paidWeeks,
                  icon: Icons.check_circle,
                  color: Colors.green,
                ),
              ),
              const SizedBox(width: 8),
              // Partial Weeks
              Expanded(
                child: _buildStatusCard(
                  title: 'Parciales',
                  count: partialWeeks,
                  icon: Icons.schedule,
                  color: Colors.orange,
                ),
              ),
              const SizedBox(width: 8),
              // Pending Weeks
              Expanded(
                child: _buildStatusCard(
                  title: 'Pendientes',
                  count: pendingWeeks,
                  icon: Icons.pending,
                  color: Colors.red,
                ),
              ),
            ],
          ),

          if (semanas.isNotEmpty) ...[
            const SizedBox(height: 16),

            // Recent Weeks
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.white.withOpacity(0.7),
                borderRadius: BorderRadius.circular(12),
                border: Border.all(color: Colors.purple[100]!),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Icon(
                        Icons.history,
                        size: 16,
                        color: Colors.purple[700],
                      ),
                      const SizedBox(width: 8),
                      Text(
                        'Últimas Semanas',
                        style: GoogleFonts.poppins(
                          fontSize: 11,
                          color: AppStyles.secondaryTextColor,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 8),

                  // Week List (simplified)
                  ...semanas.take(5).map((semana) => _buildSimpleWeekItem(semana)),

                  if (semanas.length > 5) ...[
                    const SizedBox(height: 4),
                    Center(
                      child: Text(
                        'y ${semanas.length - 5} semanas más...',
                        style: GoogleFonts.poppins(
                          fontSize: 10,
                          color: AppStyles.secondaryTextColor,
                          fontStyle: FontStyle.italic,
                        ),
                      ),
                    ),
                  ],
                ],
              ),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildStatusCard({
    required String title,
    required int count,
    required IconData icon,
    required Color color,
  }) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: color.withOpacity(0.2)),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.02),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        children: [
          Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: color.withOpacity(0.1),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Icon(
              icon,
              size: 16,
              color: color,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            count.toString(),
            style: GoogleFonts.poppins(
              fontSize: 18,
              fontWeight: FontWeight.w700,
              color: color,
            ),
          ),
          Text(
            title,
            style: GoogleFonts.poppins(
              fontSize: 10,
              color: AppStyles.secondaryTextColor,
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildWeekItem(KardexSemana semana) {
    Color statusColor;
    IconData statusIcon;
    String statusText;

    if (semana.pagada) {
      statusColor = Colors.green;
      statusIcon = Icons.check_circle;
      statusText = 'Pagada';
    } else if (semana.montoPagado > 0) {
      statusColor = Colors.orange;
      statusIcon = Icons.schedule;
      statusText = 'Parcial';
    } else {
      statusColor = Colors.red;
      statusIcon = Icons.pending;
      statusText = 'Pendiente';
    }

    return Container(
      margin: const EdgeInsets.only(bottom: 6),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: statusColor.withOpacity(0.05),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: statusColor.withOpacity(0.2)),
      ),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(4),
            decoration: BoxDecoration(
              color: statusColor.withOpacity(0.1),
              borderRadius: BorderRadius.circular(6),
            ),
            child: Icon(
              statusIcon,
              size: 12,
              color: statusColor,
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Semana ${semana.numeroSemana}',
                  style: GoogleFonts.poppins(
                    fontSize: 13,
                    fontWeight: FontWeight.w600,
                    color: AppStyles.textColor,
                  ),
                ),
                if (semana.montoPagado > 0)
                  Text(
                    '\$${semana.montoPagado.toStringAsFixed(2)} / \$${semana.monto.toStringAsFixed(2)}',
                    style: GoogleFonts.poppins(
                      fontSize: 11,
                      color: AppStyles.secondaryTextColor,
                    ),
                  ),
              ],
            ),
          ),
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
            decoration: BoxDecoration(
              color: statusColor.withOpacity(0.1),
              borderRadius: BorderRadius.circular(10),
            ),
            child: Text(
              statusText,
              style: GoogleFonts.poppins(
                fontSize: 10,
                fontWeight: FontWeight.w600,
                color: statusColor,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSimpleWeekItem(KardexSemana semana) {
    Color statusColor;
    IconData statusIcon;
    String statusText;

    if (semana.pagada) {
      statusColor = Colors.green;
      statusIcon = Icons.check_circle;
      statusText = 'Pagada';
    } else if (semana.montoPagado > 0) {
      statusColor = Colors.orange;
      statusIcon = Icons.schedule;
      statusText = 'Parcial';
    } else {
      statusColor = Colors.red;
      statusIcon = Icons.pending;
      statusText = 'Pendiente';
    }

    return Container(
      margin: const EdgeInsets.only(bottom: 4),
      padding: const EdgeInsets.all(8),
      decoration: BoxDecoration(
        color: statusColor.withOpacity(0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: statusColor.withOpacity(0.2)),
      ),
      child: Row(
        children: [
          Icon(
            statusIcon,
            size: 12,
            color: statusColor,
          ),
          const SizedBox(width: 8),
          Expanded(
            child: Text(
              'Semana ${semana.numeroSemana}',
              style: GoogleFonts.poppins(
                fontSize: 11,
                fontWeight: FontWeight.w600,
                color: AppStyles.textColor,
              ),
            ),
          ),
          if (semana.montoPagado > 0)
            Text(
              '\$${semana.montoPagado.toStringAsFixed(2)}',
              style: GoogleFonts.poppins(
                fontSize: 11,
                fontWeight: FontWeight.w700,
                color: statusColor,
              ),
            ),
          const SizedBox(width: 8),
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
            decoration: BoxDecoration(
              color: statusColor.withOpacity(0.1),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Text(
              statusText,
              style: GoogleFonts.poppins(
                fontSize: 9,
                fontWeight: FontWeight.w600,
                color: statusColor,
              ),
            ),
          ),
        ],
      ),
    );
  }
}
