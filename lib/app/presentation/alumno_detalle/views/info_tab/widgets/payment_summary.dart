import 'package:flutter/material.dart';
import 'package:gemini_app/app/presentation/alumno_detalle/alumno_detalle.styles.dart';

class PaymentSummary extends StatelessWidget {
  final double totalPagado;
  final double totalPendiente;
  
  const PaymentSummary({
    Key? key,
    required this.totalPagado,
    required this.totalPendiente,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final total = totalPagado + totalPendiente;
    final progress = total > 0 ? (totalPagado / total) : 0;

    return Container(
      padding: const EdgeInsets.all(24),
      decoration: DetalleAlumnoStyles.paymentSummaryDecoration(context),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Resumen Financiero',
            style: DetalleAlumnoStyles.sectionTitleStyle(context),
          ),
          const SizedBox(height: 16),
          ClipRRect(
            borderRadius: BorderRadius.circular(8),
            child: LinearProgressIndicator(
              value: progress.toDouble(),
              minHeight: 10,
              backgroundColor: Theme.of(context).colorScheme.surfaceVariant,
              valueColor: AlwaysStoppedAnimation<Color>(
                Theme.of(context).primaryColor,
              ),
            ),
          ),
          const SizedBox(height: 16),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              _buildPaymentSummaryItem(
                label: 'Pagado',
                value: totalPagado,
                color: Colors.green,
                context: context,
              ),
              _buildPaymentSummaryItem(
                label: 'Pendiente',
                value: totalPendiente,
                color: Colors.orange,
                context: context,
              ),
              _buildPaymentSummaryItem(
                label: 'Total',
                value: total,
                color: Theme.of(context).colorScheme.onSurface,
                context: context,
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildPaymentSummaryItem({
    required String label,
    required double value,
    required Color color,
    required BuildContext context,
  }) {
    return Column(
      children: [
        Text(label, style: DetalleAlumnoStyles.infoLabelStyle(context)),
        const SizedBox(height: 4),
        Text(
          '\$${value.toStringAsFixed(2)}',
          style: DetalleAlumnoStyles.infoValueStyle(
            context,
          ).copyWith(fontWeight: FontWeight.bold, color: color),
        ),
      ],
    );
  }
}