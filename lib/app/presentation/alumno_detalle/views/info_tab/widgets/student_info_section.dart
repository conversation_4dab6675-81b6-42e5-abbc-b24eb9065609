import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:gemini_app/app/domain/entities/alumno.dart';
import '../../../../../styles/app_styles.dart';

class StudentInfoSection extends StatelessWidget {
  final Alumno alumno;

  const StudentInfoSection({
    Key? key,
    required this.alumno,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Contact Information Cards (sin el header del estudiante)
        Row(
          children: [
            // Email Card
            Expanded(
              child: _buildContactCard(
                icon: Icons.email_outlined,
                label: 'Email',
                value: alumno.email ?? 'No especificado',
                color: Colors.blue,
                isEmpty: alumno.email == null || alumno.email!.isEmpty,
              ),
            ),
            const SizedBox(width: 12),
            // Phone Card
            Expanded(
              child: _buildContactCard(
                icon: Icons.phone_outlined,
                label: 'Teléfono',
                value: alumno.telefono ?? 'No especificado',
                color: Colors.green,
                isEmpty: alumno.telefono == null || alumno.telefono!.isEmpty,
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildContactCard({
    required IconData icon,
    required String label,
    required String value,
    required Color color,
    required bool isEmpty,
  }) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: isEmpty ? Colors.grey[300]! : color.withOpacity(0.2),
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.02),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: isEmpty ? Colors.grey[100] : color.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Icon(
                  icon,
                  size: 16,
                  color: isEmpty ? Colors.grey[400] : color,
                ),
              ),
              const SizedBox(width: 8),
              Text(
                label,
                style: GoogleFonts.poppins(
                  fontSize: 12,
                  color: AppStyles.secondaryTextColor,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Text(
            value,
            style: GoogleFonts.poppins(
              fontSize: 13,
              color: isEmpty ? Colors.grey[500] : AppStyles.textColor,
              fontWeight: FontWeight.w600,
            ),
            maxLines: 1,
            overflow: TextOverflow.ellipsis,
          ),
        ],
      ),
    );
  }
}
