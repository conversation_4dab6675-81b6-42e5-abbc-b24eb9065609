import 'package:flutter/material.dart';
import 'package:gemini_app/app/presentation/alumno_detalle/alumno_detalle.styles.dart';
import 'package:google_fonts/google_fonts.dart';
import '../../../../../domain/modelos/kardex.dart' as models;
import 'package:gemini_app/app/presentation/alumno_detalle/views/pagos_tab/widgets/crear_recibo/week_chip.dart';

class KardexSection extends StatelessWidget {
  final models.Kardex? kardex;
  final List<models.KardexSemana> semanas;

  const KardexSection({
    Key? key,
    required this.kardex,
    required this.semanas,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    if (kardex == null) {
      return Container(); // Will be handled by the main widget
    }

    return Column(
      children: [
        _buildKardexProgress(context),
        const SizedBox(height: 24),
        _buildKardexStats(context),
        if (semanas.isNotEmpty) ...[
          const SizedBox(height: 24),
          _buildPaidWeeks(context, semanas),
        ],
      ],
    );
  }

  Widget _buildKardexProgress(BuildContext context) {
    final progress = kardex!.montoTotalCurso > 0
        ? (kardex!.totalPagado / kardex!.montoTotalCurso).clamp(0.0, 1.0)
        : 0.0;

    return Column(
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              'Progreso de Pago',
              style: DetalleAlumnoStyles.infoLabelStyle(context).copyWith(
                color: Theme.of(context).colorScheme.onSurface.withOpacity(0.8),
              ),
            ),
            Text(
              '${(progress * 100).toStringAsFixed(1)}%',
              style: DetalleAlumnoStyles.progressValueStyle(context),
            ),
          ],
        ),
        const SizedBox(height: 8),
        ClipRRect(
          borderRadius: BorderRadius.circular(8),
          child: LinearProgressIndicator(
            value: progress,
            minHeight: 10,
            backgroundColor: Theme.of(context).colorScheme.surfaceVariant,
            valueColor: AlwaysStoppedAnimation<Color>(
              Theme.of(context).primaryColor,
            ),
          ),
        ),
        const SizedBox(height: 8),
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              '\$${kardex!.totalPagado.toStringAsFixed(2)} pagado',
              style: DetalleAlumnoStyles.infoLabelStyle(context),
            ),
            Text(
              '\$${kardex!.saldoPendiente.toStringAsFixed(2)} pendiente',
              style: DetalleAlumnoStyles.infoLabelStyle(context),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildKardexStats(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: DetalleAlumnoStyles.statsCardDecoration(context),
      child: Column(
        children: [
          _buildStatItem(
            label: 'Curso',
            value: kardex!.cursoNombre,
            icon: Icons.book_outlined,
            context: context,
          ),
          _buildStatItem(
            label: 'Total Semanas',
            value: '${kardex!.totalSemanasCurso} semanas',
            icon: Icons.calendar_today_outlined,
            context: context,
          ),
          _buildStatItem(
            label: 'Monto por Semana',
            value: '\$${kardex!.montoPorSemana.toStringAsFixed(2)}',
            icon: Icons.attach_money_outlined,
            context: context,
          ),
        ],
      ),
    );
  }

  Widget _buildStatItem({
    required String label,
    required String value,
    required IconData icon,
    required BuildContext context,
  }) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 12),
      child: Row(
        children: [
          Icon(
            icon,
            size: 18,
            color: Theme.of(context).primaryColor.withOpacity(0.7),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(label, style: DetalleAlumnoStyles.infoLabelStyle(context)),
                const SizedBox(height: 2),
                Text(value, style: DetalleAlumnoStyles.infoValueStyle(context)),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPaidWeeks(BuildContext context, List<models.KardexSemana> semanasPagadas) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Semanas Pagadas (${semanasPagadas.length})',
          style: DetalleAlumnoStyles.infoLabelStyle(context).copyWith(
            fontWeight: FontWeight.w500,
            color: Theme.of(context).colorScheme.onSurface.withOpacity(0.8),
          ),
        ),
        const SizedBox(height: 12),
        Wrap(
          spacing: 8,
          runSpacing: 8,
          children: semanasPagadas
              .map((semana) => WeekChip(
                    weekNumber: semana.numeroSemana,
                    amountPaid: semana.montoPagado,
                    totalAmount: semana.monto,
                    isPending: semana.estado != 'pagado',
                    primaryColor: Theme.of(context).primaryColor,
                    successColor: Colors.green,
                    warningColor: Colors.orange,
                  ))
              .toList(),
        ),
      ],
    );
  }
}
