import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:gemini_app/app/domain/modelos/costo_adicional.dart';
import '../../../../../styles/app_styles.dart';

class AdditionalCostsSection extends StatelessWidget {
  final List<CostoAdicional> costosAdicionales;

  const AdditionalCostsSection({
    Key? key,
    required this.costosAdicionales,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    if (costosAdicionales.isEmpty) {
      return Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: Colors.grey[50],
          borderRadius: BorderRadius.circular(12),
          border: Border.all(color: Colors.grey[200]!),
        ),
        child: Row(
          children: [
            Icon(
              Icons.info_outline,
              color: Colors.grey[600],
              size: 20,
            ),
            const SizedBox(width: 8),
            Text(
              'No hay costos adicionales registrados',
              style: GoogleFonts.poppins(
                fontSize: 14,
                color: Colors.grey[600],
                fontWeight: FontWeight.w400,
              ),
            ),
          ],
        ),
      );
    }

    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            Colors.orange[50]!,
            Colors.red[50]!,
          ],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: Colors.orange[100]!),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(10),
                decoration: BoxDecoration(
                  color: Colors.orange[100],
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Icon(
                  Icons.receipt_long_outlined,
                  color: Colors.orange[700],
                  size: 24,
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Costos Adicionales',
                      style: GoogleFonts.poppins(
                        fontSize: 16,
                        fontWeight: FontWeight.w700,
                        color: AppStyles.textColor,
                      ),
                    ),
                    Text(
                      '${costosAdicionales.length} concepto${costosAdicionales.length != 1 ? 's' : ''} registrado${costosAdicionales.length != 1 ? 's' : ''}',
                      style: GoogleFonts.poppins(
                        fontSize: 12,
                        color: AppStyles.secondaryTextColor,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ],
                ),
              ),
              _buildSummaryBadge(),
            ],
          ),

          const SizedBox(height: 20),

          // Summary Cards Row
          _buildSummaryCards(),

          if (costosAdicionales.isNotEmpty) ...[
            const SizedBox(height: 16),

            // Recent Costs
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.white.withOpacity(0.7),
                borderRadius: BorderRadius.circular(12),
                border: Border.all(color: Colors.orange[100]!),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Icon(
                        Icons.list_alt,
                        size: 16,
                        color: Colors.orange[700],
                      ),
                      const SizedBox(width: 8),
                      Text(
                        'Detalle de Costos',
                        style: GoogleFonts.poppins(
                          fontSize: 11,
                          color: AppStyles.secondaryTextColor,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 8),

                  // Cost List (simplified)
                  ...costosAdicionales.take(3).map((costo) => _buildSimpleCostItem(costo)),

                  if (costosAdicionales.length > 3) ...[
                    const SizedBox(height: 4),
                    Center(
                      child: Text(
                        'y ${costosAdicionales.length - 3} costos más...',
                        style: GoogleFonts.poppins(
                          fontSize: 10,
                          color: AppStyles.secondaryTextColor,
                          fontStyle: FontStyle.italic,
                        ),
                      ),
                    ),
                  ],
                ],
              ),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildSummaryBadge() {
    final totalAmount = costosAdicionales.fold<double>(
      0.0,
      (sum, costo) => sum + costo.monto,
    );
    final totalPending = costosAdicionales.fold<double>(
      0.0,
      (sum, costo) => sum + (costo.monto - costo.montoPagado),
    );

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: totalPending > 0 ? Colors.orange[100] : Colors.green[100],
        borderRadius: BorderRadius.circular(12),
      ),
      child: Text(
        '\$${totalAmount.toStringAsFixed(2)}',
        style: GoogleFonts.poppins(
          fontSize: 12,
          fontWeight: FontWeight.w700,
          color: totalPending > 0 ? Colors.orange[700] : Colors.green[700],
        ),
      ),
    );
  }

  Widget _buildSummaryCards() {
    final totalAmount = costosAdicionales.fold<double>(
      0.0,
      (sum, costo) => sum + costo.monto,
    );
    final totalPaid = costosAdicionales.fold<double>(
      0.0,
      (sum, costo) => sum + costo.montoPagado,
    );
    final totalPending = totalAmount - totalPaid;

    return Row(
      children: [
        // Total Amount
        Expanded(
          child: _buildAmountCard(
            title: 'Total',
            amount: totalAmount,
            icon: Icons.receipt_outlined,
            color: Colors.orange,
            isPositive: true,
          ),
        ),
        const SizedBox(width: 12),
        // Pending Amount
        Expanded(
          child: _buildAmountCard(
            title: 'Pendiente',
            amount: totalPending,
            icon: Icons.schedule_outlined,
            color: totalPending > 0 ? Colors.red : Colors.green,
            isPositive: totalPending <= 0,
          ),
        ),
      ],
    );
  }

  Widget _buildAmountCard({
    required String title,
    required double amount,
    required IconData icon,
    required Color color,
    required bool isPositive,
  }) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: color.withOpacity(0.2)),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.02),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(6),
                decoration: BoxDecoration(
                  color: color.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Icon(
                  icon,
                  size: 14,
                  color: color,
                ),
              ),
              const SizedBox(width: 6),
              Expanded(
                child: Text(
                  title,
                  style: GoogleFonts.poppins(
                    fontSize: 10,
                    color: AppStyles.secondaryTextColor,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Text(
            '\$${amount.toStringAsFixed(2)}',
            style: GoogleFonts.poppins(
              fontSize: 16,
              fontWeight: FontWeight.w700,
              color: color,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSimpleCostItem(CostoAdicional costo) {
    final pendingAmount = costo.monto - costo.montoPagado;
    final isPaid = costo.pagado;

    return Container(
      margin: const EdgeInsets.only(bottom: 4),
      padding: const EdgeInsets.all(8),
      decoration: BoxDecoration(
        color: isPaid ? Colors.green[50] : Colors.orange[50],
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: isPaid ? Colors.green[200]! : Colors.orange[200]!,
        ),
      ),
      child: Row(
        children: [
          Icon(
            isPaid ? Icons.check_circle : Icons.schedule,
            size: 12,
            color: isPaid ? Colors.green[700] : Colors.orange[700],
          ),
          const SizedBox(width: 8),
          Expanded(
            child: Text(
              costo.nombre,
              style: GoogleFonts.poppins(
                fontSize: 11,
                fontWeight: FontWeight.w600,
                color: AppStyles.textColor,
              ),
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
            ),
          ),
          Text(
            '\$${(isPaid ? costo.monto : pendingAmount).toStringAsFixed(2)}',
            style: GoogleFonts.poppins(
              fontSize: 11,
              fontWeight: FontWeight.w700,
              color: isPaid ? Colors.green[700] : Colors.orange[700],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildCostItem(CostoAdicional costo) {
    final pendingAmount = costo.monto - costo.montoPagado;
    final isPaid = costo.pagado;
    final isPartiallyPaid = costo.montoPagado > 0 && !isPaid;

    return Container(
      margin: const EdgeInsets.only(bottom: 8),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: isPaid
              ? Colors.green[200]!
              : isPartiallyPaid
                  ? Colors.orange[200]!
                  : Colors.grey[200]!,
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.02),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header row
          Row(
            children: [
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      costo.nombre,
                      style: GoogleFonts.poppins(
                        fontSize: 14,
                        fontWeight: FontWeight.w600,
                        color: AppStyles.textColor,
                      ),
                    ),
                    if (costo.descripcion.isNotEmpty) ...[
                      const SizedBox(height: 4),
                      Text(
                        costo.descripcion,
                        style: GoogleFonts.poppins(
                          fontSize: 12,
                          color: AppStyles.secondaryTextColor,
                          fontWeight: FontWeight.w400,
                        ),
                      ),
                    ],
                  ],
                ),
              ),
              _buildStatusChip(isPaid, isPartiallyPaid),
            ],
          ),
          
          const SizedBox(height: 12),
          
          // Amount details
          Row(
            children: [
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    _buildAmountRow(
                      'Total:',
                      costo.monto,
                      AppStyles.textColor,
                    ),
                    if (costo.montoPagado > 0)
                      _buildAmountRow(
                        'Pagado:',
                        costo.montoPagado,
                        Colors.green[700]!,
                      ),
                    if (pendingAmount > 0)
                      _buildAmountRow(
                        'Pendiente:',
                        pendingAmount,
                        Colors.orange[700]!,
                      ),
                  ],
                ),
              ),
              if (pendingAmount > 0)
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                  decoration: BoxDecoration(
                    color: Colors.orange[50],
                    borderRadius: BorderRadius.circular(20),
                    border: Border.all(color: Colors.orange[200]!),
                  ),
                  child: Text(
                    '\$${pendingAmount.toStringAsFixed(2)}',
                    style: GoogleFonts.poppins(
                      fontSize: 12,
                      fontWeight: FontWeight.w600,
                      color: Colors.orange[700],
                    ),
                  ),
                ),
            ],
          ),
          
          // Due date if exists
          if (costo.fechaVencimiento.isAfter(DateTime.now()))
            Padding(
              padding: const EdgeInsets.only(top: 8),
              child: Row(
                children: [
                  Icon(
                    Icons.schedule_outlined,
                    size: 14,
                    color: AppStyles.secondaryTextColor,
                  ),
                  const SizedBox(width: 4),
                  Text(
                    'Vence: ${_formatDate(costo.fechaVencimiento)}',
                    style: GoogleFonts.poppins(
                      fontSize: 11,
                      color: AppStyles.secondaryTextColor,
                      fontWeight: FontWeight.w400,
                    ),
                  ),
                ],
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildStatusChip(bool isPaid, bool isPartiallyPaid) {
    Color backgroundColor;
    Color textColor;
    String text;
    IconData icon;

    if (isPaid) {
      backgroundColor = Colors.green[50]!;
      textColor = Colors.green[700]!;
      text = 'Pagado';
      icon = Icons.check_circle_outline;
    } else if (isPartiallyPaid) {
      backgroundColor = Colors.orange[50]!;
      textColor = Colors.orange[700]!;
      text = 'Parcial';
      icon = Icons.schedule_outlined;
    } else {
      backgroundColor = Colors.red[50]!;
      textColor = Colors.red[700]!;
      text = 'Pendiente';
      icon = Icons.pending_outlined;
    }

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: backgroundColor,
        borderRadius: BorderRadius.circular(12),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(icon, size: 12, color: textColor),
          const SizedBox(width: 4),
          Text(
            text,
            style: GoogleFonts.poppins(
              fontSize: 10,
              fontWeight: FontWeight.w600,
              color: textColor,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildAmountRow(String label, double amount, Color color) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 2),
      child: Row(
        children: [
          Text(
            label,
            style: GoogleFonts.poppins(
              fontSize: 12,
              color: AppStyles.secondaryTextColor,
              fontWeight: FontWeight.w400,
            ),
          ),
          const SizedBox(width: 8),
          Text(
            '\$${amount.toStringAsFixed(2)}',
            style: GoogleFonts.poppins(
              fontSize: 12,
              fontWeight: FontWeight.w600,
              color: color,
            ),
          ),
        ],
      ),
    );
  }

  String _formatDate(DateTime date) {
    final months = [
      'Ene', 'Feb', 'Mar', 'Abr', 'May', 'Jun',
      'Jul', 'Ago', 'Sep', 'Oct', 'Nov', 'Dic'
    ];
    return '${date.day} ${months[date.month - 1]} ${date.year}';
  }
}
