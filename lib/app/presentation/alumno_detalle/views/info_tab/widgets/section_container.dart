import 'package:flutter/material.dart';
import 'package:gemini_app/app/presentation/alumno_detalle/alumno_detalle.styles.dart';

class SectionContainer extends StatelessWidget {
  final String title;
  final IconData icon;
  final Widget child;
  
  const SectionContainer({
    Key? key,
    required this.title,
    required this.icon,
    required this.child,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.all(24),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(icon, size: 20, color: Theme.of(context).primaryColor),
              const SizedBox(width: 12),
              Text(
                title,
                style: DetalleAlumnoStyles.sectionTitleStyle(context),
              ),
            ],
          ),
          const SizedBox(height: 16),
          child,
        ],
      ),
    );
  }
}