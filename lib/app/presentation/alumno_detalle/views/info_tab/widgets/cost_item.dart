import 'package:flutter/material.dart';
import 'package:gemini_app/app/presentation/alumno_detalle/alumno_detalle.styles.dart';
import '../../../../../domain/modelos/costo_adicional.dart';

class CostItem extends StatelessWidget {
  final CostoAdicional costo;

  const CostItem({
    Key? key,
    required this.costo,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final progress = costo.monto > 0
        ? (costo.montoPagado / costo.monto).clamp(0.0, 1.0)
        : 0.0;
    final remaining = (costo.monto - costo.montoPagado).clamp(0.0, costo.monto);

    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      padding: const EdgeInsets.all(16),
      decoration: DetalleAlumnoStyles.costItemDecoration(context),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                costo.nombre,
                style: DetalleAlumnoStyles.infoValueStyle(context),
              ),
              Text(
                '\$${costo.monto.toStringAsFixed(2)}',
                style: DetalleAlumnoStyles.infoValueStyle(
                  context,
                ).copyWith(fontWeight: FontWeight.bold),
              ),
            ],
          ),
          const SizedBox(height: 12),
          ClipRRect(
            borderRadius: BorderRadius.circular(8),
            child: LinearProgressIndicator(
              value: progress,
              minHeight: 6,
              backgroundColor: Theme.of(context).colorScheme.surfaceVariant,
              valueColor: AlwaysStoppedAnimation<Color>(
                Theme.of(context).primaryColor,
              ),
            ),
          ),
          const SizedBox(height: 8),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                '\$${costo.montoPagado.toStringAsFixed(2)} pagado',
                style: DetalleAlumnoStyles.infoLabelStyle(context),
              ),
              Text(
                '\$${remaining.toStringAsFixed(2)} restante',
                style: DetalleAlumnoStyles.infoLabelStyle(context),
              ),
              Text(
                '${(progress * 100).toStringAsFixed(0)}%',
                style: DetalleAlumnoStyles.progressValueStyle(context),
              ),
            ],
          ),
        ],
      ),
    );
  }
}