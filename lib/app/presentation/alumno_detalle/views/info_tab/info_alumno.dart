import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:gemini_app/app/domain/entities/alumno.dart';
import 'package:gemini_app/app/domain/modelos/kardex.dart';
import 'package:gemini_app/app/domain/modelos/costo_adicional.dart';
import 'package:gemini_app/app/presentation/alumno_detalle/views/info_tab/widgets/additional_costs_section.dart';
import 'package:gemini_app/app/presentation/alumno_detalle/views/info_tab/widgets/student_info_section.dart';
import 'package:gemini_app/app/presentation/alumno_detalle/views/info_tab/widgets/enhanced_payment_summary.dart';
import 'package:gemini_app/app/presentation/alumno_detalle/views/info_tab/widgets/enhanced_kardex_section.dart';
import '../../../../domain/casos_de_uso/watch_alumno_usecase.dart';
import '../../../../domain/casos_de_uso/watch_kardex_usecase.dart';
import '../../../../domain/casos_de_uso/watch_semanas_usecase.dart';
import '../../../../domain/casos_de_uso/watch_costos_adicionales_usecase.dart';

class StudentInfoCard extends StatelessWidget {
  final String alumnoId;

  const StudentInfoCard({Key? key, required this.alumnoId}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final watchAlumnoUseCase = Get.find<WatchAlumnoUseCase>();
    final watchKardexUseCase = Get.find<WatchKardexUseCase>();
    final watchSemanasUseCase = Get.find<WatchSemanasUseCase>();
    final watchCostosAdicionalesUseCase = Get.find<WatchCostosAdicionalesUseCase>();

    return StreamBuilder<Alumno>(
      stream: watchAlumnoUseCase.execute(alumnoId),
      builder: (context, alumnoSnapshot) {
        if (alumnoSnapshot.connectionState == ConnectionState.waiting) {
          return const Center(child: CircularProgressIndicator());
        }

        if (alumnoSnapshot.hasError) {
          return Center(
            child: Text('Error: ${alumnoSnapshot.error}'),
          );
        }

        if (!alumnoSnapshot.hasData) {
          return const Center(child: Text('No se encontró el alumno'));
        }

        final alumno = alumnoSnapshot.data!;

        return StreamBuilder<Kardex?>(
          stream: watchKardexUseCase.execute(alumnoId),
          builder: (context, kardexSnapshot) {
            final kardex = kardexSnapshot.data;

            return StreamBuilder<List<KardexSemana>>(
              stream: watchSemanasUseCase.execute(alumnoId),
              builder: (context, semanasSnapshot) {
                final semanas = semanasSnapshot.data ?? [];

                return StreamBuilder<List<CostoAdicional>>(
                  stream: watchCostosAdicionalesUseCase.execute(alumnoId),
                  builder: (context, costosSnapshot) {
                    final costosAdicionales = costosSnapshot.data ?? [];

                    return SingleChildScrollView(
                      padding: const EdgeInsets.all(16),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [

                          // Student Contact Information Section
                          StudentInfoSection(alumno: alumno),

                          const SizedBox(height: 16),

                          // Enhanced Kardex Section
                          if (kardex != null && semanas.isNotEmpty)
                            EnhancedKardexSection(
                              kardex: kardex,
                              semanas: semanas,
                            )
                          else
                            Container(
                              padding: const EdgeInsets.all(20),
                              decoration: BoxDecoration(
                                color: Colors.grey[50],
                                borderRadius: BorderRadius.circular(16),
                                border: Border.all(color: Colors.grey[200]!),
                              ),
                              child: const Center(
                                child: Text(
                                  'No se encontró información de kardex',
                                  style: TextStyle(color: Colors.grey),
                                ),
                              ),
                            ),
                          const SizedBox(height: 16),

                          // Enhanced Payment Summary
                          if (kardex != null) ...[
                            EnhancedPaymentSummary(kardex: kardex),
                            const SizedBox(height: 16),
                          ],

                          // Additional Costs Section
                          AdditionalCostsSection(
                            costosAdicionales: costosAdicionales,
                          ),

                          const SizedBox(height: 12),


                        ],
                      ),
                    );
                  },
                );
              },
            );
          },
        );
      },
    );
  }
}
