import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:beamer/beamer.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:gemini_app/app/domain/entities/alumno.dart';
import 'package:gemini_app/app/presentation/alumno_detalle/controller/detalle_alumno.controller.dart';
import 'package:gemini_app/app/presentation/alumno_detalle/views/info_tab/info_alumno.dart';
import 'package:gemini_app/app/presentation/alumno_detalle/views/pagos_tab/crear_recibo.dart';
import 'package:gemini_app/app/presentation/alumno_detalle/views/pagos_tab/historial_recibos.dart';
import 'package:gemini_app/app/presentation/alumno_detalle/views/recibos_tab/recibos_tab.dart';
import 'package:gemini_app/app/presentation/alumno_detalle/views/semanas_tab/semanas_tab.dart';
import 'alumno_detalle.state.dart';
import '../../styles/app_styles.dart';

class DetalleAlumnoPage extends StatefulWidget {
  const DetalleAlumnoPage({Key? key, required this.studentId}) : super(key: key);
  final String studentId;

  @override
  State<DetalleAlumnoPage> createState() => _DetalleAlumnoPageState();
}

class _DetalleAlumnoPageState extends State<DetalleAlumnoPage>
    with TickerProviderStateMixin {
  final DetalleAlumnoController controller = Get.find();
  late TabController _tabController;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 4, vsync: this);
    controller.setAlumnoId(widget.studentId);
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey[50],
      body: Column(
        children: [
          // Custom Header with Back Button and Student Info
          _buildCustomHeader(),

          // Tab Content
          Expanded(
            child: TabBarView(
              controller: _tabController,
              children: [
                // Info Tab
                Obx(() {
                  if (controller.state.value is DetalleAlumnoLoaded) {
                    final alumno = (controller.state.value as DetalleAlumnoLoaded).alumno;
                    return StudentInfoCard(alumnoId: alumno.id);
                  } else {
                    return const Center(child: CircularProgressIndicator());
                  }
                }),

                // Payments Tab
                Obx(() {
                  if (controller.state.value is DetalleAlumnoLoaded) {
                    final state = controller.state.value as DetalleAlumnoLoaded;
                    return EnhancedPaymentSection(
                      alumno: state.alumno,
                      costosAdicionales: state.costosAdicionales,
                      onGenerarRecibo: (reciboData) {
                        // La implementación está en EnhancedPaymentSection
                      },
                      onReciboCreated: () {
                        // Cambiar al tab de información (índice 0)
                        _tabController.animateTo(0);
                      },
                    );
                  } else {
                    return const Center(child: CircularProgressIndicator());
                  }
                }),

                // Recibos Tab
                Obx(() {
                  if (controller.state.value is DetalleAlumnoLoaded) {
                    return const RecibosTab();
                  } else {
                    return const Center(child: CircularProgressIndicator());
                  }
                }),

                // Weeks Tab
                Obx(() {
                  if (controller.state.value is DetalleAlumnoLoaded) {
                    final state = controller.state.value as DetalleAlumnoLoaded;
                    return SemanasTab(
                      controller: controller,
                      totalSemanas: state.semanas.length,
                    );
                  } else {
                    return const Center(child: CircularProgressIndicator());
                  }
                }),
              ],
            ),
          ),
        ],
      ),

      // Bottom Navigation Tabs
      bottomNavigationBar: _buildBottomTabBar(),
    );
  }

  Widget _buildCustomHeader() {
    return Container(
      padding: EdgeInsets.only(
        top: MediaQuery.of(context).padding.top + 8,
        left: 16,
        right: 16,
        bottom: 16,
      ),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            AppStyles.primaryColor,
            AppStyles.primaryColor.withOpacity(0.8),
          ],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        boxShadow: [
          BoxShadow(
            color: AppStyles.primaryColor.withOpacity(0.3),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Obx(() {
        if (controller.state.value is DetalleAlumnoLoaded) {
          final alumno = (controller.state.value as DetalleAlumnoLoaded).alumno;
          return Row(
            children: [
              // Back Button
              Container(
                decoration: BoxDecoration(
                  color: Colors.white.withOpacity(0.2),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: IconButton(
                  onPressed: () {
                    context.beamToNamed('/admin');
                  },
                  icon: const Icon(
                    Icons.arrow_back_ios_new,
                    color: Colors.white,
                    size: 20,
                  ),
                ),
              ),

              const SizedBox(width: 16),

              // Student Info
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      alumno.nombre,
                      style: GoogleFonts.poppins(
                        fontSize: 20,
                        fontWeight: FontWeight.w700,
                        color: Colors.white,
                      ),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                    const SizedBox(height: 4),
                    Row(
                      children: [
                        Container(
                          padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
                          decoration: BoxDecoration(
                            color: Colors.white.withOpacity(0.2),
                            borderRadius: BorderRadius.circular(12),
                          ),
                          child: Text(
                            'ID: ${alumno.id}',
                            style: GoogleFonts.poppins(
                              fontSize: 11,
                              color: Colors.white.withOpacity(0.9),
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                        ),
                        if (alumno.carrera != null && alumno.carrera!.isNotEmpty) ...[
                          const SizedBox(width: 8),
                          Container(
                            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
                            decoration: BoxDecoration(
                              color: Colors.white.withOpacity(0.2),
                              borderRadius: BorderRadius.circular(12),
                            ),
                            child: Text(
                              alumno.carrera!,
                              style: GoogleFonts.poppins(
                                fontSize: 11,
                                color: Colors.white.withOpacity(0.9),
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                          ),
                        ],
                      ],
                    ),
                  ],
                ),
              ),

              // Status Indicator
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                decoration: BoxDecoration(
                  color: Colors.green.withOpacity(0.2),
                  borderRadius: BorderRadius.circular(20),
                  border: Border.all(color: Colors.green.withOpacity(0.3)),
                ),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Container(
                      width: 6,
                      height: 6,
                      decoration: BoxDecoration(
                        color: Colors.green,
                        borderRadius: BorderRadius.circular(3),
                      ),
                    ),
                    const SizedBox(width: 6),
                    Text(
                      'Activo',
                      style: GoogleFonts.poppins(
                        fontSize: 11,
                        color: Colors.white,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          );
        } else {
          return Row(
            children: [
              // Back Button
              Container(
                decoration: BoxDecoration(
                  color: Colors.white.withOpacity(0.2),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: IconButton(
                  onPressed: () {
                    context.beamToNamed('/admin');
                  },
                  icon: const Icon(
                    Icons.arrow_back_ios_new,
                    color: Colors.white,
                    size: 20,
                  ),
                ),
              ),

              const SizedBox(width: 16),

              // Loading State
              Expanded(
                child: Text(
                  'Cargando información...',
                  style: GoogleFonts.poppins(
                    fontSize: 18,
                    fontWeight: FontWeight.w600,
                    color: Colors.white.withOpacity(0.9),
                  ),
                ),
              ),
            ],
          );
        }
      }),
    );
  }

  Widget _buildBottomTabBar() {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 8,
            offset: const Offset(0, -2),
          ),
        ],
      ),
      child: SafeArea(
        child: TabBar(
          controller: _tabController,
          tabs: [
            _buildTab(Icons.info_outline, 'Info'),
            _buildTab(Icons.payment_outlined, 'Pagos'),
            _buildTab(Icons.history, 'Historial'),
            _buildTab(Icons.calendar_today, 'Semanas'),
          ],
          labelColor: AppStyles.primaryColor,
          unselectedLabelColor: Colors.grey[600],
          indicatorColor: AppStyles.primaryColor,
          indicatorWeight: 3,
          indicatorSize: TabBarIndicatorSize.label,
          labelStyle: GoogleFonts.poppins(
            fontSize: 11,
            fontWeight: FontWeight.w600,
          ),
          unselectedLabelStyle: GoogleFonts.poppins(
            fontSize: 11,
            fontWeight: FontWeight.w500,
          ),
        ),
      ),
    );
  }

  Widget _buildTab(IconData icon, String label) {
    return Tab(
      height: 60,
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(icon, size: 22),
          const SizedBox(height: 4),
          Text(label),
        ],
      ),
    );
  }
}
