import 'package:gemini_app/app/domain/entities/alumno.dart';
import 'package:gemini_app/app/domain/modelos/kardex.dart';
import 'package:gemini_app/app/domain/modelos/detalle_pago_unificado.dart';
import 'package:gemini_app/app/domain/modelos/costo_adicional.dart';

abstract class DetalleAlumnoState {}

class DetalleAlumnoInitial extends DetalleAlumnoState {}

class DetalleAlumnoLoading extends DetalleAlumnoState {}

class DetalleAlumnoError extends DetalleAlumnoState {
  final String message;
  DetalleAlumnoError(this.message);
}

class DetalleAlumnoLoaded extends DetalleAlumnoState {
  final Alumno alumno;
  final Kardex? kardex;
  final List<KardexSemana> semanas;
  final List<DetallePagoUnificado> detallesPago;
  final List<CostoAdicional> costosAdicionales;

  DetalleAlumnoLoaded({
    required this.alumno,
    required this.kardex,
    required this.semanas,
    required this.detallesPago,
    required this.costosAdicionales,
  });

  DetalleAlumnoLoaded copyWith({
    Alumno? alumno,
    Kardex? kardex,
    List<KardexSemana>? semanas,
    List<DetallePagoUnificado>? detallesPago,
    List<CostoAdicional>? costosAdicionales,
  }) {
    return DetalleAlumnoLoaded(
      alumno: alumno ?? this.alumno,
      kardex: kardex ?? this.kardex,
      semanas: semanas ?? this.semanas,
      detallesPago: detallesPago ?? this.detallesPago,
      costosAdicionales: costosAdicionales ?? this.costosAdicionales,
    );
  }
}
