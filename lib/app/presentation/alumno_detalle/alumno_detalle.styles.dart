import 'package:flutter/material.dart';
import 'package:gemini_app/app/styles/app_styles.dart';

class DetalleAlumnoStyles {
  // Card decorations - keep component-specific
  
  static BoxDecoration headerDecoration(BuildContext context) {
    final theme = Theme.of(context);
    return BoxDecoration(
      color: theme.colorScheme.primary.withOpacity(0.05),
      borderRadius: const BorderRadius.only(
        topLeft: Radius.circular(12),
        topRight: Radius.circular(12),
      ),
      border: Border.all(
        color: theme.dividerColor.withOpacity(0.1),
        width: 1,
      ),
      gradient: LinearGradient(
        begin: Alignment.topLeft,
        end: Alignment.bottomRight,
        colors: [
          theme.colorScheme.primary.withOpacity(0.05),
          theme.colorScheme.primary.withOpacity(0.02),
        ],
      ),
    );
  }
  

  static BoxDecoration statsCardDecoration(BuildContext context) {
    return BoxDecoration(
      color: Theme.of(context).colorScheme.surfaceVariant.withOpacity(0.2),
      borderRadius: BorderRadius.circular(10),
      border: Border.all(
        color: Theme.of(context).dividerColor.withOpacity(0.1),
        width: 1,
      ),
    );
  }

  static BoxDecoration costItemDecoration(BuildContext context) {
    return BoxDecoration(
      color: Theme.of(context).colorScheme.surfaceVariant.withOpacity(0.1),
      borderRadius: BorderRadius.circular(10),
      border: Border.all(
        color: Theme.of(context).dividerColor.withOpacity(0.1),
        width: 1,
      ),
    );
  }

  static BoxDecoration paymentSummaryDecoration(BuildContext context) {
    return BoxDecoration(
      color: Theme.of(context).colorScheme.surfaceVariant.withOpacity(0.1),
      borderRadius: const BorderRadius.only(
        bottomLeft: Radius.circular(12),
        bottomRight: Radius.circular(12),
      ),
      border: Border(
        top: BorderSide(
          color: Theme.of(context).dividerColor.withOpacity(0.1),
          width: 1,
        ),
      ),
    );
  }

  // Text styles - simplify using AppStyles
  static TextStyle studentNameStyle(BuildContext context) =>
    AppStyles.detailSubtitleStyle.copyWith(
      fontSize: 20,
      letterSpacing: -0.2,
      color: Theme.of(context).colorScheme.onSurface,
    );

  static TextStyle studentIdStyle(BuildContext context) =>
    AppStyles.infoLabelStyle.copyWith(
      color: Theme.of(context).colorScheme.onSurface.withOpacity(0.6),
    );

  static TextStyle statusStyle() =>
    AppStyles.infoLabelStyle.copyWith(
      color: Colors.green,
      fontWeight: FontWeight.w600,
    );

  static TextStyle sectionTitleStyle(BuildContext context) =>
    AppStyles.subtitleStyle.copyWith(
      letterSpacing: -0.1,
      color: Theme.of(context).colorScheme.onSurface,
    );

  static TextStyle infoLabelStyle(BuildContext context) =>
    AppStyles.infoLabelStyle.copyWith(
      color: Theme.of(context).colorScheme.onSurface.withOpacity(0.6),
    );

  static TextStyle infoValueStyle(BuildContext context) =>
    AppStyles.infoValueStyle.copyWith(
      color: Theme.of(context).colorScheme.onSurface,
    );

  static TextStyle progressValueStyle(BuildContext context) =>
    AppStyles.infoValueStyle.copyWith(
      fontWeight: FontWeight.bold,
      color: Theme.of(context).primaryColor,
    );

  // Avatar styles - keep component-specific
  static BoxDecoration avatarDecoration(String name) {
    return BoxDecoration(
      shape: BoxShape.circle,
      color: _getAvatarColor(name),
      boxShadow: [
        BoxShadow(
          color: Colors.black.withOpacity(0.05),
          blurRadius: 6,
          offset: const Offset(0, 2),
        ),
      ],
    );
  }

  static TextStyle avatarTextStyle() =>
    AppStyles.detailTitleStyle.copyWith(
      color: Colors.white,
      fontSize: 28,
      fontWeight: FontWeight.bold,
    );

  // Helper methods - keep component-specific
  static String getInitials(String name) {
    final names = name.split(' ');
    if (names.length >= 2) {
      return '${names[0][0]}${names[1][0]}'.toUpperCase();
    }
    return name.isNotEmpty ? name[0].toUpperCase() : '?';
  }

  static Color _getAvatarColor(String name) {
    final colors = [
      const Color(0xFF6366F1), // Índigo
      const Color(0xFF8B5CF6), // Violeta
      const Color(0xFF10B981), // Esmeralda
      const Color(0xFFF59E0B), // Ámbar
      const Color(0xFF3B82F6), // Azul
    ];
    return colors[name.length % colors.length];
  }

  // Colors for payment section
  static Color successColor(BuildContext context) => const Color(0xFF10B981);
  static Color warningColor(BuildContext context) => const Color(0xFFF59E0B);
  static Color errorColor(BuildContext context) => const Color(0xFFEF4444);
  static Color textPrimary(BuildContext context) => Theme.of(context).colorScheme.onSurface;
  static Color textSecondary(BuildContext context) => Theme.of(context).colorScheme.onSurface.withOpacity(0.6);
  static Color borderColor(BuildContext context) => Theme.of(context).dividerColor.withOpacity(0.1);
  static Color cardBackground(BuildContext context) => Theme.of(context).colorScheme.surfaceVariant.withOpacity(0.1);
}