import 'package:gemini_app/di/injection.dart';
import 'package:get/get.dart';
import 'controller/detalle_alumno.controller.dart';
import 'views/pagos_tab/controllers/payment_controller.dart';
import 'package:gemini_app/app/domain/repositories/kardex_repository.dart';
import 'package:gemini_app/app/domain/casos_de_uso/watch_alumno_usecase.dart';
import 'package:gemini_app/app/domain/casos_de_uso/watch_kardex_usecase.dart';
import 'package:gemini_app/app/domain/casos_de_uso/watch_semanas_usecase.dart'; // Added import
import 'package:gemini_app/app/domain/casos_de_uso/watch_costos_adicionales_usecase.dart';

class DetalleAlumnoBinding implements Bindings {
  @override
  void dependencies() {
    // Registrar repositorios necesarios
    Get.put(getIt<KardexRepository>());
    Get.put(getIt<WatchAlumnoUseCase>());
    Get.put(getIt<WatchKardexUseCase>());
    Get.put(getIt<WatchSemanasUseCase>()); // Added registration
    Get.put(getIt<WatchCostosAdicionalesUseCase>());
    
    // Obtener controladores registrados en GetIt
    Get.put(getIt<DetalleAlumnoController>());
    Get.put(getIt<PaymentController>());
  }


  @override
  void close() {
    // Eliminar controladores al cerrar la pantalla
    Get.delete<DetalleAlumnoController>();
    Get.delete<PaymentController>();
    Get.delete<KardexRepository>(); // Also clean up repository
    Get.delete<WatchSemanasUseCase>(); // Added cleanup
    Get.delete<WatchCostosAdicionalesUseCase>();
  }
}
