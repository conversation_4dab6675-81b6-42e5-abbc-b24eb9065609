import 'dart:async';
import 'package:get/get.dart';
import 'package:flutter/foundation.dart';
import 'package:gemini_app/app/domain/entities/alumno.dart';
import 'package:gemini_app/app/domain/modelos/kardex.dart';
import 'package:gemini_app/app/domain/modelos/detalle_pago_unificado.dart';
import 'package:gemini_app/app/domain/modelos/costo_adicional.dart';
import 'package:gemini_app/app/domain/casos_de_uso/detalle_alumno_usecase.dart';
import 'package:gemini_app/app/domain/casos_de_uso/watch_semanas_usecase.dart';
import 'package:gemini_app/app/domain/casos_de_uso/watch_alumno_usecase.dart';
import 'package:gemini_app/app/domain/casos_de_uso/watch_kardex_usecase.dart';
import 'package:gemini_app/app/domain/casos_de_uso/watch_recibos_usecase.dart';
import 'package:gemini_app/app/domain/casos_de_uso/watch_costos_adicionales_usecase.dart';
import 'package:gemini_app/app/domain/modelos/recibo.dart';
import '../alumno_detalle.state.dart';

class DetalleAlumnoController extends GetxController {
  final DetalleAlumnoUseCase _detalleAlumnoUseCase;
  final WatchSemanasUseCase _watchSemanasUseCase;
  final WatchAlumnoUseCase _watchAlumnoUseCase;
  final WatchKardexUseCase _watchKardexUseCase;
  final WatchRecibosUseCase _watchRecibosUseCase;
  final WatchCostosAdicionalesUseCase _watchCostosAdicionalesUseCase;

  final state = Rx<DetalleAlumnoState>(DetalleAlumnoInitial());
  Stream<List<KardexSemana>>? semanasStream;
  Stream<Alumno>? _alumnoStream;
  Stream<Kardex?>? _kardexStream; // Declare the private field
  Stream<Kardex?>? get kardexStream => _kardexStream;
  Stream<List<Recibo>>? recibosStream;
  Stream<List<CostoAdicional>>? costosAdicionalesStream;
  StreamSubscription<Alumno>? _alumnoSubscription;
  StreamSubscription<Kardex?>? _kardexSubscription;

  DetalleAlumnoController(
    this._detalleAlumnoUseCase,
    this._watchSemanasUseCase,
    this._watchAlumnoUseCase,
    this._watchKardexUseCase,
    this._watchRecibosUseCase,
    this._watchCostosAdicionalesUseCase,
  );

  @override
  void onInit() {
    super.onInit();
  }

  void setAlumnoId(String alumnoId) {
    if (alumnoId.isNotEmpty) {
      // Cancelar suscripciones anteriores
      _alumnoSubscription?.cancel();
      _kardexSubscription?.cancel();
      
      // Resetear streams
      semanasStream = null;
      _alumnoStream = null;
      _kardexStream = null;
      costosAdicionalesStream = null;

      // Inicializar streams
      semanasStream = _watchSemanasUseCase.execute(alumnoId);
      _alumnoStream = _watchAlumnoUseCase.execute(alumnoId);
      _kardexStream = _watchKardexUseCase.execute(alumnoId);
      recibosStream = _watchRecibosUseCase.execute(alumnoId);
      costosAdicionalesStream = _watchCostosAdicionalesUseCase.execute(alumnoId);

      // Suscribirse a cambios en el alumno
      _alumnoSubscription = _alumnoStream?.listen((alumnoActualizado) {
        if (state.value is DetalleAlumnoLoaded) {
          final currentState = state.value as DetalleAlumnoLoaded;
          state.value = currentState.copyWith(alumno: alumnoActualizado);
        }
      }, onError: (error) {
        state.value = DetalleAlumnoError('Error en stream de alumno: $error');
      });

      // Suscribirse a cambios en el kardex
      _kardexSubscription = _kardexStream?.listen((kardexActualizado) {
        if (state.value is DetalleAlumnoLoaded) {
          final currentState = state.value as DetalleAlumnoLoaded;
          state.value = currentState.copyWith(kardex: kardexActualizado);
        }
      }, onError: (error) {
        state.value = DetalleAlumnoError('Error en stream de kardex: $error');
      });

      // Cargar datos iniciales
      loadAlumnoData(alumnoId);
    } else {
      state.value = DetalleAlumnoError('No se proporcionó un ID de alumno');
    }
  }

  Future<void> loadAlumnoData(String alumnoId) async {
    state.value = DetalleAlumnoLoading();

    try {
      final detalle = await _detalleAlumnoUseCase.getAlumnoDetails(alumnoId);

      state.value = DetalleAlumnoLoaded(
        alumno: detalle.alumno,
        kardex: detalle.kardex,
        semanas: detalle.semanas,
        detallesPago: detalle.detallesPago,
        costosAdicionales: detalle.costosAdicionales,
      );
    } catch (e) {
      state.value = DetalleAlumnoError('Error al cargar datos: $e');
      debugPrint('Error loading student data: $e');
    }
  }

  @override
  void onClose() {
    // Cancelar suscripciones
    _alumnoSubscription?.cancel();
    _kardexSubscription?.cancel();
    // Limpiar streams
    semanasStream = null;
    _alumnoStream = null;
    _kardexStream = null;
    costosAdicionalesStream = null;
    
    super.onClose();
  }

  Future<void> generarRecibo(
    String alumnoId,
    Map<String, dynamic> reciboData,
  ) async {
    // TODO: Implementar lógica de recibo
    await loadAlumnoData(alumnoId);
  }

  Future<void> actualizarPagos(String alumnoId) async {
    await loadAlumnoData(alumnoId);
  }
}

