import 'package:gemini_app/app/domain/entities/alumno.dart';
import 'package:gemini_app/app/domain/modelos/kardex.dart';
import 'package:gemini_app/app/domain/modelos/detalle_pago_unificado.dart';
import 'package:gemini_app/app/domain/modelos/costo_adicional.dart';

abstract class DetalleAlumnoState {}

class DetalleAlumnoInitial extends DetalleAlumnoState {}

class DetalleAlumnoLoading extends DetalleAlumnoState {}

class DetalleAlumnoLoaded extends DetalleAlumnoState {
  final Alumno alumno;
  final List<CostoAdicional> costosAdicionales;
  final Kardex? kardex;
  final List<KardexSemana> semanas;

  DetalleAlumnoLoaded({
    required this.alumno,
    required this.costosAdicionales,
    this.kardex,
    required this.semanas,
  });
  

  DetalleAlumnoLoaded copyWith({
    Alumno? alumno,
    List<CostoAdicional>? costosAdicionales,
    Kardex? kardex,
    List<KardexSemana>? semanas,
  }) {
    return DetalleAlumnoLoaded(
      alumno: alumno ?? this.alumno,
      costosAdicionales: costosAdicionales ?? this.costosAdicionales,
      kardex: kardex ?? this.kardex,
      semanas: semanas ?? this.semanas,
    );
  }
}

class DetalleAlumnoError extends DetalleAlumnoState {
  final String message;
  
  DetalleAlumnoError(this.message);
}