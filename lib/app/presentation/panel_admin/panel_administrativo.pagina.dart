import 'package:cloud_firestore/cloud_firestore.dart';

import '../../data/repositories/alumno_repository_impl.dart';
import '../../domain/entities/alumno.dart';
import '../../domain/modelos/alumno.model.dart';
import '../../providers/auth_provider.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:get/get.dart';
import 'package:google_fonts/google_fonts.dart';
import '../lista_alumnos/lista_alumnos.page.dart';
import '../lista_alumnos/lista_alumnos.controller.dart';
import '../lista_alumnos/casos_de_uso/obtener_alumnos_paginados.caso_uso.dart';
import '../../servicios/busqueda_service.dart';
import 'package:gemini_app/app/presentation/lista_alumnos/lista_alumnos.binding.dart';

class PanelAdministrativoPage extends StatefulWidget {
  const PanelAdministrativoPage({super.key});

  @override
  _PanelAdministrativoPageState createState() =>
      _PanelAdministrativoPageState();
}

class _PanelAdministrativoPageState extends State<PanelAdministrativoPage> {
  int _selectedIndex = 0;

  // Color scheme
  final _primaryColor = const Color(0xFF6366F1);
  final _secondaryColor = const Color(0xFFA855F7);
  final _backgroundColor = const Color(0xFFF3F4F6);
  final _cardColor = Colors.white;
  final _textColor = const Color(0xFF1F2937);
  final _secondaryTextColor = const Color(0xFF6B7280);
  final _borderColor = const Color(0xFFE5E7EB);
  final _indigoLight = const Color(0xFFEEF2FF);
  final _indigoBorder = const Color(0xFFC7D2FE);

  final FirebaseFirestore _firestore = FirebaseFirestore.instance;

  @override
  void initState() {
    super.initState();
  }

  // Y el método para mostrar el diálogo
  void _mostrarDialogoActualizacion() {
    final busquedaService = BusquedaService();
    bool actualizando = false;

    showDialog(
      context: context,
      builder: (context) {
        return StatefulBuilder(
          builder: (context, setState) {
            return AlertDialog(
              title: Text('Actualizar índices de búsqueda'),
              content:
                  actualizando
                      ? Column(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          CircularProgressIndicator(),
                          SizedBox(height: 16),
                          Text('Actualizando índices...'),
                        ],
                      )
                      : Text(
                        '¿Deseas actualizar los índices de búsqueda para todos los alumnos? Esto puede tomar unos momentos.',
                      ),
              actions: [
                if (!actualizando)
                  TextButton(
                    onPressed: () => Navigator.pop(context),
                    child: Text('Cancelar'),
                  ),
                ElevatedButton(
                  onPressed:
                      actualizando
                          ? null
                          : () async {
                            setState(() => actualizando = true);
                            try {
                              await busquedaService.actualizarTodosLosAlumnos();
                              if (mounted) {
                                ScaffoldMessenger.of(context).showSnackBar(
                                  SnackBar(
                                    content: Text(
                                      'Índices actualizados correctamente',
                                    ),
                                    backgroundColor: Colors.green,
                                  ),
                                );
                                Navigator.pop(context);
                              }
                            } catch (e) {
                              if (mounted) {
                                ScaffoldMessenger.of(context).showSnackBar(
                                  SnackBar(
                                    content: Text(
                                      'Error al actualizar índices: $e',
                                    ),
                                    backgroundColor: Colors.red,
                                  ),
                                );
                                Navigator.pop(context);
                              }
                            }
                          },
                  child: Text(actualizando ? 'Actualizando...' : 'Actualizar'),
                ),
              ],
            );
          },
        );
      },
    );
  }

  // Replace the _sections list with a getter method
  List<Map<String, dynamic>> get _sections {
    return [
      {
        'icon': Icons.groups_outlined,
        'title': 'Alumnos',
        'page': ListaAlumnosPagina(),
      },
      {
        'icon': Icons.settings_outlined,
        'title': 'Configuración',
        'page': _buildConfiguracionPage(),
      },
    ];
  }

  // Add this method to build the configuration page
  Widget _buildConfiguracionPage() {
    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.start,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Configuración del Sistema',
            style: GoogleFonts.inter(
              fontSize: 24,
              fontWeight: FontWeight.w600,
              color: _textColor,
            ),
          ),
          const SizedBox(height: 24),
          Card(
            elevation: 2,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(12),
              side: BorderSide(color: _borderColor, width: 1),
            ),
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      const Icon(
                        Icons.search_off_outlined,
                        color: Color(0xFF6B7280),
                      ),
                      const SizedBox(width: 12),
                      Text(
                        'Búsqueda de Alumnos',
                        style: GoogleFonts.inter(
                          fontSize: 18,
                          fontWeight: FontWeight.w600,
                          color: _textColor,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 16),
                  Text(
                    'Actualiza los tokens de búsqueda para mejorar los resultados de búsqueda en la aplicación.',
                    style: GoogleFonts.inter(
                      color: _secondaryTextColor,
                      fontSize: 14,
                    ),
                  ),
                  const SizedBox(height: 24),
                ],
              ),
            ),
          ),

          // Add this button in your _buildConfiguracionPage method, next to the other button
          ElevatedButton.icon(
            onPressed: () {
              _mostrarDialogoActualizacion();
            },
            icon: Icon(Icons.update),
            label: Text('Actualizar índices de búsqueda'),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.orange,
              foregroundColor: Colors.white,
            ),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final authProvider = context.watch<ProveedorAutenticacion>();

    // Update the provider value
    _sections[0]['page'] = ListaAlumnosPagina();

    if (!authProvider.estaAutenticado) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        Get.offAllNamed('/login');
      });
      return const Scaffold(body: Center(child: CircularProgressIndicator()));
    }

    return Scaffold(
      backgroundColor: _backgroundColor,
      appBar: AppBar(
        title: Text(
          'Panel Administrativo',
          style: GoogleFonts.inter(
            color: Colors.white,
            fontSize: 18,
            fontWeight: FontWeight.w600,
          ),
        ),
        backgroundColor: Colors.transparent,
        elevation: 0,
        iconTheme: const IconThemeData(color: Colors.white),
        actions: [
          IconButton(
            icon: const Icon(Icons.notifications_outlined, color: Colors.white),
            onPressed: () {},
          ),
        ],
        flexibleSpace: Container(
          decoration: BoxDecoration(
            gradient: LinearGradient(
              colors: [_primaryColor, _secondaryColor],
              begin: Alignment.centerLeft,
              end: Alignment.centerRight,
            ),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withOpacity(0.1),
                blurRadius: 8,
                offset: const Offset(0, 2),
              ),
            ],
          ),
        ),
      ),
      body: Column(
        children: [
          // Main Content
          Expanded(
            child: IndexedStack(
              index: _selectedIndex,
              children:
                  _sections.map<Widget>((s) => s['page'] as Widget).toList(),
            ),
          ),
          // Bottom Navigation Bar
          Container(
            decoration: BoxDecoration(
              color: _cardColor,
              border: Border(top: BorderSide(color: _borderColor, width: 1)),
            ),
            child: SafeArea(
              top: false,
              child: Padding(
                padding: const EdgeInsets.symmetric(vertical: 4),
                child: Row(
                  children: List.generate(_sections.length, (index) {
                    final isSelected = _selectedIndex == index;
                    return Expanded(
                      child: TextButton(
                        onPressed: () {
                          setState(() {
                            _selectedIndex = index;
                          });
                        },
                        style: TextButton.styleFrom(
                          padding: const EdgeInsets.symmetric(vertical: 12),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(8),
                          ),
                          backgroundColor:
                              isSelected ? _indigoLight : Colors.transparent,
                        ),
                        child: Column(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            Icon(
                              _sections[index]['icon'],
                              size: 24,
                              color:
                                  isSelected
                                      ? _primaryColor
                                      : _secondaryTextColor,
                            ),
                            const SizedBox(height: 4),
                            Text(
                              _sections[index]['title'],
                              style: GoogleFonts.inter(
                                fontSize: 10,
                                fontWeight:
                                    isSelected
                                        ? FontWeight.w600
                                        : FontWeight.normal,
                                color:
                                    isSelected
                                        ? _primaryColor
                                        : _secondaryTextColor,
                                height: 1.2,
                              ),
                            ),
                          ],
                        ),
                      ),
                    );
                  }),
                ),
              ),
            ),
          ),
        ],
      ),
      drawer: _buildDrawer(context, authProvider),
    );
  }

  Widget _buildDrawer(
    BuildContext context,
    ProveedorAutenticacion authProvider,
  ) {
    return Drawer(
      child: Container(
        color: _cardColor,
        child: Column(
          children: [
            // Drawer Header
            Container(
              width: double.infinity,
              padding: const EdgeInsets.only(
                top: 40,
                bottom: 20,
                left: 20,
                right: 20,
              ),
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  colors: [_primaryColor, _secondaryColor],
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                ),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  CircleAvatar(
                    radius: 30,
                    backgroundColor: Colors.white.withOpacity(0.3),
                    child: const Icon(
                      Icons.person,
                      size: 30,
                      color: Colors.white,
                    ),
                  ),
                  const SizedBox(height: 16),
                  Text(
                    authProvider.usuario?.email ?? '<EMAIL>',
                    style: const TextStyle(
                      color: Colors.white,
                      fontSize: 16,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    'Administrador',
                    style: TextStyle(
                      color: Colors.white.withOpacity(0.9),
                      fontSize: 14,
                    ),
                  ),
                ],
              ),
            ),
            // Menu Items
            Expanded(
              child: ListView(
                padding: const EdgeInsets.symmetric(vertical: 8),
                children: [
                  ..._sections.asMap().entries.map((entry) {
                    final index = entry.key;
                    final section = entry.value;
                    return _buildDrawerItem(
                      icon: section['icon'] as IconData,
                      title: section['title'] as String,
                      isSelected: _selectedIndex == index,
                      onTap: () {
                        setState(() {
                          _selectedIndex = index;
                        });
                        Navigator.pop(context);
                      },
                    );
                  }),
                  const Divider(indent: 16, endIndent: 16, height: 1),
                  _buildDrawerItem(
                    icon: Icons.logout,
                    title: 'Cerrar Sesión',
                    isSelected: false,
                    iconColor: Colors.red.shade400,
                    textColor: Colors.red.shade400,
                    onTap: () async {
                      await authProvider.signOut();
                      Get.offAllNamed('/login');
                    },
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDrawerItem({
    required IconData icon,
    required String title,
    required bool isSelected,
    Color? iconColor,
    Color? textColor,
    VoidCallback? onTap,
  }) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: isSelected ? _primaryColor.withOpacity(0.1) : Colors.transparent,
        borderRadius: BorderRadius.circular(12),
      ),
      child: ListTile(
        leading: Icon(
          icon,
          color: isSelected ? _primaryColor : iconColor ?? _secondaryTextColor,
          size: 24,
        ),
        title: Text(
          title,
          style: GoogleFonts.inter(
            fontSize: 14,
            fontWeight: isSelected ? FontWeight.w600 : FontWeight.normal,
            color: isSelected ? _primaryColor : textColor ?? _textColor,
          ),
        ),
        selected: isSelected,
        onTap: onTap,
        contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
        dense: true,
      ),
    );
  }

  // Add this method
  Future<void> _migrarNombresApellidos() async {
    try {
      // Show loading dialog
      showDialog(
        context: context,
        barrierDismissible: false,
        builder:
            (context) => const AlertDialog(
              title: Text('Migrando nombres de apellidos...'),
              content: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  CircularProgressIndicator(),
                  SizedBox(height: 16),
                  Text('Actualizando registros...'),
                ],
              ),
            ),
      );

      //await alumnosRepositoryImpl.migrarNombresApellidos();
      

      if (mounted) {
        Navigator.of(context, rootNavigator: true).pop();
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Migración de apellidos completada exitosamente'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        Navigator.of(context, rootNavigator: true).pop();
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error en la migración: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }
}
