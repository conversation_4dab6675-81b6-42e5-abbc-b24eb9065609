import 'package:flutter/material.dart';

class AdminSidenav extends StatelessWidget {
  final String currentRoute;
  final Function(String) onNavigate;

  const AdminSidenav({
    Key? key,
    required this.currentRoute,
    required this.onNavigate,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Drawer(
      child: Column(
        children: [
          // Header del drawer
          Container(
            height: 200,
            decoration: BoxDecoration(
              gradient: LinearGradient(
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
                colors: [
                  Theme.of(context).primaryColor,
                  Theme.of(context).primaryColor.withOpacity(0.8),
                ],
              ),
            ),
            child: const Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    Icons.school,
                    size: 60,
                    color: Colors.white,
                  ),
                  SizedBox(height: 10),
                  Text(
                    'Panel de Pagos',
                    style: TextStyle(
                      color: Colors.white,
                      fontSize: 20,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  Text(
                    'Administración',
                    style: TextStyle(
                      color: Colors.white70,
                      fontSize: 14,
                    ),
                  ),
                ],
              ),
            ),
          ),

          // Menú principal
          Expanded(
            child: ListView(
              padding: EdgeInsets.zero,
              children: [
                _buildMenuSection('GESTIÓN DE PAGOS'),
                _buildMenuItem(
                  icon: Icons.dashboard,
                  title: 'Dashboard',
                  route: '/dashboard',
                  badge: null,
                ),
                _buildMenuItem(
                  icon: Icons.people,
                  title: 'Alumnos y Kardex',
                  route: '/alumnos',
                  badge: null,
                ),
                _buildMenuItem(
                  icon: Icons.payment,
                  title: 'Registrar Pago',
                  route: '/registrar-pago',
                  badge: null,
                ),
                _buildMenuItem(
                  icon: Icons.receipt_long,
                  title: 'Recibos',
                  route: '/recibos',
                  badge: null,
                ),
                _buildMenuItem(
                  icon: Icons.warning,
                  title: 'Pagos Vencidos',
                  route: '/vencidos',
                  badge: '12', // Esto vendría de un provider
                ),

                const Divider(),
                _buildMenuSection('CONFIGURACIÓN'),
                _buildMenuItem(
                  icon: Icons.school,
                  title: 'Cursos',
                  route: '/cursos',
                  badge: null,
                ),
                _buildMenuItem(
                  icon: Icons.category,
                  title: 'Conceptos de Pago',
                  route: '/conceptos',
                  badge: null,
                ),
                _buildMenuItem(
                  icon: Icons.business,
                  title: 'Planteles',
                  route: '/planteles',
                  badge: null,
                ),

                const Divider(),
                _buildMenuSection('REPORTES'),
                _buildMenuItem(
                  icon: Icons.analytics,
                  title: 'Ingresos',
                  route: '/reportes-ingresos',
                  badge: null,
                ),
                _buildMenuItem(
                  icon: Icons.trending_down,
                  title: 'Egresos',
                  route: '/reportes-egresos',
                  badge: null,
                ),
                _buildMenuItem(
                  icon: Icons.assessment,
                  title: 'Estado de Cuentas',
                  route: '/estado-cuentas',
                  badge: null,
                ),
              ],
            ),
          ),

          // Footer
          Container(
            padding: const EdgeInsets.all(16),
            child: Column(
              children: [
                const Divider(),
                _buildMenuItem(
                  icon: Icons.settings,
                  title: 'Configuración',
                  route: '/configuracion',
                  badge: null,
                ),
                _buildMenuItem(
                  icon: Icons.logout,
                  title: 'Cerrar Sesión',
                  route: '/logout',
                  badge: null,
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildMenuSection(String title) {
    return Padding(
      padding: const EdgeInsets.fromLTRB(16, 16, 16, 8),
      child: Text(
        title,
        style: const TextStyle(
          fontSize: 12,
          fontWeight: FontWeight.bold,
          color: Colors.grey,
        ),
      ),
    );
  }

  Widget _buildMenuItem({
    required IconData icon,
    required String title,
    required String route,
    String? badge,
  }) {
    final isSelected = currentRoute == route;

    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(8),
        color: isSelected ? Colors.blue.withOpacity(0.1) : null,
      ),
      child: ListTile(
        leading: Icon(
          icon,
          color: isSelected ? Colors.blue : Colors.grey[600],
        ),
        title: Text(
          title,
          style: TextStyle(
            color: isSelected ? Colors.blue : Colors.grey[800],
            fontWeight: isSelected ? FontWeight.w600 : FontWeight.normal,
          ),
        ),
        trailing: badge != null
            ? Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                decoration: BoxDecoration(
                  color: Colors.red,
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Text(
                  badge,
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 12,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              )
            : null,
        onTap: () => onNavigate(route),
      ),
    );
  }
}