import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/cupertino.dart';
import 'database_service.dart';

class FirestoreService implements DatabaseService {
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;

  @override
  Future<Map<String, dynamic>?> getDocument(String path) async {
    final doc = await _firestore.doc(path).get();
    return doc.data();
  }

  @override
  Stream<Map<String, dynamic>?> streamDocument(String path) {
    return _firestore.doc(path).snapshots().map((snapshot) {
      final data = snapshot.data();
      final fromCache = snapshot.metadata.isFromCache;
      return data;
    });
  }

  @override
  Future<(List<Map<String, dynamic>>, dynamic)> getCollection({
    required String path,
    int limit = 10,
    dynamic startAfter,
    Map<String, dynamic> filters = const {},
    String? sortBy,
    bool descending = false,
  }) async {
    Query query = _firestore.collection(path);

    if (filters.isNotEmpty) {
      for (var entry in filters.entries) {
        query = query.where(entry.key, isEqualTo: entry.value);
      }
    }

    if (sortBy != null) {
      query = query.orderBy(sortBy, descending: descending);
    }

    query = query.limit(limit);

    if (startAfter != null) {
      query = query.startAfterDocument(startAfter as DocumentSnapshot);
    }

    final snapshot = await query.get();
    final lastDoc = snapshot.docs.isNotEmpty ? snapshot.docs.last : null;

    return (
      snapshot.docs.map<Map<String, dynamic>>((doc) {
        final data = doc.data() as Map<String, dynamic>? ?? <String, dynamic>{};
        return {...data, 'id': doc.id};
      }).toList(),
      lastDoc,
    );
  }

  @override
  Future<(List<Map<String, dynamic>>, dynamic)> searchAlumnos({
    required String searchQuery,
    required String path,
    int limit = 10,
    dynamic startAfter,
    String? sortBy,
    bool descending = false,
  }) async {
    try {
      Query query = _firestore.collection(path);

      if (searchQuery != '') {
        query = query.where(
          'terminosBusqueda',
          arrayContains: searchQuery.toLowerCase().trim(),
        );
      }

      if (sortBy != null) {
        query = query.orderBy(sortBy, descending: descending);
      }

      query = query.limit(limit);

      if (startAfter != null) {
        query = query.startAfterDocument(startAfter as DocumentSnapshot);
      }

      final snapshot = await query.get();
      final lastDoc = snapshot.docs.isNotEmpty ? snapshot.docs.last : null;

      return (
        snapshot.docs.map<Map<String, dynamic>>((doc) {
          final data =
              doc.data() as Map<String, dynamic>? ?? <String, dynamic>{};
          return {...data, 'id': doc.id};
        }).toList(),
        lastDoc,
      );
    } catch (e) {
      if (e is FirebaseException && e.code == 'failed-precondition') {
        debugPrint(
          'Firestore index required for terminosBusqueda arrayContains query. Check Firestore console.',
        );
      }
      rethrow;
    }
  }

  Stream<(List<Map<String, dynamic>>, DocumentSnapshot?)> streamSearchAlumnos({
    required String searchQuery,
    required String path,
    int limit = 10,
    dynamic startAfter,
    String? sortBy,
    bool descending = false,
  }) {
    try {
      Query query = _firestore.collection(path);

      if (searchQuery != '') {
        query = query.where(
          'terminosBusqueda',
          arrayContains: searchQuery.toLowerCase().trim(),
        );
      }

      if (sortBy != null) {
        query = query.orderBy(sortBy, descending: descending);
      }

      query = query.limit(limit);

      if (startAfter != null) {
        query = query.startAfterDocument(startAfter as DocumentSnapshot);
      }

      return query.snapshots().map((snapshot) {
        final lastDoc = snapshot.docs.isNotEmpty ? snapshot.docs.last : null;
        return (
          snapshot.docs.map((doc) {
            final data =
                doc.data() as Map<String, dynamic>? ?? <String, dynamic>{};
            return {...data, 'id': doc.id};
          }).toList(),
          lastDoc,
        );
      });
    } catch (e) {
      debugPrint('Error en streamSearchAlumnos: $e');
      if (e is FirebaseException && e.code == 'failed-precondition') {
        debugPrint(
          'Firestore index required for terminosBusqueda arrayContains query.',
        );
      }
      rethrow;
    }
  }

  @override
  Future<void> saveDocument(
    String path,
    Map<String, dynamic> data, {
    bool merge = false,
  }) async {
    await _firestore.doc(path).set(data, SetOptions(merge: merge));
  }

  @override
  Future<void> deleteDocument(String path) async {
    await _firestore.doc(path).delete();
  }

  @override
  Future<void> batchUpdate(List<Map<String, dynamic>> updates) async {
    final batch = _firestore.batch();
    for (var update in updates) {
      final path = update['path'] as String;
      final data = update['data'] as Map<String, dynamic>;
      final docRef = _firestore.doc(path);
      batch.set(docRef, data, SetOptions(merge: true));
    }
    await batch.commit();
  }

  @override
  Stream<List<Map<String, dynamic>>> streamCollection(
    String path, {
    Map<String, dynamic> filters = const {},
  }) {
    Query query = _firestore.collection(path);
    if (filters.isNotEmpty) {
      for (var entry in filters.entries) {
        query = query.where(entry.key, isEqualTo: entry.value);
      }
    }
    return query.snapshots().map(
      (snapshot) =>
          snapshot.docs
              .map<Map<String, dynamic>>(
                (doc) =>
                    (doc.data() as Map<String, dynamic>?) ??
                    <String, dynamic>{},
              )
              .toList(),
    );
  }
}
