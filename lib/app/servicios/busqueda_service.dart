import 'package:cloud_firestore/cloud_firestore.dart';

class BusquedaService {
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;

  // Genera un array de términos de búsqueda para un nombre dado
  List<String> generarTerminosBusqueda(String texto) {
    final terminos = <String>{}; // Usamos un Set para evitar duplicados
    final textoLimpio = texto.toLowerCase().trim();

    // Primero, dividir por espacios y guiones bajos para obtener todas las partes
    final partes = textoLimpio.split(RegExp(r'[_\s]+'));

    // Generar términos para cada parte individual
    for (var parte in partes) {
      if (parte.isEmpty) continue;

      // Agregar todos los prefijos de cada parte
      for (int i = 1; i <= parte.length; i++) {
        terminos.add(parte.substring(0, i));
      }

      // Manejar casos como "de la", "del", etc.
      if (parte == 'de' || parte == 'la' || parte == 'del' || parte == 'los' || parte == 'las') {
        continue; // No agregar estos como términos individuales
      }
    }

    // Generar términos para combinaciones de nombre y apellido
    if (partes.length > 1) {
      // Agregar combinaciones de nombre + apellido
      for (int i = 0; i < partes.length; i++) {
        if (partes[i].isEmpty) continue;

        // Agregar nombre + siguiente palabra
        if (i < partes.length - 1) {
          final combinacion = '${partes[i]}_${partes[i+1]}';
          terminos.add(combinacion);

          // Agregar prefijos de la combinación
          for (int j = 1; j <= combinacion.length; j++) {
            terminos.add(combinacion.substring(0, j));
          }
        }
      }

      // Agregar nombre completo
      final nombreCompleto = partes.join('_');
      terminos.add(nombreCompleto);

      // Agregar variantes con espacios
      terminos.add(partes.join(' '));
    }

    return terminos.toList()..sort((a, b) => a.length.compareTo(b.length));
  }

  // Actualiza los términos de búsqueda para un alumno específico
  Future<void> actualizarTerminosBusqueda(String alumnoId) async {
    try {
      final doc = await _firestore.collection('alumnos').doc(alumnoId).get();
      if (!doc.exists) return;

      final data = doc.data()!;
      final nombreCompleto = '${data['nombre']}_${data['apellidoPaterno']}_${data['apellidoMaterno'] ?? ''}';
      final terminos = generarTerminosBusqueda(nombreCompleto);

      await _firestore.collection('alumnos').doc(alumnoId).update({
        'terminosBusqueda': terminos,
        'indiceBusqueda': nombreCompleto.toLowerCase().trim(),
      });
    } catch (e) {
      print('Error actualizando términos de búsqueda para $alumnoId: $e');
      rethrow;
    }
  }

  // Actualiza los términos de búsqueda para todos los alumnos
  Future<void> actualizarTodosLosAlumnos() async {
    try {
      final batch = _firestore.batch();
      int batchCount = 0;
      const batchSize = 100;

      final query = _firestore.collection('alumnos').limit(300);
      final snapshot = await query.get();

      for (final doc in snapshot.docs) {
        final data = doc.data();
        final nombre = '${data['nombre']} ${data['apellidoPaterno']} ${data['apellidoMaterno'] ?? ''}'
            .toLowerCase()
            .trim()
            .replaceAll(RegExp(r'\s+'), ' '); // Normalizar espacios

        final terminos = generarTerminosBusqueda(nombre);

        batch.update(doc.reference, {
          'terminosBusqueda': terminos,
          'indiceBusqueda': nombre.replaceAll(' ', '_'),
        });

        batchCount++;
        if (batchCount % batchSize == 0) {
          await batch.commit();
          batchCount = 0;
        }
      }

      if (batchCount > 0) {
        await batch.commit();
      }

    } catch (e) {
      print('Error actualizando términos de búsqueda: $e');
      rethrow;
    }
  }
}
