import 'package:cloud_firestore/cloud_firestore.dart';

abstract class DatabaseService {
  Future<(List<Map<String, dynamic>>, dynamic)> getCollection({
    required String path,
    int limit = 10,
    dynamic startAfter,
    Map<String, dynamic> filters = const {},
    String? sortBy,
    bool descending = false,
  });

  Future<(List<Map<String, dynamic>>, dynamic)> searchAlumnos({
    required String searchQuery,
    required String path,
    int limit = 10,
    dynamic startAfter,
    String? sortBy,
    bool descending = false,
  });

  Stream<(List<Map<String, dynamic>>, DocumentSnapshot?)> streamSearchAlumnos({
    required String searchQuery,
    required String path,
    int limit = 10,
    String? sortBy,
    dynamic startAfter,
    bool descending = false,
  });

  Future<Map<String, dynamic>?> getDocument(String path);
  Stream<Map<String, dynamic>?> streamDocument(String path);
  Future<void> saveDocument(String path, Map<String, dynamic> data, {bool merge = false});
  Future<void> batchUpdate(List<Map<String, dynamic>> updates);
  Future<void> deleteDocument(String path);
  Stream<List<Map<String, dynamic>>> streamCollection(String path, {Map<String, dynamic> filters = const {}});
}