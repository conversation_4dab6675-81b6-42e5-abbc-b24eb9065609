import 'package:flutter/material.dart';

class AppStyles {
  // Colors
  static const Color primaryColor = Color(0xFF4F46E5);
  static const Color backgroundColor = Color(0xFFF3F4F6);
  static const Color cardBackground = Color(0xFFFFFFFF);
  static const Color textColor = Color(0xFF111827);
  static const Color secondaryTextColor = Color(0xFF6B7280);
  static const Color borderColor = Color(0xFFE5E7EB);
  static const Color accentLight = Color(0xFFEEF2FF);
  static const Color accentBorder = Color(0xFFC7D2FE);

  // Text Styles
  static TextStyle titleStyle = TextStyle(
    fontSize: 20,
    fontWeight: FontWeight.bold,
    color: textColor,
  );

  static TextStyle subtitleStyle = TextStyle(
    fontSize: 16,
    fontWeight: FontWeight.w600,
    color: textColor,
    letterSpacing: -0.2,
  );

  static TextStyle errorText = TextStyle(
    color: Colors.red,
    fontSize: 16,
    fontWeight: FontWeight.w500,
  );

  static TextStyle bodyStyle = TextStyle(
    fontSize: 14,
    color: textColor,
  );

  static TextStyle captionStyle = TextStyle(
    fontSize: 12,
    color: secondaryTextColor,
  );

  // Button Styles
  static ButtonStyle outlineButtonStyle = OutlinedButton.styleFrom(
    padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
    shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
    side: BorderSide(color: accentBorder, width: 1),
    backgroundColor: accentLight,
  );
// Detail Page Styles
            static TextStyle detailTitleStyle = TextStyle(
              fontSize: 24,
              fontWeight: FontWeight.bold,
              color: textColor,
            );
          
            static TextStyle detailSubtitleStyle = TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.w600,
              color: textColor,
            );
          
            static BoxDecoration detailCardDecoration = BoxDecoration(
              color: cardBackground,
              borderRadius: BorderRadius.circular(12),
              boxShadow: [
                BoxShadow(
                  color: Colors.black12,
                  blurRadius: 6,
                  offset: Offset(0, 2),
                ),
              ],
            );
          
            static TextStyle infoLabelStyle = TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.w600,
              color: secondaryTextColor,
            );
          
            static TextStyle infoValueStyle = TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w500,
              color: textColor,
            );
}