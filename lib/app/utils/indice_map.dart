String obtenerNombreIndex(String nombre) {
  final acentos = {
    'á': 'a',
    'é': 'e',
    'í': 'i',
    'ó': 'o',
    'ú': 'u',
    'Á': 'a',
    'É': 'e',
    'Í': 'i',
    'Ó': 'o',
    'Ú': 'u',
  };

  String nombreIndex = nombre.toLowerCase();
  acentos.forEach((k, v) {
    nombreIndex = nombreIndex.replaceAll(k, v);
  });
  nombreIndex = nombreIndex.replaceAll(' ', '_');
  return nombreIndex;
}