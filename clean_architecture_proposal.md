# Propuesta de Arquitectura Limpia

## Estructura de Directorios
```plaintext
lib/
├── core/                            
│   ├── errors/                      
│   ├── utils/                       
│   └── constants/                   
│
├── domain/                          
│   ├── entities/                    
│   │   ├── alumno.dart
│   │   └── recibo.dart
│   ├── models/                      
│   ├── repositories/                
│   └── value_objects/               
│
├── data/                            
│   ├── repositories/                
│   └── datasources/                 
│
├── features/                        
│   ├── alumnos/
│   │   └── presentation/            
│   │       ├── detalle_alumno/
│   │       │   ├── detalle_alumno.page.dart
│   │       │   ├── detalle_alumno.controller.dart
│   │       │   ├── detalle_alumno.state.dart
│   │       │   └── widgets/
│   │       │       ├── info_alumno.dart
│   │       │       └── historial_recibos.dart
│   │       └── lista_alumnos/
│   │
│   ├── auth/
│   │   └── presentation/
│   │       ├── login/
│   │       │   ├── login.page.dart
│   │       │   ├── login.controller.dart
│   │       │   └── casos_de_uso/
│   │       │       └── sign_in_usecase.dart
│   │
│   └── pagos/                       
│
├── routes/                          
├── injection.dart                   
└── main.dart
```

## Diagrama de Dependencias
```mermaid
graph TD
    A[Features/Presentation] -->|Usa| B[Domain]
    B -->|Implementado por| C[Data]
    A -->|Usa| D[Core]
    C -->|Usa| D[Core]
    
    subgraph Presentation
        A1[Páginas]
        A2[Controladores]
        A3[Widgets]
    end
    
    subgraph Domain
        B1[Entidades]
        B2[Interfaces de Repositorio]
    end
    
    subgraph Data
        C1[Repositorios Implementados]
        C2[Firestore]
    end
    
    subgraph Core
        D1[Utilidades]
        D2[Manejo de Errores]
    end
```

## Plan de Implementación
1. **Reorganizar directorios existentes**
   - Crear estructura de `core/`, `domain/`, `data/`, y `features/`
   - Mover archivos a sus nuevas ubicaciones

2. **Actualizar dependencias**
   - Ajustar imports en todos los archivos afectados
   - Actualizar `injection.dart` para reflejar nuevas rutas

3. **Refactorizar controladores**
   - Asegurar que usen casos de uso en lugar de acceder repositorios directamente
   - Mover lógica de negocio a casos de uso

4. **Implementar repositorios**
   - Crear interfaces en `domain/repositories/`
   - Implementar concretamente en `data/repositories/`

5. **Actualizar rutas**
   - Modificar `beamer_locations.dart` para nuevas rutas de páginas

6. **Pruebas**
   - Verificar que todas las funciones siguen trabajando
   - Ajustar tests existentes