-- php<PERSON><PERSON><PERSON><PERSON><PERSON> SQL Dump
-- version 5.1.1deb5ubuntu1
-- https://www.phpmyadmin.net/
--
-- Servidor: localhost:3306
-- Tiempo de generación: 20-05-2025 a las 15:58:13
-- Versión del servidor: 10.6.22-MariaDB-0ubuntu0.22.04.1
-- Versión de PHP: 8.1.32

SET SQL_MODE = "NO_AUTO_VALUE_ON_ZERO";
START TRANSACTION;
SET time_zone = "+00:00";


/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!40101 SET NAMES utf8mb4 */;

--
-- Base de datos: `base_imi`
--

-- --------------------------------------------------------

--
-- Estructura de tabla para la tabla `alumnos`
--

CREATE TABLE `alumnos` (
  `id` int(10) NOT NULL,
  `idplantel` int(10) DEFAULT NULL,
  `foto` varchar(120) DEFAULT NULL,
  `curp` varchar(20) DEFAULT NULL,
  `nombre` varchar(25) DEFAULT NULL,
  `apellido1` varchar(20) DEFAULT NULL,
  `apellido2` varchar(20) NOT NULL,
  `idcurso` int(10) DEFAULT NULL,
  `nivel` int(10) DEFAULT NULL,
  `grupo` varchar(10) DEFAULT NULL,
  `fecha_nacimiento` date DEFAULT NULL,
  `tutor` varchar(50) DEFAULT NULL,
  `colonia` varchar(45) DEFAULT NULL,
  `calle` varchar(45) DEFAULT NULL,
  `localidad` varchar(33) DEFAULT NULL,
  `municipio` varchar(33) DEFAULT NULL,
  `entidad` varchar(15) DEFAULT NULL,
  `telefono_casa` varchar(20) DEFAULT NULL,
  `telefono_oficina` varchar(20) DEFAULT NULL,
  `tutor_mail` varchar(45) DEFAULT NULL,
  `sexo` varchar(1) DEFAULT NULL,
  `idusuario` int(10) DEFAULT NULL,
  `activo` int(11) DEFAULT 1
) ENGINE=InnoDB DEFAULT CHARSET=latin1 COLLATE=latin1_swedish_ci;

--
-- Volcado de datos para la tabla `alumnos`
--

INSERT INTO `alumnos` (`id`, `idplantel`, `foto`, `curp`, `nombre`, `apellido1`, `apellido2`, `idcurso`, `nivel`, `grupo`, `fecha_nacimiento`, `tutor`, `colonia`, `calle`, `localidad`, `municipio`, `entidad`, `telefono_casa`, `telefono_oficina`, `tutor_mail`, `sexo`, `idusuario`, `activo`) VALUES
(17, 1, NULL, NULL, 'Marco Antonio', 'Flores', 'De la Rosa', 4, 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 1),
(18, 1, NULL, NULL, 'Johana', 'Merlo', 'Ramirez', 4, 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 1),
(19, 1, NULL, NULL, 'Maria Fernanda', 'Leon', 'Funes', 4, 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 1),
(20, 1, NULL, NULL, 'Jose Agustin', 'Vazquez', 'Garcia', 4, 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 1),
(21, 1, NULL, NULL, 'Aldo Alejandro', 'Bautista', 'Flores', 4, 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 1),
(22, 1, NULL, NULL, 'Fernando', 'Lezama', 'Garciano', 4, 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 1),
(24, 1, NULL, NULL, 'Hugo', 'Torres', 'Huerta', 4, 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 1),
(25, 1, NULL, NULL, 'Rosa Paola', 'Garcia', 'Sanchez', 4, 2, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 1),
(27, 1, NULL, NULL, 'Luisa Fernanda', 'Ramos', 'Hernandez', 4, 2, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 1),
(28, 1, NULL, NULL, 'Jorge Luis', 'Jacobo', 'Alejo', 4, 2, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 1),
(29, 1, NULL, NULL, 'Victor Manuel', 'Arellano', 'Dominguez', 4, 2, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 1),
(30, 1, NULL, NULL, 'Jorge', 'Tierranueva', 'Cortes', 4, 2, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 1),
(31, 1, NULL, NULL, 'Mariana', 'Gonzalez', 'Cortes', 4, 2, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 1),
(32, 1, NULL, NULL, 'Laura Guadalupe', 'Venancio', 'Garcia', 3, 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 1),
(33, 1, NULL, NULL, 'Eva', 'Ramirez', 'Flores', 3, 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 1),
(34, 1, NULL, NULL, 'Denis Sucet', 'Santamaria', 'Santamaria', 3, 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 1),
(35, 1, NULL, NULL, 'Guadalupe', 'Martinez', 'Reyes', 3, 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 1),
(36, 1, NULL, NULL, 'Celia', 'Ventura', 'Rojas', 3, 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 1),
(37, 1, NULL, NULL, 'Guadalupe', 'Serrano', 'Baez', 3, 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 1),
(38, 1, NULL, NULL, 'Alondra', 'Garcia', 'Rosales', 3, 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 1),
(39, 1, NULL, NULL, 'Lizbeth', 'Flores', 'Medina', 3, 2, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 1),
(40, 1, NULL, NULL, 'Judith Ester', 'Ramos', 'Rojas', 3, 2, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 1),
(41, 1, NULL, NULL, 'Itzel', 'Acevedo', 'Tobon', 3, 2, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 1),
(42, 1, NULL, NULL, 'Lizbeth', 'Perez', 'Huerta', 3, 2, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 1),
(43, 1, NULL, NULL, 'Diana Karen', 'Rosas', 'Flores', 3, 2, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 1),
(44, 1, NULL, NULL, 'Monica Guadalupe', 'Sanchez', 'Lopez', 3, 2, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 1),
(45, 1, NULL, NULL, 'Maria Belen', 'Vargas', 'Rojas', 3, 2, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 1),
(46, 1, NULL, NULL, 'Guadalupe', 'Zapata', 'De Jesus', 3, 2, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 1),
(47, 1, NULL, NULL, 'Jose Marcos', 'Salome', 'Limon', 4, 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 1),
(48, 1, NULL, NULL, 'Cesar', 'Aguilar', 'Moron', 6, 3, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 1),
(50, 1, NULL, NULL, 'Guadalupe', 'Amador', 'Casitllo', 6, 3, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 1),
(51, 1, NULL, NULL, 'Acely Joselin', 'Castillo', 'Cruz', 6, 3, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 1),
(52, 1, NULL, NULL, 'Maria Fernanda', 'Gonzalez', 'Guerra', 6, 3, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 1),
(53, 1, NULL, NULL, 'Jose Luis', 'Hernandez', 'Reyes', 6, 3, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 1),
(54, 1, NULL, NULL, 'Ivan', 'Juarez', 'Jacobo', 6, 3, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 1),
(55, 1, NULL, NULL, 'Ana Karen', 'Leon', 'Mendez', 6, 3, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 1),
(56, 1, NULL, NULL, 'Jesus Hosua', 'Loeza', 'Valencia', 6, 3, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 1),
(57, 1, NULL, NULL, 'Jose Carlos', 'Luis', 'Gonzalez', 6, 3, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 1),
(58, 1, NULL, NULL, 'Jose Emmanuel', 'Ramirez', 'Martinez', 6, 3, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 1),
(59, 1, NULL, NULL, 'Jesus Eduardo', 'Rosales', 'Perez', 6, 3, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 1),
(60, 1, NULL, NULL, 'Areli', 'Santamaria', 'Pita', 6, 3, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 1),
(61, 1, NULL, NULL, 'Carlos Antonio', 'Santos', 'Peralta', 6, 3, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 1),
(62, 1, NULL, NULL, 'Araceli', 'Cerezo', 'Candia', 1, 3, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 1),
(63, 1, NULL, NULL, 'Jonathan', 'Flores', 'Martinez', 1, 3, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 1),
(64, 1, NULL, NULL, 'Valeria', 'Morales', 'Mendoza', 1, 3, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 1),
(65, 1, NULL, NULL, 'Ricardo', 'Nuñez', 'Olivares', 1, 3, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 1),
(66, 1, NULL, NULL, 'Rosa Isela', 'Lopez', 'Ramos', 1, 3, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 1),
(67, 1, NULL, NULL, 'Osvaldo', 'Martinez', 'Cortes', 6, 3, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 1),
(68, 1, NULL, NULL, 'Eduardo', 'Galindo', 'Juarez', 6, 3, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 1),
(69, 1, NULL, NULL, 'Ricardo', 'Mellado', 'Velazquez', 6, 3, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 1),
(70, 1, NULL, NULL, 'Jose Irvin', 'Palacios', 'Galindo', 6, 3, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 1),
(71, 1, NULL, NULL, 'Jhony', 'Hernandez', 'Flores', 6, 3, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 1),
(72, 1, NULL, NULL, 'Camilo', 'Victoriano', 'Rodriguez', 6, 3, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 1),
(73, 1, NULL, NULL, 'Maria Belen', 'Davila', 'Colon', 6, 3, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 1),
(74, 1, NULL, NULL, 'Gabriel', 'Sanchez', 'Espindola', 6, 2, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 1),
(75, 1, NULL, NULL, 'Citlalli', 'Sobal', 'Alonso', 6, 2, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 1),
(76, 1, NULL, NULL, 'Luisa Fernanda', 'Ramos', 'Hernandez', 6, 2, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 1),
(77, 1, NULL, NULL, 'Dulce Michelle', 'Gasca', 'Flores', 6, 2, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 1),
(78, 1, NULL, NULL, 'Alfonso', 'De Ita', 'Lopez', 4, 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 1),
(79, 1, NULL, NULL, 'Diana Paola', 'Cortes', 'Mendez', 4, 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 1),
(80, 1, NULL, NULL, 'Ivan Antonio', 'Rosas', 'Vega', 4, 2, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 1),
(81, 1, NULL, NULL, 'ejemplo', 'apellido_1', 'apellido_2', 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 1),
(82, 1, NULL, NULL, 'JESUS EDUARDO', 'SERRANO', 'TELLEZ', 4, 2, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 1),
(83, 1, NULL, NULL, 'JESSICA ZITLALI', 'HUERTA', 'PEREZ', 4, 2, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 1),
(84, 1, NULL, NULL, 'EDUARDO', 'ROJAS', 'HUERTA', 4, 2, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 1),
(85, 1, NULL, NULL, 'ALAN ISRAEL', 'AVELLEYRA', 'VARGAS', 4, 2, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 1),
(86, 1, NULL, NULL, 'LUZ MARIA', 'MORENO', 'MARTINEZ', 4, 2, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 1),
(87, 1, NULL, NULL, 'VIANEY', 'ROJAS', 'CASELIN', 4, 2, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 1),
(88, 1, NULL, NULL, 'IVONNE', 'HERNANDEZ', 'CENTENO', 4, 2, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 1),
(89, 1, NULL, NULL, 'DULCE VIANEY', 'ZARATE', 'BAEZ', 4, 2, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 1),
(90, 1, NULL, NULL, 'ANGEL ARMANDO', 'HERNANDEZ', 'FELIX', 4, 2, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 1),
(91, 1, NULL, NULL, 'MEYLLI YEYELLI', 'GOMEZ', 'MONTOYA', 4, 2, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 1),
(92, 1, NULL, NULL, 'JOSE IGNACIO', 'VILLANUEVA', 'CARVAJAL', 4, 2, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 1),
(93, 1, NULL, NULL, 'MARIA GUADALUPE', 'LEON', 'SERRANO', 4, 2, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 1),
(94, 1, NULL, NULL, 'ADRIANA', 'ROJAS', 'GONZALEZ', 4, 2, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 1),
(95, 1, NULL, NULL, 'JUAN', 'MORALES', 'SEBASTIAN', 4, 2, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 1);

-- --------------------------------------------------------

--
-- Estructura de tabla para la tabla `cursos`
--

CREATE TABLE `cursos` (
  `id` int(11) NOT NULL,
  `descripcion` varchar(200) DEFAULT NULL,
  `idesquema_pagos` int(11) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_general_ci;

--
-- Volcado de datos para la tabla `cursos`
--

INSERT INTO `cursos` (`id`, `descripcion`, `idesquema_pagos`) VALUES
(1, 'CONTADOR TECNICO CON COMPUTACION', 1),
(2, 'BACHILLERATO GENERAL NO ESCOLARIZADO', 3),
(3, 'ESTILISMO Y DISEÑO DE IMAGEN', 1),
(4, 'BACHILLERATO 3 AÑOS', 1),
(6, 'TECNICO PROFESIONAL EN TEC. DE LA INFORMACION', 1);

-- --------------------------------------------------------

--
-- Estructura de tabla para la tabla `escuela`
--

CREATE TABLE `escuela` (
  `id` int(10) NOT NULL,
  `iniciales` varchar(30) DEFAULT NULL,
  `nombre` varchar(120) DEFAULT NULL,
  `direccion` varchar(120) DEFAULT NULL,
  `escudo` varchar(110) DEFAULT NULL,
  `localidad` varchar(50) DEFAULT NULL,
  `municipio` varchar(50) DEFAULT NULL,
  `entidad` varchar(50) DEFAULT NULL,
  `telefono` varchar(30) DEFAULT NULL,
  `sitio_web` varchar(60) DEFAULT NULL,
  `email` varchar(50) DEFAULT NULL,
  `director` varchar(50) DEFAULT NULL,
  `zona_escolar` varchar(35) DEFAULT NULL,
  `ct` varchar(35) DEFAULT NULL,
  `ciclo_actual` int(10) DEFAULT NULL,
  `reglamento` varchar(110) DEFAULT NULL,
  `eslogan` varchar(150) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=latin1 COLLATE=latin1_swedish_ci;

--
-- Volcado de datos para la tabla `escuela`
--

INSERT INTO `escuela` (`id`, `iniciales`, `nombre`, `direccion`, `escudo`, `localidad`, `municipio`, `entidad`, `telefono`, `sitio_web`, `email`, `director`, `zona_escolar`, `ct`, `ciclo_actual`, `reglamento`, `eslogan`) VALUES
(1, 'IMI', 'I N S T I T U T O  M E X I C A N O  D E  - I N F O R M A T I C A,  A . C.', '4 Sur No. 108 Col. Cenro Tepeaca Puebla', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 20192020, NULL, NULL);

-- --------------------------------------------------------

--
-- Estructura de tabla para la tabla `esquema_pagos`
--

CREATE TABLE `esquema_pagos` (
  `id` int(11) NOT NULL,
  `descripcion` varchar(255) DEFAULT NULL,
  `numero_pagos` int(11) DEFAULT NULL,
  `monto` decimal(18,2) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_general_ci;

--
-- Volcado de datos para la tabla `esquema_pagos`
--

INSERT INTO `esquema_pagos` (`id`, `descripcion`, `numero_pagos`, `monto`) VALUES
(1, 'Pago 52 Semanas', 52, '200.00'),
(2, 'Pago 17 Semanas', 17, '200.00'),
(3, 'Pago 78 Semanas', 78, '200.00');

-- --------------------------------------------------------

--
-- Estructura de tabla para la tabla `folios`
--

CREATE TABLE `folios` (
  `id` int(11) NOT NULL,
  `descripcion` varchar(250) DEFAULT NULL,
  `valor` int(11) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_general_ci;

--
-- Volcado de datos para la tabla `folios`
--

INSERT INTO `folios` (`id`, `descripcion`, `valor`) VALUES
(1, 'recibo', 117);

-- --------------------------------------------------------

--
-- Estructura de tabla para la tabla `kardex_semanal`
--

CREATE TABLE `kardex_semanal` (
  `id` int(11) NOT NULL,
  `id_alumno` int(11) DEFAULT NULL,
  `numpago` int(11) DEFAULT NULL,
  `cantidad_pagada` decimal(18,2) DEFAULT NULL,
  `fecha_limite_pago` date DEFAULT NULL,
  `fecha_pago` date DEFAULT NULL,
  `pago_con_vencimiento` tinyint(4) DEFAULT 0,
  `persona_recibe` int(50) DEFAULT NULL,
  `ciclo` int(11) DEFAULT NULL,
  `referencia` varchar(50) DEFAULT NULL
) ENGINE=MyISAM DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_general_ci;

--
-- Volcado de datos para la tabla `kardex_semanal`
--

INSERT INTO `kardex_semanal` (`id`, `id_alumno`, `numpago`, `cantidad_pagada`, `fecha_limite_pago`, `fecha_pago`, `pago_con_vencimiento`, `persona_recibe`, `ciclo`, `referencia`) VALUES
(316, 81, 4, '200.00', NULL, '2019-09-05', 0, 6, 20192020, NULL),
(309, 17, 12, '200.00', NULL, '2019-09-06', 0, 1, 20192020, NULL),
(310, 17, 13, '200.00', NULL, '2019-09-06', 0, 1, 20192020, NULL),
(303, 80, 3, '200.00', NULL, '2019-09-06', 0, 1, 20192020, NULL),
(315, 81, 3, '200.00', NULL, '2019-09-04', 0, 6, 20192020, NULL),
(304, 80, 4, '200.00', NULL, '2019-09-06', 0, 1, 20192020, NULL),
(308, 17, 11, '200.00', NULL, '2019-09-06', 0, 1, 20192020, NULL),
(307, 17, 10, '200.00', NULL, '2019-09-06', 0, 1, 20192020, NULL),
(305, 80, 5, '200.00', NULL, '2019-09-06', 0, 1, 20192020, NULL),
(314, 81, 2, '200.00', NULL, '2019-09-04', 0, 6, 20192020, NULL),
(313, 81, 1, '200.00', NULL, '2019-09-02', 0, 6, 20192020, NULL),
(312, 17, 15, '200.00', NULL, '2019-09-06', 0, 1, 20192020, NULL),
(306, 80, 6, '200.00', '2019-09-06', '2019-09-06', NULL, NULL, 20192020, NULL),
(311, 17, 14, '200.00', NULL, '2019-09-06', 0, 1, 20192020, NULL),
(302, 80, 2, '200.00', NULL, '2019-09-06', 0, 1, 20192020, NULL),
(300, 51, 1, '200.00', '2019-08-21', '2019-08-21', NULL, NULL, 20192020, NULL),
(298, 38, 20, '100.00', '2019-08-21', '2019-08-21', NULL, NULL, 20192020, NULL),
(295, 17, 7, '200.00', NULL, '2019-09-06', 0, 1, 20192020, NULL),
(294, 17, 6, '200.00', NULL, '2019-09-06', 0, 1, 20192020, NULL),
(292, 17, 4, '200.00', NULL, '2019-09-06', 0, 1, 20192020, NULL),
(301, 80, 1, '200.00', NULL, '2019-09-06', 0, 1, 20192020, NULL),
(299, 38, 10, '100.00', '2019-08-21', '2019-08-21', NULL, NULL, 20192020, NULL),
(297, 17, 9, '200.00', NULL, '2019-09-06', 0, 1, 20192020, NULL),
(296, 17, 8, '200.00', NULL, '2019-09-06', 0, 1, 20192020, NULL),
(293, 17, 5, '200.00', NULL, '2019-09-06', 0, 1, 20192020, NULL),
(288, 17, 1, '200.00', '2019-08-21', '2019-08-21', NULL, NULL, 20192020, NULL),
(291, 17, 3, '200.00', NULL, '2019-09-06', 0, 1, 20192020, NULL),
(289, 17, 1, '200.00', NULL, '2019-09-06', 0, 1, 20192020, NULL),
(290, 17, 2, '200.00', NULL, '2019-09-06', 0, 1, 20192020, NULL),
(317, 81, 5, '200.00', NULL, '2019-09-21', 0, 6, 20192020, 'F.891'),
(318, 81, 6, '200.00', NULL, '2019-10-07', 0, 6, 20192020, ''),
(319, 81, 7, '200.00', NULL, '2020-03-06', 0, 1, 20192020, 'Recib.6789'),
(320, 81, 8, '200.00', NULL, '2020-03-06', 0, 1, 20192020, ''),
(321, 63, 1, '200.00', NULL, '2025-03-31', 0, 6, 20192020, '');

-- --------------------------------------------------------

--
-- Estructura de tabla para la tabla `menus`
--

CREATE TABLE `menus` (
  `id` int(11) NOT NULL,
  `descripcion` varchar(200) DEFAULT NULL,
  `link` varchar(250) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_general_ci;

--
-- Volcado de datos para la tabla `menus`
--

INSERT INTO `menus` (`id`, `descripcion`, `link`) VALUES
(1, 'Alumnos Global', 'alumnos/global'),
(2, 'Alumnos Tepeaca', 'alumnos/alu_tepeaca'),
(3, 'Alumnos Nopalucan', 'alumnos/alu_nopalucan'),
(4, 'Informes Ingresos Global', 'informes/periodog'),
(5, 'Informes Ingresos Tepeaca', 'informes/periodot'),
(6, 'Informe Ingresos Nopalucan', 'informes/periodon'),
(7, 'Kardex Pagos Mantenimiento', 'alumnos/kardex'),
(8, 'Otros Pagos Mantenimiento', 'alumnos/otros_pagos'),
(9, 'Alumnos Inactivos', 'alumnos/inactivos'),
(10, 'Cursos', 'anexos/cursos'),
(11, 'Esquema de Pagos', 'anexos/esquema_pagos'),
(12, 'Pagos Conceptos', 'anexos/pago_conceptos'),
(13, 'Colegio', 'anexos/escuela'),
(14, 'Planteles', 'anexos/planteles'),
(15, 'Folios', 'anexos/folios'),
(16, 'Aumento en +1 todos los niveles', 'anexos/nivel'),
(17, 'Disminuir en -1 todos los nivles', 'anexos/disminuir'),
(18, 'Convertir Alumnos a Inactivos', 'anexos/convertir_inactivos'),
(19, 'Usuarios', 'usuarios/usuario'),
(20, 'Roles', 'usuarios/roles'),
(21, 'Permisos Asignados', 'usuarios/permisos'),
(22, 'Asignacion de Permisos a Roles', 'usuarios/sin_asignacion'),
(23, 'Menus', 'usuarios/menus');

-- --------------------------------------------------------

--
-- Estructura de tabla para la tabla `migrations_ci`
--

CREATE TABLE `migrations_ci` (
  `version` bigint(20) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_general_ci;

--
-- Volcado de datos para la tabla `migrations_ci`
--

INSERT INTO `migrations_ci` (`version`) VALUES
(1);

-- --------------------------------------------------------

--
-- Estructura de tabla para la tabla `pagos`
--

CREATE TABLE `pagos` (
  `id` int(10) NOT NULL,
  `id_alumno` int(10) NOT NULL,
  `fecha` date DEFAULT NULL,
  `id_pago_concepto` int(11) DEFAULT NULL,
  `monto` decimal(18,2) DEFAULT NULL,
  `id_persona_recibe` int(100) DEFAULT NULL,
  `scan_recibo` varchar(150) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=latin1 COLLATE=latin1_swedish_ci;

--
-- Volcado de datos para la tabla `pagos`
--

INSERT INTO `pagos` (`id`, `id_alumno`, `fecha`, `id_pago_concepto`, `monto`, `id_persona_recibe`, `scan_recibo`) VALUES
(1, 21, '2025-03-31', 4, '200.00', NULL, NULL);

-- --------------------------------------------------------

--
-- Estructura de tabla para la tabla `pago_conceptos`
--

CREATE TABLE `pago_conceptos` (
  `id` int(10) NOT NULL,
  `descripcion` varchar(50) DEFAULT NULL,
  `monto` decimal(18,2) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=latin1 COLLATE=latin1_swedish_ci;

--
-- Volcado de datos para la tabla `pago_conceptos`
--

INSERT INTO `pago_conceptos` (`id`, `descripcion`, `monto`) VALUES
(1, 'INSCRIPCIÓN NORMAL', '800.00'),
(2, 'INSCRIPCIÓN POR PROMOCIÓN', '400.00'),
(3, 'CUOTA SEP', '700.00'),
(4, 'MENSUALIDAD NORMAL', '950.00'),
(5, 'MENSUALIDAD POR PROMOCIÓN', '700.00'),
(6, 'INSCRIPCION BACHILLER', '500.00'),
(8, 'DOCUMENTACION  1', '1800.00'),
(9, 'DOCUMENTACION 2', '2000.00'),
(10, 'EXAMEN SEMESTRAL A', '200.00'),
(11, 'EXAMEN SEMESTRAL A ESTILISMO', '200.00'),
(12, 'CUOTA SEP BACHILLER', '700.00'),
(13, 'UNIFORMES 1', '1250.00');

-- --------------------------------------------------------

--
-- Estructura de tabla para la tabla `permisos`
--

CREATE TABLE `permisos` (
  `id` int(11) NOT NULL,
  `menu_id` int(11) DEFAULT NULL,
  `rol_id` int(11) DEFAULT NULL,
  `access` tinyint(4) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_general_ci;

--
-- Volcado de datos para la tabla `permisos`
--

INSERT INTO `permisos` (`id`, `menu_id`, `rol_id`, `access`) VALUES
(1, 1, 1, 1),
(2, 2, 1, 1),
(3, 3, 1, 1),
(4, 4, 1, 1),
(5, 5, 1, 1),
(6, 6, 1, 1),
(7, 7, 1, 1),
(8, 8, 1, 1),
(9, 9, 1, 1),
(10, 10, 1, 1),
(11, 11, 1, 1),
(12, 12, 1, 1),
(13, 13, 1, 1),
(14, 14, 1, 1),
(15, 15, 1, 1),
(16, 16, 1, 1),
(17, 17, 1, 1),
(18, 18, 1, 1),
(19, 19, 1, 1),
(20, 20, 1, 1),
(21, 21, 1, 1),
(22, 22, 1, 1),
(23, 23, 1, 1),
(24, 1, 3, 0),
(25, 2, 3, 0),
(26, 3, 3, 1),
(27, 4, 3, 0),
(28, 5, 3, 0),
(29, 6, 3, 1),
(30, 7, 3, 0),
(31, 8, 3, 0),
(32, 9, 3, 0),
(33, 10, 3, 0),
(34, 11, 3, 0),
(35, 12, 3, 0),
(36, 13, 3, 0),
(37, 14, 3, 0),
(38, 15, 3, 0),
(39, 16, 3, 0),
(40, 17, 3, 0),
(41, 18, 3, 0),
(42, 19, 3, 0),
(43, 20, 3, 0),
(44, 21, 3, 0),
(45, 22, 3, 0),
(46, 23, 3, 0),
(47, 1, 2, 0),
(48, 2, 2, 1),
(49, 3, 2, 0),
(50, 4, 2, 0),
(51, 5, 2, 1),
(52, 6, 2, 0),
(53, 7, 2, 1),
(54, 8, 2, 1),
(55, 9, 2, 0),
(56, 10, 2, 1),
(57, 11, 2, 1),
(58, 12, 2, 1),
(59, 13, 2, 0),
(60, 14, 2, 0),
(61, 15, 2, 0),
(62, 16, 2, 0),
(63, 17, 2, 0),
(64, 18, 2, 0),
(65, 19, 2, 0),
(66, 20, 2, 0),
(67, 21, 2, 0),
(68, 22, 2, 0),
(69, 23, 2, 0);

-- --------------------------------------------------------

--
-- Estructura de tabla para la tabla `planteles`
--

CREATE TABLE `planteles` (
  `id` int(11) NOT NULL,
  `descripcion` varchar(250) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_general_ci;

--
-- Volcado de datos para la tabla `planteles`
--

INSERT INTO `planteles` (`id`, `descripcion`) VALUES
(1, 'Tepeaca'),
(2, 'Nopalucan');

-- --------------------------------------------------------

--
-- Estructura de tabla para la tabla `roles`
--

CREATE TABLE `roles` (
  `id` int(11) NOT NULL,
  `descripcion` varchar(200) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_general_ci;

--
-- Volcado de datos para la tabla `roles`
--

INSERT INTO `roles` (`id`, `descripcion`) VALUES
(1, 'Administrador'),
(2, 'Tepeaca'),
(3, 'Nopalucan');

-- --------------------------------------------------------

--
-- Estructura de tabla para la tabla `salidas_dinero`
--

CREATE TABLE `salidas_dinero` (
  `id_salidas_dinero` int(10) NOT NULL,
  `fecha` date DEFAULT NULL,
  `concepto` varchar(50) DEFAULT NULL,
  `cantidad` decimal(18,2) DEFAULT NULL,
  `id_persona_recibe` int(10) DEFAULT NULL,
  `scan_recibo` varchar(120) DEFAULT NULL,
  `id_escuela` int(10) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=latin1 COLLATE=latin1_swedish_ci;

-- --------------------------------------------------------

--
-- Estructura de tabla para la tabla `usuarios`
--

CREATE TABLE `usuarios` (
  `id` int(11) NOT NULL,
  `nombre` varchar(50) DEFAULT NULL,
  `clave` varchar(25) DEFAULT NULL,
  `descripcion` varchar(100) DEFAULT NULL,
  `rol_id` int(10) DEFAULT NULL,
  `reporta_a` int(10) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=latin1 COLLATE=latin1_swedish_ci;

--
-- Volcado de datos para la tabla `usuarios`
--

INSERT INTO `usuarios` (`id`, `nombre`, `clave`, `descripcion`, `rol_id`, `reporta_a`) VALUES
(1, 'admin', 'fid9294', 'Ing, Fidel Lopez Castillo', 1, NULL),
(3, 'recepcion', 'olga476', 'Sra. Olga  Lidia Barojas Damian', 2, 1),
(4, 'elsy', 'elsa52', 'Elsa Zarate Rojas', 2, 1),
(5, 'contador', 'fercas', 'Prof. Margarito ', 3, NULL),
(6, 'gessa', '19731973', 'Admin. Sistemas', 1, NULL),
(7, 'soco', '2017', 'Socorro Contabilidad', NULL, NULL);

-- --------------------------------------------------------

--
-- Estructura de tabla para la tabla `variables`
--

CREATE TABLE `variables` (
  `id` int(11) NOT NULL,
  `descripcion` int(11) DEFAULT NULL,
  `valor_char` varchar(255) DEFAULT NULL,
  `valor_int` int(11) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_general_ci;

--
-- Índices para tablas volcadas
--

--
-- Indices de la tabla `alumnos`
--
ALTER TABLE `alumnos`
  ADD PRIMARY KEY (`id`);

--
-- Indices de la tabla `cursos`
--
ALTER TABLE `cursos`
  ADD PRIMARY KEY (`id`);

--
-- Indices de la tabla `escuela`
--
ALTER TABLE `escuela`
  ADD PRIMARY KEY (`id`);

--
-- Indices de la tabla `esquema_pagos`
--
ALTER TABLE `esquema_pagos`
  ADD PRIMARY KEY (`id`);

--
-- Indices de la tabla `folios`
--
ALTER TABLE `folios`
  ADD PRIMARY KEY (`id`);

--
-- Indices de la tabla `kardex_semanal`
--
ALTER TABLE `kardex_semanal`
  ADD PRIMARY KEY (`id`);

--
-- Indices de la tabla `menus`
--
ALTER TABLE `menus`
  ADD PRIMARY KEY (`id`);

--
-- Indices de la tabla `pagos`
--
ALTER TABLE `pagos`
  ADD PRIMARY KEY (`id`);

--
-- Indices de la tabla `pago_conceptos`
--
ALTER TABLE `pago_conceptos`
  ADD PRIMARY KEY (`id`);

--
-- Indices de la tabla `permisos`
--
ALTER TABLE `permisos`
  ADD PRIMARY KEY (`id`);

--
-- Indices de la tabla `planteles`
--
ALTER TABLE `planteles`
  ADD PRIMARY KEY (`id`);

--
-- Indices de la tabla `roles`
--
ALTER TABLE `roles`
  ADD PRIMARY KEY (`id`);

--
-- Indices de la tabla `salidas_dinero`
--
ALTER TABLE `salidas_dinero`
  ADD PRIMARY KEY (`id_salidas_dinero`);

--
-- Indices de la tabla `usuarios`
--
ALTER TABLE `usuarios`
  ADD PRIMARY KEY (`id`);

--
-- Indices de la tabla `variables`
--
ALTER TABLE `variables`
  ADD PRIMARY KEY (`id`);

--
-- AUTO_INCREMENT de las tablas volcadas
--

--
-- AUTO_INCREMENT de la tabla `alumnos`
--
ALTER TABLE `alumnos`
  MODIFY `id` int(10) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=96;

--
-- AUTO_INCREMENT de la tabla `cursos`
--
ALTER TABLE `cursos`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=7;

--
-- AUTO_INCREMENT de la tabla `escuela`
--
ALTER TABLE `escuela`
  MODIFY `id` int(10) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=2;

--
-- AUTO_INCREMENT de la tabla `esquema_pagos`
--
ALTER TABLE `esquema_pagos`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=4;

--
-- AUTO_INCREMENT de la tabla `folios`
--
ALTER TABLE `folios`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=2;

--
-- AUTO_INCREMENT de la tabla `kardex_semanal`
--
ALTER TABLE `kardex_semanal`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=322;

--
-- AUTO_INCREMENT de la tabla `menus`
--
ALTER TABLE `menus`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=24;

--
-- AUTO_INCREMENT de la tabla `pagos`
--
ALTER TABLE `pagos`
  MODIFY `id` int(10) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=2;

--
-- AUTO_INCREMENT de la tabla `pago_conceptos`
--
ALTER TABLE `pago_conceptos`
  MODIFY `id` int(10) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=14;

--
-- AUTO_INCREMENT de la tabla `permisos`
--
ALTER TABLE `permisos`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=70;

--
-- AUTO_INCREMENT de la tabla `planteles`
--
ALTER TABLE `planteles`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=3;

--
-- AUTO_INCREMENT de la tabla `roles`
--
ALTER TABLE `roles`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=4;

--
-- AUTO_INCREMENT de la tabla `salidas_dinero`
--
ALTER TABLE `salidas_dinero`
  MODIFY `id_salidas_dinero` int(10) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT de la tabla `usuarios`
--
ALTER TABLE `usuarios`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=8;

--
-- AUTO_INCREMENT de la tabla `variables`
--
ALTER TABLE `variables`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;
COMMIT;

/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
