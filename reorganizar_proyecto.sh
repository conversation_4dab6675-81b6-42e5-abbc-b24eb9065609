
# Script para reorganizar automáticamente la estructura del proyecto Flutter
# Ejecuta este script para crear la nueva estructura y mover los archivos

echo "🚀 Iniciando reorganización del proyecto..."

# Crear estructura de directorios
echo "📂 Creando estructura de directorios..."
mkdir -p lib/features/{\n  auth/{presentation/{pages,widgets},domain/{models,repositories},infrastructure/datasources},\n  dashboard/{presentation/{pages,widgets},domain/{models,repositories},infrastructure/datasources},\n  students/{presentation/{pages,widgets},domain/{models,repositories},infrastructure/datasources},\n  payments/{presentation/{pages,widgets},domain/{models,repositories},infrastructure/datasources}
}

mkdir -p lib/core/{constants,theme,utils,widgets,routing}
mkdir -p lib/shared/{services,providers,models}

# Mover archivos de autenticación
echo "🔑 Moviendo archivos de autenticación..."
mv lib/pages/login_page.dart lib/features/auth/presentation/pages/
mv lib/providers/auth_provider.dart lib/features/auth/providers/

# Mover archivos del dashboard
echo "📊 Moviendo archivos del dashboard..."
mv lib/pages/admin_page.dart lib/features/dashboard/presentation/pages/dashboard_page.dart

# Mover archivos de estudiantes
echo "👥 Moviendo archivos de estudiantes..."
mv lib/pages/student_details_page_widget.dart lib/features/students/presentation/pages/student_details_page.dart

# Mover archivos de pagos
echo "💰 Moviendo archivos de pagos..."
mv lib/pages/new_payment_form_page.dart lib/features/payments/presentation/pages/payment_form_page.dart
mv lib/screens/dashboard_pagos_screen.dart lib/features/payments/presentation/pages/payment_list_page.dart
mv lib/screens/receipt_detail_screen.dart lib/features/payments/presentation/pages/receipt_detail_page.dart

# Mover providers compartidos
echo
