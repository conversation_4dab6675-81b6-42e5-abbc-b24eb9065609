# Refactoring Plan for HistorialRecibos

## Goals
1. Simplify styles by centralizing them
2. Decompose into smaller widgets
3. Improve theme integration

## Steps

### Style Simplification
- Create `ReceiptInfoStyles` class (in `receipt_info_styles.dart`)
- Extract color constants
- Define text theme

### Widget Decomposition
- Create these new widgets (each in their own file):
  - `ReceiptHeaderWidget` (header with icon and title)
  - `ReceiptTileWidget` (the main tile for each receipt)
  - `PaymentDetailItem` (item in the payment details list)
  - `ReceiptStatusBadge` (the badge showing the amount)

### Theme Integration
- Replace hardcoded colors with `Theme.of(context)` equivalents
- Consider creating custom theme extensions if necessary

### File Structure Changes
1. `lib/app/presentation/detalle_alumno/views/recibos_info/historial_recibos.dart` - Main widget (refactored)
2. `lib/app/presentation/detalle_alumno/views/recibos_info/receipt_info_styles.dart` - New file for styles
3. `lib/app/presentation/detalle_alumno/views/recibos_info/widgets/receipt_tile_widget.dart` - New widget file
4. `lib/app/presentation/detalle_alumno/views/recibos_info/widgets/receipt_header_widget.dart` - New widget file
5. `lib/app/presentation/detalle_alumno/views/recibos_info/widgets/payment_detail_item.dart` - New widget file
6. `lib/app/presentation/detalle_alumno/views/recibos_info/widgets/receipt_status_badge.dart` - New widget file

## Implementation Order
1. Create `receipt_info_styles.dart`
2. Refactor `historial_recibos.dart` to use the new styles and start extracting widgets
3. Extract widgets one by one, creating the new files

Note: The `widgets` directory already exists, so we can put the new widgets there.