# Arquitectura Refactorizada para Detalle Alumno (v1.1)

## Capas de Arquitectura

### 1. FirestoreService
- Ubicación: `lib/app/servicios/firestore/firestore_servicio.dart`
- Responsabilidades:
  - Operaciones CRUD genéricas para Firestore
  - Manejo de documentos y subcolecciones
  - Consultas compuestas
- Métodos clave:
  ```dart
  Future<DocumentSnapshot> getDocument(String path);
  Future<QuerySnapshot> getCollection(String path);
  Stream<DocumentSnapshot> documentStream(String path);
  ```

### 2. Repositorios
**AlumnoRepository**
- Ubicación: `lib/app/repositorios/alumno_repositorio.dart`
- Métodos:
  ```dart
  Future<Alumno> getAlumnoById(String id);
  ```

**KardexRepository**
- Ubicación: `lib/app/repositorios/kardex_repository.dart`
- Métodos:
  ```dart
  Future<Kardex> getKardexByAlumnoId(String alumnoId);
  Future<List<KardexSemana>> getSemanas(String alumnoId);
  Future<List<DetallePago>> getDetallesPago(String alumnoId);
  ```

### 3. Controlador
**DetalleAlumnoController**
- Ubicación: `lib/app/modulos/detalle_alumno/controlador/detalle_alumno_controlador.dart`
- Responsabilidades:
  - Coordinar repositorios
  - Manejar estados de carga/error
  - Implementar caché
  - Gestionar persistencia web
- Flujo principal:
  ```dart
  Future<void> loadAlumnoData(String alumnoId) async {
    state = LoadingState();
    try {
      final alumno = await _alumnoRepo.getAlumnoById(alumnoId);
      final kardex = await _kardexRepo.getKardexByAlumnoId(alumnoId);
      final semanas = await _kardexRepo.getSemanas(alumnoId);
      final detalles = await _kardexRepo.getDetallesPago(alumnoId);
      
      final data = AlumnoFullData(alumno, kardex, semanas, detalles);
      _saveToCache(alumnoId, data);
      state = LoadedState(data);
    } catch (e) {
      state = ErrorState(e.toString());
    }
  }
  ```

### 4. Estado
**DetalleAlumnoState**
- Ubicación: `lib/app/modulos/detalle_alumno/controlador/detalle_alumno_estado.dart`
- Estados:
  ```dart
  abstract class DetalleAlumnoState {}
  class InitialState extends DetalleAlumnoState {}
  class LoadingState extends DetalleAlumnoState {}
  class LoadedState extends DetalleAlumnoState {
    final AlumnoFullData data;
    LoadedState(this.data);
  }
  class ErrorState extends DetalleAlumnoState {
    final String message;
    ErrorState(this.message);
  }
  ```

## Persistencia Web
```mermaid
graph LR
  A[Recarga de página] --> B{Existe en caché?}
  B -->|Sí| C[Usar datos cacheados]
  B -->|No| D[Buscar en sessionStorage]
  D -->|Existe| E[Parsear y cargar]
  D -->|No existe| F[Fetch desde Firestore]
  F --> G[Guardar en caché y sessionStorage]
```

## Checklist de Implementación
- [ ] Crear KardexRepository
- [ ] Implementar DetalleAlumnoController
- [ ] Definir estados DetalleAlumnoState
- [ ] Refactorizar UI para usar controlador
- [ ] Implementar caché en memoria
- [ ] Agregar persistencia web con sessionStorage
- [ ] Actualizar navegación a rutas ID-based

## Secuencia de Ejecución
```mermaid
sequenceDiagram
  participant UI as Vista
  participant Controller
  participant RepoAlumno
  participant RepoKardex
  participant Firestore

  UI->>Controller: initState(alumnoId)
  Controller->>Controller: checkCache(alumnoId)
  alt Datos en caché
    Controller-->>UI: show cached data
  else Sin caché
    Controller->>RepoAlumno: getAlumnoById(alumnoId)
    RepoAlumno->>Firestore: getDocument('alumnos/$id')
    Firestore-->>RepoAlumno: alumnoData
    Controller->>RepoKardex: getKardexData(alumnoId)
    RepoKardex->>Firestore: getDocument('kardex/$id')
    RepoKardex->>Firestore: getSubcollection('semanas')
    RepoKardex->>Firestore: getSubcollection('detalles_pago')
    Firestore-->>RepoKardex: kardexData
    RepoKardex-->>Controller: combinedData
    Controller->>Controller: saveToCache(alumnoId, data)
    Controller-->>UI: updateState(LoadedState)
  end