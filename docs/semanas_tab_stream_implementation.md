# Semanas Tab Stream Implementation Plan

## Objective
Implement real-time updates for Kardex weeks using streams and GetX, ensuring:
1. Weeks are automatically updated without manual refresh
2. Pending weeks appear first in yellow
3. Maintain current functionality while adding reactivity

## Architecture
```mermaid
graph TD
    A[KardexRepository] -->|Add| B[Stream<List<KardexSemana>> watchSemanas]
    B --> C[DatabaseService watchCollection]
    D[DetalleAlumnoController] -->|Add| E[RxList<KardexSemana> semanas]
    D -->|Add| F[void setupSemanasStream]
    G[SemanasTab] -->|Update| H[Use Obx with controller.semanas]
    H --> I[Reactive sorting: pending weeks first]
```

## Implementation Steps

### 1. Update KardexRepository Interface
Add stream method to the repository contract

### 2. Implement Stream in KardexRepositoryImpl
Create concrete stream implementation using DatabaseService

### 3. Add watchCollection to DatabaseService
Implement real-time collection watching functionality

### 4. Update DetalleAlumnoController
- Add reactive semanas list
- Implement stream setup
- Handle subscription lifecycle

### 5. Modify SemanasTab Widget
- Convert to use controller's reactive list
- Implement Obx for automatic updates
- Remove static semana list parameter

### 6. Update Sorting Logic
Replace manual filtering with reactive sorting that:
1. Puts pending weeks first
2. Maintains numerical order within groups
3. Handles null weeks gracefully

### 7. Integrate with Parent Widget
Update AlumnoDetallePage to pass controller instead of static list

## Code Changes

### File: `lib/domain/repositories/kardex_repository.dart`
```dart
abstract class KardexRepository {
  // ... existing methods ...
  
  /// Real-time stream of kardex weeks
  Stream<List<KardexSemana>> watchSemanas(String alumnoId);
}
```

### File: `lib/data/repositories/kardex_repository_impl.dart`
```dart
class KardexRepositoryImpl implements KardexRepository {
  // ... existing code ...

  @override
  Stream<List<KardexSemana>> watchSemanas(String alumnoId) {
    return _databaseService.watchCollection(
      path: 'kardex/$alumnoId/semanas',
    ).map((snapshots) => snapshots
        .map(KardexSemana.fromMap)
        .toList());
  }
}
```

### File: `lib/servicios/database_service.dart`
```dart
abstract class DatabaseService {
  // ... existing methods ...
  
  /// Real-time collection stream
  Stream<List<Map<String, dynamic>>> watchCollection({
    required String path,
    Query Function(Query query)? queryBuilder,
    int Function(Map<String, dynamic>, Map<String, dynamic>)? compare,
  });
}
```

### File: `lib/presentation/alumno_detalle/controller/detalle_alumno.controller.dart`
```dart
class DetalleAlumnoController extends GetxController {
  // ... existing properties ...
  final semanas = <KardexSemana>[].obs;
  StreamSubscription? _semanasSubscription;

  @override
  void onInit() {
    super.onInit();
    _setupSemanasStream();
  }

  void _setupSemanasStream() {
    _semanasSubscription = kardexRepository
        .watchSemanas(alumnoId)
        .listen((semanasList) {
          semanas.value = semanasList;
        });
  }

  @override
  void onClose() {
    _semanasSubscription?.cancel();
    super.onClose();
  }
}
```

### File: `lib/presentation/alumno_detalle/views/semanas_tab/semanas_tab.dart`
```dart
class SemanasTab extends StatelessWidget {
  final DetalleAlumnoController controller;
  final int totalSemanas;
  
  const SemanasTab({
    super.key, 
    required this.controller,
    required this.totalSemanas
  });

  @override
  Widget build(BuildContext context) {
    return Obx(() {
      final semanas = controller.semanas;
      
      // Create a map for quick lookup
      final weekMap = {for (var s in semanas) s.numeroSemana: s};
      
      // Create ordered list: pending weeks first, then others
      final pendingWeeks = semanas
          .where((s) => s.estado != 'pagado')
          .map((s) => s.numeroSemana)
          .toList()
        ..sort();
      
      final otherWeeks = List.generate(totalSemanas, (i) => i + 1)
          .where((weekNum) => !pendingWeeks.contains(weekNum))
          .toList();
      
      final orderedWeeks = [...pendingWeeks, ...otherWeeks];
      
      // ... rest of build method unchanged ...
    });
  }
}
```

### File: `lib/presentation/alumno_detalle/alumno_detalle.page.dart`
```dart
class AlumnoDetallePage extends StatelessWidget {
  // ... 
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      // ...
      body: TabBarView(
        children: [
          // ...
          SemanasTab(
            controller: controller,
            totalSemanas: controller.alumno.value?.totalSemanas ?? 0,
          ),
          // ...
        ],
      ),
    );
  }
}
```

## Considerations
1. Ensure DatabaseService implementation (Firestore) supports watchCollection
2. Handle stream errors appropriately
3. Maintain current week coloring logic
4. Preserve onTap functionality for week details
5. Optimize sorting performance for large week lists