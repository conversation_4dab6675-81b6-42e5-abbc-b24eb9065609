# Info Tab Stream Implementation Plan

## Objective
Implement real-time updates for Kardex and additional costs data in the info_tab view using streams and GetX, ensuring:
1. Changes to kardex data (paid amount, new weeks) are reflected instantly
2. Additional cost updates appear without page reload
3. Maintain current functionality while adding reactivity

## Architecture
```mermaid
graph TD
    A[KardexRepository] -->|Add| B[Stream<models.Kardex> watchKardex]
    A -->|Add| C[Stream<List<CostoAdicional>> watchCostosAdicionales]
    D[DatabaseService] -->|Extend| E[watchDocument method]
    F[DetalleAlumnoController] -->|Add| G[Rx<models.Kardex?> kardex]
    F -->|Add| H[RxList<CostoAdicional> costosAdicionales]
    I[StudentInfoCard] -->|Update| J[Use Obx with controller reactive properties]
```

## Implementation Steps

### 1. Update Repository Interfaces
Add stream methods to KardexRepository:
```dart
abstract class KardexRepository {
  // Existing methods...
  
  /// Real-time stream of kardex data
  Stream<models.Kardex> watchKardex(String alumnoId);
  
  /// Real-time stream of additional costs
  Stream<List<CostoAdicional>> watchCostosAdicionales(String alumnoId);
}
```

### 2. Implement Repository Streams
Update KardexRepositoryImpl:
```dart
class KardexRepositoryImpl implements KardexRepository {
  // Existing code...

  @override
  Stream<models.Kardex> watchKardex(String alumnoId) {
    return _databaseService.watchDocument('kardex/$alumnoId')
        .map((snapshot) => models.Kardex.fromMap(snapshot.data()!));
  }

  @override
  Stream<List<CostoAdicional>> watchCostosAdicionales(String alumnoId) {
    return _databaseService.watchCollection('alumnos/$alumnoId/costos_adicionales')
        .map((snapshots) => snapshots.map(CostoAdicional.fromMap).toList());
  }
}
```

### 3. Update DetalleAlumnoController
Add reactive properties and stream handling:
```dart
class DetalleAlumnoController extends GetxController {
  // Existing properties...
  final kardex = Rx<models.Kardex?>(null);
  final costosAdicionales = RxList<CostoAdicional>();
  StreamSubscription? _kardexSubscription;
  StreamSubscription? _costosSubscription;

  @override
  void onInit() {
    super.onInit();
    _setupKardexStream();
    _setupCostosStream();
  }

  void _setupKardexStream() {
    _kardexSubscription = kardexRepository
        .watchKardex(alumnoId)
        .listen((k) => kardex.value = k);
  }

  void _setupCostosStream() {
    _costosSubscription = kardexRepository
        .watchCostosAdicionales(alumnoId)
        .listen((c) => costosAdicionales.value = c);
  }

  @override
  void onClose() {
    _kardexSubscription?.cancel();
    _costosSubscription?.cancel();
    super.onClose();
  }
}
```

### 4. Refactor StudentInfoCard Widget
Convert to use reactive controller data:
```dart
class StudentInfoCard extends StatelessWidget {
  final DetalleAlumnoController controller;
  
  const StudentInfoCard({
    Key? key,
    required this.controller,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Obx(() {
      final alumno = controller.alumno.value;
      final kardex = controller.kardex.value;
      final costosAdicionales = controller.costosAdicionales;
      
      return Container(
        child: Column(
          children: [
            StudentHeader(alumno: alumno),
            
            // Personal Information section remains unchanged
            
            // Kardex Information
            if (kardex == null) ...,
            else SectionContainer(
              title: 'Información de Kardex',
              icon: Icons.school_outlined,
              child: KardexSection(
                kardex: kardex,
                semanasPagadas: controller.semanas, // Already reactive
              ),
            ),

            // Additional Costs
            if (costosAdicionales.isNotEmpty) ...,
            
            // Payment Summary
            PaymentSummary(
              totalPagado: kardex?.totalPagado ?? 0.0,
              totalPendiente: kardex?.saldoPendiente ?? 0.0,
            ),
          ],
        ),
      );
    });
  }
}
```

### 5. Integrate with Parent Widget
Update AlumnoDetallePage:
```dart
class AlumnoDetallePage extends StatelessWidget {
  // ...
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      // ...
      body: TabBarView(
        children: [
          // ...
          InfoTab(controller: controller), // Pass controller
          // ...
        ],
      ),
    );
  }
}
```

## Key Implementation Details
1. **Data Relationships**: Kardex changes automatically trigger UI updates
2. **Efficiency**: Only affected widgets rebuild when data changes
3. **Error Handling**: Add error handlers to stream subscriptions
4. **Initial State**: Handle null values during initial load
5. **Combined Updates**: Use Rx.combineLatest for coordinated updates

## Testing Strategy
1. Verify UI updates when kardex data changes
2. Test additional costs updates
3. Validate subscription cleanup
4. Check error scenarios