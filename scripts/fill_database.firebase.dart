// Save as upload_students.dart in your project root
// Run with: dart run upload_students.dart

import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_core/firebase_core.dart';
import 'package:gemini_app/firebase_options.dart';
import 'package:intl/intl.dart';

void main() async {
  await Firebase.initializeApp(
    options: DefaultFirebaseOptions.currentPlatform,
  );

  // Initialize Firestore
  final firestore = FirebaseFirestore.instance;

  // Student data with names split into components
  final students = [
    // Format: [nombre, apellidoPaterno, apellidoMaterno]
    ['ANTONIO ALBERTO', 'MARTINEZ', 'CORTES'],
    ['MA. FERNANDA', 'FLORES', 'RIVERA'],
    ['EDUARDO', 'RODRIGUEZ', 'TURRUBIATES'],
    ['ERIKA', 'LIMONNFLORES', null],
    ['CLAUDIA EDITH', 'LEAL', 'ROMERO'],
    ['<PERSON><PERSON><PERSON><PERSON>', 'FLORES', '<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>'],
    ['<PERSON><PERSON><PERSON><PERSON>', '<PERSON>ER<PERSON><PERSON>E<PERSON>', '<PERSON>ER<PERSON><PERSON>'],
    ['JESUS BRANDON', '<PERSON><PERSON>E<PERSON>', 'CA<PERSON><PERSON><PERSON>EDA'],
    ['MARCO ANTONIO', 'SALAMIHUA', 'FLORES'],
    ['ROGELIO', 'ROMERO', 'CALIXTO'],
    ['RAUL', 'VELAZQUEZ', 'HERNANDEZ'],
    ['ANA VALERIA', 'GUEVARA', 'MARIA'],
    ['JOSE ALBERTO', 'CASTILLO', 'SANCHEZ'],
    ['FERNANDO', 'CAZARES', 'SANCHEZ'],
    ['DANIEL', 'DONOSO', 'CELIS'],
    ['VIANNEY', 'GIL', 'LUIS'],
    ['JUANCARLOS', 'LOPEZ', 'OSORIO'],
    ['BENJAMIN', 'MORALES', 'TELLEZ'],
    ['FELIX', 'PALACIOS', 'RIVERA'],
    ['NANCY', 'VELAZQUEZ', 'IGNACIO'],
    ['EMMANUEL', 'VENANCIO', 'PADUA'],
    ['RICARDO CUAUHTEMOC', 'VAZQUEZ', 'MORALES'],
    ['JORGE', 'HERNANDEZ', 'VELAZQUEZ'],
    ['ARTURO', 'MARTINEZ', 'CESPEDES'],
    ['YADIRA CINCEPCION', 'MORALES', 'CONTRERAS'],
    ['YARELY', 'OCAMPO', 'MONTOYA'],
    ['JULISSA NATALIA', 'SOTO', 'OJEDA'],
    ['SERGIO', 'MORALES', 'TELLEZ'],
    ['JOSELINNE', 'HERNANDEZ', 'JIMENEZ'],
    ['MARCO', 'ISLA', 'PEREZ'],
    ['GLORIA', 'JUAREZ', 'HUERTA'],
    ['CARLOS', 'MENESES', 'CAMACHO'],
    ['DAIRAN', 'MITRE', 'ROJAS'],
    ['GELACIO', 'MONTOYA', 'MORALES'],
    ['ARIANA', 'NUÑEZ', 'ALCANTARA'],
    ['ADRIANA', 'PEREZ', 'ORTIZ'],
    ['VIRGINIA', 'ROJAS', 'CABRERA'],
    ['RODRIGO GUSTAVO', 'ROSAS', 'SANCHEZ'],
    ['FLOYLAN', 'MENDEZ', 'MENDEZ'],
    ['CARLOS NARCISO', 'GONZALEZ', 'SOTO'],
    ['EMANUEL', 'DIAZ', 'CEFERINO'],
    ['ROSARIO', 'ROSAS', 'PEREZ'],
    ['LUIS ENRIQUE', 'ROMERO', 'ROSAS'],
    ['ISM AEL', 'COTO', 'CASELIN'],
  ];

  final batch = firestore.batch();
  final studentsRef = firestore.collection('alumnos');
  final now = DateTime.now();
  final dateFormat = DateFormat('yyyyMMddHHmmss');

  print('Starting to upload ${students.length} students...');

  for (var i = 0; i < students.length; i++) {
    final student = students[i];
    final nombre = student[0];
    final apellidoPaterno = student[1];
    final apellidoMaterno = student[2];

    // Generate a unique ID for each student
    final studentId = 'BGNE-${dateFormat.format(now)}-${i.toString().padLeft(3, '0')}';

    // Create student data
    final studentData = {
      'id': studentId,
      'nombre': nombre,
      'apellidoPaterno': apellidoPaterno,
      if (apellidoMaterno != null) 'apellidoMaterno': apellidoMaterno,
      'carrera': 'BGNE',
      'semestre': 0,
      'email': 'demo$<EMAIL>',
      'fechaRegistro': Timestamp.now(),
      'activo': true,
    };

    // Add to batch
    batch.set(studentsRef.doc(studentId), studentData);
  }

  try {
    // Commit the batch
    await batch.commit();
    print('\n✅ Successfully uploaded ${students.length} students to Firestore!');
  } catch (e) {
    print('\n❌ Error uploading students: $e');
  }
}
