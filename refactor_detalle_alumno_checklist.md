# Checklist Refactorización DetalleAlumno

- [ ] Completar `lib/app/presentation/detalle_alumno/detalle_alumno.state.dart`
- [ ] Actualizar imports en controlador y página
- [ ] Extraer componente `InfoTab` a `widgets/info_tab/info_tab.dart`
- [ ] Extraer componente `PagosTab` a `widgets/pagos_tab/pagos_tab.dart`
- [ ] Extraer componente `SemanasTab` a `widgets/semanas_tab/semanas_tab.dart`
- [ ] Refactorizar estilos:
   - [ ] Mover estilos comunes a `app_styles.dart`
   - [ ] Dividir `detalle_alumno.styles.dart` en módulos temáticos
- [ ] Optimizaciones de rendimiento:
   - [ ] Añadir `const` a widgets estáticos
   - [ ] Implementar `AutomaticKeepAliveClientMixin` en pestañas
   - [ ] Usar `ListView.builder` para listas dinámicas