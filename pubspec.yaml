name: gemini_app
description: 'A new Flutter project.'
publish_to: 'none'
version: 0.1.0

environment:
  sdk: ^3.7.2

dependencies:
  beamer: ^1.7.0
  dio: ^5.8.0+1
  flutter:
    sdk: flutter
  flutter_chat_types: ^3.6.2
  flutter_chat_ui: ^1.6.15
  flutter_dotenv: ^5.2.1
  flutter_riverpod: ^2.6.1
  get: ^4.7.2
  get_it: ^7.6.4
  google_fonts: ^6.1.0
  google_maps_flutter: ^2.5.3
  image_picker: ^1.1.2
  riverpod_annotation: ^2.6.1
  universal_html: ^2.2.4
  url_launcher: ^6.2.4
  uuid: ^4.5.1
  widgets_to_image: ^1.0.0
  provider: ^6.1.2
  firebase_auth: ^5.4.0
  firebase_core: ^3.10.0
  cloud_firestore: ^5.6.1
  data_table_2: ^2.5.10
  responsive_framework: ^1.5.1
  after_layout: ^1.2.0
  permission_handler: ^10.0.0
  share_plus: ^7.0.0

dev_dependencies:
  build_runner: ^2.4.15
  custom_lint: ^0.7.5
  flutter_lints: ^5.0.0
  flutter_test:
    sdk: flutter
  riverpod_generator: ^2.6.5
  riverpod_lint: ^2.6.5

flutter:
  uses-material-design: true
  assets:
    - assets/
    - .env
