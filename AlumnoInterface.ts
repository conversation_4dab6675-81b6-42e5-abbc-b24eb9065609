interface Alumno {
  id: string;
  idPlantel: string;
  nombre: string;
  apellido1: string;
  apellido2: string;
  curp?: string;
  fechaNacimiento?: Date;
  sexo?: string;
  tutor?: string;
  telefonoCasa?: string;
  telefonoOficina?: string;
  tutorMail?: string;
  direccion?: {
    colonia?: string;
    calle?: string;
    localidad?: string;
    municipio?: string;
    entidad?: string;
  };
  foto?: string;
  activo: boolean;
  createdAt: Timestamp;
  updatedAt: Timestamp;
}